<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePartnersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('partners', function (Blueprint $table) {
            $table->id();
            $table->string('partner_name',255);
            $table->integer('partner_category_id');
            $table->string('logo',255)->default(null);
            $table->text('partner_description');
            $table->text('reason_well');
            $table->text('terms_and_conditions');
            $table->text('redemeption_process');
            $table->string('slug',255);
            $table->string('partner_code',255)->default(null);
            $table->text('short_desc',255)->default(null);
            $table->text('status')->comment('1 for active, 2 for inactive, 3 for softdelete');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('partners');
    }
}
