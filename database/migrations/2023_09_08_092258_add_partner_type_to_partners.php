<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPartnerTypeToPartners extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('partners', function (Blueprint $table) {
            $table->tinyInteger('partner_type')->default(1)->comment('1 for static gift code, 2 for dynamic gift code')->after("partner_category_id");
            $table->tinyInteger('denomination_type')->default(0)->comment('0 for fixed values, 1 for open values')->after("partner_type");
            $table->integer('tillo_category_id')->default(null)->after("denomination_type");
            $table->index(['partner_type', 'denomination_type','tillo_category_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('partners', function (Blueprint $table) {
            $table->dropColumn(['partner_type', 'denomination_type']);
        });
    }
}
