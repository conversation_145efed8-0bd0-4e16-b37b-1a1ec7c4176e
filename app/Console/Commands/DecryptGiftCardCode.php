<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GiftValues;
use App\Services\EncryptDecrypt;

class DecryptGiftCardCode extends Command
{
    protected $signature = 'decrypt:code {gift_card_code : The gift card code to decrypt}';

    protected $description = 'Decrypt a specific gift card code';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        // Get the gift card code from the command argument
        $gift_card_code = $this->argument('gift_card_code');

        // Instantiate EncryptDecrypt service
        $cc = new EncryptDecrypt;

        // Decrypt the gift card code
        $gift_card_code_decrypted = $cc->codeDecrypt($gift_card_code);

        // Output the decrypted gift card code
        $this->info("Decrypted value: $gift_card_code_decrypted");
    }
}

