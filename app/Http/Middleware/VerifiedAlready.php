<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use Illuminate\Support\Facades\Log; // Import the Log class from the correct namespace

class VerifiedAlready
{
    /**
     * Middleware to validate the veryfy email Link.
     *
     * @var array
     */
    public function handle($request, Closure $next)
    {
        $url = request()->segments(0);
        // Get the user object from the user ID
        $user = User::find($url[1]);

        if ($user !== null && $user->hasVerifiedEmail()) {
            return redirect('/already-verified')->with('message', 'Your email is already verified.');
        }

        return $next($request);
    }
}

