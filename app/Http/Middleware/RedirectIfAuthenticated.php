<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  ...$guards
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$guards)
    {
        //die('redirct');
        $guards = empty($guards) ? [null] : $guards;

   
        $error = 0;
        $redirect_url = '';
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {

                $user = Auth::user();
                $role =  $user->roles()->select('title')->get();
              
                if(!$role->isEmpty())
                {
                    $accountRole = $role[0]->title;

                    if($accountRole == 'admin' && ! $request->expectsJson()){
                        $error = 1;
                        $redirect_url = redirect(RouteServiceProvider::HOME);
                           
                    }
                    elseif($accountRole == 'user' && ! $request->expectsJson()){
                        $error = 1;
                        $redirect_url = redirect('/');
                       
                    }
                } else {
                    if (! $request->expectsJson()) {
                        $error = 1;
                        $redirect_url = redirect('/');
                    }
                }
            }
        }  
        if($error==1)
        {
            return $redirect_url;
        }     

        return $next($request);
    }
}
