<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Http\Requests\Admin\AddTagRequest;
use Illuminate\Support\Facades\Storage;
use App\Models\Tags as TagsModel;
use App\Models\PartnerTags as PartnerTagsModel;
use DB;
use Redirect;

class TagsController extends BaseController
{   
    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = 13;
    }
    // this handles the view screen of all Tags 
    public function index()
    {   
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('tag.home') ], ['link' => "javascript:void(0)", 'name' => __('tag.tags')], ['name' =>  __('tag.manage_tags')]
          ];
        $tag_data = TagsModel::orderBy('id','desc')->get();
        return view('/admin/pages/tag/manage-tags', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'tag_data'=> $tag_data
          ]);
        return view('/admin/pages/tag/manage-tags');
    }
    // this handles the view screen of add Tag
    public function addTag()
    {
        $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('tag.home') ], ['link' => "/admin/manage-tags", 'name' => __('tag.tags')], ['name' =>  __('tag.add-tag')]
        ];
       
         return view('/admin/pages/tag/add-tags', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu
          ]);
    }
    // this handles the create Tag
    public function createTag(AddTagRequest $request)
    {
      $tagModel = TagsModel::create($request->all());
      $request->session()->flash('created', __('tag.added_success'));
      return redirect('admin/manage-tags');
    }

    // this handles the edit screen of Tag
    public function editTag($id)
    { 
      $tag_data = TagsModel::where('id',$id)->first();
      $breadcrumbs = [
        ['link' => "/admin/dashboard", 'name' =>   __('tag.home') ], ['link' => "/admin/manage-tags", 'name' => __('tag.tags')], ['name' =>  __('tag.edit-tag')]
      ];
      return view('/admin/pages/tag/edit-tags', [
        'breadcrumbs' => $breadcrumbs,
        'active_menu' => $this->active_menu,
        'tag'=> $tag_data
      ]);
    }

    // this handles the update process of Tag
    public function updateTag(AddTagRequest $request)
    {
      $request_data = $request->all();
      $id = $request_data['id'];
     
      $tag_update = TagsModel::find($id);
      $tag_update->update(['tag' => $request->input('tag')]);
      $request->session()->flash('created', __('tag.update_success'));
      return redirect('admin/manage-tags');
    }

    // this handles the delete of Tag
    public function deleteTag(Request $request)
    {
      $request_data = $request->all();
      $tag_id = $request_data['tag_id'];
      $result = PartnerTagsModel::where('tag_id',$tag_id)->get();
      if(isset($result[0])){
        $request->session()->flash('error_tag', __('tag.error_tag'));
        return false;
      }
      TagsModel::where('id',$tag_id)->delete();
      return true;
    }
}
