<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Models\EmailTemplates as EmailTemplatesModel;
use App\Http\Requests\Admin\EmailTemplateRequest;
class EmailTemplates extends BaseController
{
    //
    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = 8;
    }
    // this handles the manage email templates
    public function index()
    {
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "javascript:void(0)", 'name' => __('email_template.email_template')], ['name' =>  __('email_template.manage_email_template')]
          ];
          $email_templates_data = EmailTemplatesModel::where('status','!=',3)->orderBy('id','desc')->get();

          return view('/admin/pages/email-templates/manage-email-templates', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'email_templates_data' => $email_templates_data
          ]);
    }

    // this handles the incoming of add email template
    public function addEmailTemplate()
    {
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/manage-email-templates", 'name' => __('email_template.email_template')], ['name' =>  __('email_template.manage_email_template')]
          ];
          $email_templates_data = EmailTemplatesModel::orderBy('id','desc')->get();

          return view('/admin/pages/email-templates/add-email-templates', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'email_templates_data' => $email_templates_data
          ]);
    }

    // this handles the update screen of email template
    public function editEmailTemplate($id='')
    {
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/manage-email-templates", 'name' => __('email_template.email_template')], ['name' =>  __('email_template.edit_email_template')]
          ];
          $email_templates_data = EmailTemplatesModel::where('id',$id)->first();

          return view('/admin/pages/email-templates/edit-email-templates', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'email_templates_data' => $email_templates_data
          ]);
    }

  // this handles the update of email template
    public function updateEmailTemplate(EmailTemplateRequest $request)
    {
        $request_data = $request->all();
        $id = $request_data['id'];
        $email_template = EmailTemplatesModel::find($id);
        $email_data = [    
            'email_type' => $request->input('email_type'),
            'template' => $request->input('template'),
          ];
          $email_template->update($email_data);
          $request->session()->flash('created', __('email_template.update_success'));
          return redirect('admin/manage-email-templates');
    }

    // this handles the save of email template
    public function saveEmailTemplate(EmailTemplateRequest $request)
    {
        $email_template = new EmailTemplatesModel;
        $email_template->email_type = $request->input('email_type');
        $email_template->template = $request->input('template');
        $email_template->status = 1;
     
        $email_template->save();
        
        $request->session()->flash('created', __('email_template.added_success'));
        return redirect('admin/manage-email-templates');
    }

    // this handles the delete of email template
    public function deleteEmailTemplate(Request $request)
    {
       $request_data = $request->all();
       $email_id = $request_data['email_id'];
       EmailTemplatesModel::where('id',$email_id)->update(['status'=>3]);
       return true;
    }

}
