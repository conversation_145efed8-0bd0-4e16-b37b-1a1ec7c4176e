<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use App\Http\Requests\Admin\AddProfileRequest;
use App\Models\User;
use Hash;
use Auth;
use Validator;

class AdminController extends BaseController
{

    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = '';
    }

    /**
     * Display the Change Password view.
     *
     * @return \Illuminate\View\View
     */
    
   
    public function changePasswordShow()
    {       
         $breadcrumbs = [
            ['link' => "/", 'name' => "Home"], ['link' => "javascript:void(0)", 'name' => "Account Settings"], ['name' => "Change Password"]
          ];
         
          return view('/admin/pages/change-password', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu
          ]);
    }

    /**
     * Handle an incoming change password request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     */
    
    public function changePasswordStore(Request $request)
    {
        if($request->isMethod('POST'))
        { 
            $user = Auth::user();
            $rules = array(
                'password' => 'required',
                'newPassword' => 'required|min:8|confirmed|regex:/^(?=.*[a-z])(?=.*[A-Z]).+$/'
            );

            $validator = Validator::make($request->all(), $rules);

            if($validator->fails())
            {
                return Redirect::route('changePassword')->withErrors($validator);
            }
            else
            {
                if (!Hash::check($request->password, $user->password))
                {
                    $errors['password'] = 'Your Current password does not match!';
                    return Redirect::route('changePassword')->withErrors($errors);
                }
                else
                {
                    $user->password = Hash::make($request->newPassword);                    
                    $success = $user->save();
                    $status = 'Password Changed successfully!';
                    $error = 'Password not changed successfully. Please try again later!';

                    // If the password was successfully changed, we will redirect the user back to
                    // the change Password view. If there is an error we can
                    // redirect them back to where they came from with their error message.
                    return $success ? back()->with('status', __($status)) : back()->with('failure', __($error));
                }
            
            }
        }        
    }

    /**
     * Display the Profile view.
     *
     * @return \Illuminate\View\View
     */

    public function profile()
    {
        $breadcrumbs = [
            ['link' => "/", 'name' => "Home"], ['link' => "javascript:void(0)", 'name' => "Account Settings"], ['name' => "Account"]
          ];

        $currentUser = Auth::user();
        $userData = User::where('id',$currentUser->id)->first();
        return view('admin.pages.profile', [
            'user' => $userData,
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu
        ]);
    }

    /**
     * Handle an incoming Profile Update request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     */

    public function profileStore(AddProfileRequest $request)
    {
        $id = Auth::user()->id;
        $user_profile_update = User::find($id);
        $user_profile_update->first_name = $request->input('first_name');
        $user_profile_update->last_name = $request->input('last_name');
        $user_profile_update->email = $request->input('email');
        $user_profile_update->save();

        $request->session()->flash('created', 'Profile updated Successfully');
        return back();
    }
}


