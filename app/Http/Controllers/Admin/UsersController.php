<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\Admin\EditUserRequest;
use DB;

class UsersController extends Controller
{
    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = 1;
    }
    // this handles the view screen of users
    public function index()
    {
         $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('user.home') ], ['link' => "javascript:void(0)", 'name' => __('user.user')], ['name' =>  __('user.manage_user')]
          ];
          $user_data = User::orderBy('id','desc')->get();

          return view('/admin/pages/user/manage-users', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'user_data' => $user_data
          ]);
    }
    // this handles the view screen of user data
    public function viewUser($id)
    {  
      $user = User::where('id',$id)->first();
      
      $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('user.home') ], ['link' => "/admin/manage-users", 'name' => __('user.user')], ['name' =>  __('user.view_user')]
       ];
      
       return view('/admin/pages/user/view-users', [
          'breadcrumbs' => $breadcrumbs,
          'active_menu' => $this->active_menu,
           'user' => $user
        ]);
    }
    // this handles the edit screen of user
    public function editUser($id)
    {    
      
      $user = User::where('id',$id)->first();
      
      
      $breadcrumbs = [
        ['link' => "/admin/dashboard", 'name' =>   __('user.home') ], ['link' => "/admin/manage-users", 'name' => __('user.user')], ['name' =>  __('user.edit_user')]
      ];
      return view('/admin/pages/user/edit-users', [
        'breadcrumbs' => $breadcrumbs,
        'active_menu' => $this->active_menu,
        'user' => $user,
      ]);
    }

    // this handles the update process of user data
    public function updateUser(EditUserRequest $request)
    {        
        $request_data = $request->all();
        $id = $request_data['id'];
        $user_update = User::find($id);

        $user_update->first_name = $request->input('first_name');
        $user_update->last_name = $request->input('last_name');
        $user_update->email = $request->input('email');
        $user_update->phone = $request->input('phone');

        if($user_update->save())
        {
            $request->session()->flash('created', __('user.update_success'));
            return redirect('admin/manage-users');
         
        } else {
            $request->session()->flash('creation_failed', __('user.update_failure'));
            return redirect('admin/manage-users');
        }  
    }

    // this will change the status of user
    public function activateUser(Request $request)
    {
        $request_data = $request->all();      
        $user_id = $request_data['user_id'];
        User::where('id',$user_id)->update(['status'=>1]);
        return true;
    }
   // this will change the status of user
    public function deactivateUser(Request $request)
    {
      $request_data = $request->all();      
      $user_id = $request_data['user_id'];
      User::where('id',$user_id)->update(['status'=>3]);
      return true;
    }
  // this will get the status of user
    public function statusUser(Request $request) {
        
        $request_data = $request->all();      
      $user_id = $request_data['user_id'];
      $status = User::select('status')->where('id',$user_id)->first();
      return $status->status;
    }
}
