<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Mail\ContactUsNotification;
use Illuminate\Support\Facades\Mail;
class ContactUs extends BaseController
{
    public function index()
    { 
        return view('/frontend/pages/contact-us', [
            'page_no'=>4
          ]);
    }
    // this handles the contact data
    public function mailContact(Request $request)
    {
        $request_data = $request->all();
        
        if(config('values.enable_email'))
        {
            Mail::to(config('mail.from.to'))->send(new ContactUsNotification($request_data));
        }
        
       $request->session()->flash('created', __('contact.success'));
       return redirect('/contact-us#contact_us_form');
    }
   
}
