<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use App\Mail\GiftDynamicURLNotification;
use Illuminate\Support\Facades\Mail;
use App\Notifications\CustomVerifyEmailNotification;
use App\Mail\GiftCouponsNotification;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class EmailTemplateController extends Controller
{
    /**
     * Display a preview of the email template.
     *
     * @param string $template
     * @return \Illuminate\View\View|\Illuminate\Contracts\View\Factory
     */
    public function previewEmail($template)
    {
	
       // Example data to populate the email template
       // $data = ['url' => 'https://example.com']; // Replace with actual data as necessary



      if( $template == "GiftDynamicURLNotification"){
        
	       $data = [
    'gift_mail' => [
        'partner1' => [
            [
                'order_id' => 'ORD123456',
                'partner_name' => 'Just Eat Takeaways',
                'gift_value_price' => 15,
                'gift_card_url' => 'https://example.com/gift-card-url-1',
                'gift_pin' => '1234', // Optional: include only if needed
                'partner_url' => 'https://just-eat.com/partner-info' // Added partner URL
            ],
            [
                'order_id' => 'ORD123457',
                'partner_name' => 'Just Eat Takeaways',
                'gift_value_price' => 20,
                'gift_card_url' => 'https://example.com/gift-card-url-2',
                'gift_pin' => '5678', // Optional: include only if needed
                'partner_url' => 'https://just-eat.com/partner-info' // Added partner URL
            ]
        ],
        'partner2' => [
            [
                'order_id' => 'ORD78910',
                'partner_name' => 'Amazon',
                'gift_value_price' => 50,
                'gift_card_url' => 'https://example.com/amazon-gift-card',
                'partner_url' => 'https://amazon.com/partner-info' // Added partner URL
            ],
            [
                'order_id' => 'ORD78911',
                'partner_name' => 'Amazon',
                'gift_value_price' => 75,
                'gift_card_url' => 'https://example.com/amazon-gift-card-2',
                'partner_url' => 'https://amazon.com/partner-info' // Added partner URL
            ]
        ]
    ],
    'url' => 'https://example.com' // General URL if needed elsewhere in the template
];
	      
	// Define the recipient email
        $testEmailAddress = '<EMAIL>';

        // Send the email
        Mail::to($testEmailAddress)->send(new GiftDynamicURLNotification($data));

        // Return a response indicating success
        return response()->json(['message' => 'Test GiftDynamicURLNotification email sent successfully to ' . $testEmailAddress]);
      }elseif($template == "GiftCouponsNotification"){
      
      $data = [
    'gift_mail' => [
        'partner1' => [
            [
                'order_id' => 'ORD123456',
                'partner_name' => 'Just Eat Takeaways',
                'gift_value_price' => 15,
                'gift_card_url' => 'https://example.com/gift-card-url-1',
                'gift_card_code' => 'CODE123', // Added gift card code
                'gift_pin' => '1234', // Include only if needed
                'partner_url' => 'https://just-eat.com/partner-info' // Added partner URL
            ],
            [
                'order_id' => 'ORD123457',
                'partner_name' => 'Just Eat Takeaways',
                'gift_value_price' => 20,
                'gift_card_url' => 'https://example.com/gift-card-url-2',
                'gift_card_code' => 'CODE456', // Added gift card code
                'gift_pin' => '5678', // Include only if needed
                'partner_url' => 'https://just-eat.com/partner-info' // Added partner URL
            ]
        ],
        'partner2' => [
            [
                'order_id' => 'ORD78910',
                'partner_name' => 'Amazon',
                'gift_value_price' => 50,
                'gift_card_url' => 'https://example.com/amazon-gift-card',
                'gift_card_code' => 'AMZ50', // Added gift card code
		'gift_pin' => '5678', // Include only if needed
		'partner_url' => 'https://amazon.com/partner-info' // Added partner URL
            ],
            [
                'order_id' => 'ORD78911',
                'partner_name' => 'Amazon',
                'gift_value_price' => 75,
                'gift_card_url' => 'https://example.com/amazon-gift-card-2',
                'gift_card_code' => 'AMZ75', // Added gift card code
		'gift_pin' => '5678', // Include only if needed
		'partner_url' => 'https://amazon.com/partner-info' // Added partner URL
            ]
        ]
    ],
    'url' => 'https://example.com' // General URL if needed elsewhere in the template
];

      
      
      
      // Define the recipient email
        $testEmailAddress = '<EMAIL>';

        // Send the email
        Mail::to($testEmailAddress)->send(new GiftCouponsNotification($data));

        // Return a response indicating success
        return response()->json(['message' => 'Test GiftCouponsNotification email sent successfully to ' . $testEmailAddress]);
      }elseif($template == "VerifyEmailNotification"){
	      $user = User::findOrFail(4311);
	      $user->email = "<EMAIL>";
	     Log::info('User object:', ['user' => $user->toArray()]); 
	     // This will throw an exception if the user does not exist
        $user->sendEmailVerificationNotification();
        return response()->json(['message' => 'Test VerifyEmailNotification email sent successfully to ' . $user->email]);
      
      }else{
        return response()->json(['template' => $template]);
      }
	
	
    }


    /**
     * Print the template name from the URL.
     *
     * @param string $template The template name passed in the URL.
     * @return \Illuminate\Http\Response
     */
    public function printTemplateName($template)
    {
        return response()->json(['template' => $template]);
    }



}

