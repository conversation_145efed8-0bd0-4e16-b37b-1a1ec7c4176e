<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Services\Cart as CartService;
use App\Rules\MatchOldPassword;
use Hash;
 
class RegisterVoucher extends BaseController
{
    public function __construct()
    {
      
    }
    // this handles the view screen of register voucher
    public function index()
    {
        
        return view('/frontend/pages/register-voucher', [
              
          ]);
    }

    // this function will show the error message while registgering
    public function registerError()
    {
        return view('/frontend/pages/verify-error', [
              
        ]);
    }
    // this handles the view screen of login page
    public function login()
    {
        return view('/frontend/pages/login', [
              
        ]);
    }
    // this handles the login via ajax
    public function loginAjax(Request $request,CartService $cart)
    {
        try {
            $login_return = '';
            // Get new jobs or execute a job
            $messages = ['email.required' => __('message.api.email_required'), 'email.email' => __('message.api.email_format'), 'password.required' => __('message.api.password_required') ];
            $headers_data = $request->all();
            $validator = Validator::make($headers_data, ['email' => 'required|email', 'password' => 'required', ], $messages);
            if ($validator->fails()) {
               
               return $this->sendError(__('message.api.validation_title'), $validator->errors(), 422);
            }
            $login_validate = array();
            $login_validate['email'] = $headers_data['email'];
            $login_validate['password'] = $headers_data['password'];
            if (Auth::attempt(['email' => $login_validate['email'], 'password' => $login_validate['password']])) {
                $user = Auth::user();
                $userData = User::find($user->id);
          
                if($userData->status==2)
                {
                  Auth::logout();
                   $login_return = $this->sendError(__('Account is not verified!'), ['email' => __('Account is not verified!') ], 422);
                }else if($userData->roles()->where('title', 'admin')->exists()){
                    Auth::logout();
                    $login_return = $this->sendError(__('Access Denied!'), ['email' => __('Access Denied!') ], 422);
                    
                }else
                {
                    $request->session()->regenerate();
                    // add products to cart which is in cookie
                    $cart->setBasketToLogin();
    
                    $login_return = $this->sendResponse($user, __('message.api.login_success'), 200);
                }
              
            }else
            {
                $login_return = $this->sendError(__('message.api.email_password_combination'), ['email' => __('message.api.email_password_combination') ], 422);
            }
            return $login_return;
        }catch(Exception $e) {
            $e = $e->getMessage();
        }
    }
// this changes the user password
    public function changePasswordAjax(Request $request)
    {
        $user = Auth::user();
            $messages = [
                'currentpassword.required' => trans('passwords.password_required'),
               'password.required' => trans('passwords.newpass_required'),
             ];

            $validator = Validator::make($request->all(), [
                'currentpassword' => ['required'],
                'password' => 'required',
                 
            ], $messages);

            if ($validator->fails()) {
                return $this->sendError('Validation Error.', $validator->errors(), 422);
            }

            if (!Hash::check($request->currentpassword, $user->password))
            {
                return $this->sendError('Validation Error.', ['password' => __('Your Current password does not match!') ], 422); 
            }
            else
            {
                $user->password = Hash::make($request->newPassword);                    
                $user->save();
            }

            $user = $request->user();
            $user->password = bcrypt($request->password);
            $user->save();
            
            return $this->sendResponse($user, __('passwords.mobileChange'), config('constant.status_code.success'));
    }

    public function sendResponse($result, $message, $code = 0)
    {
       
        $response = [
            'success' => true,
            'status_code' => $code,
            'data'    => $result,
            'message' => $message,
        ];

        return response()->json($response);
    }


    /**
     * return error response.
     *
     * @return \Illuminate\Http\Response
     */
    public function sendError($error, $errorMessages = [], $codes = 2)
    {
        $response = [
            'success' => false,
            'message' => $error,
            'status_code' => $codes
        ];

        if (!empty($errorMessages)) {
            $response['data'] = [];
            
            if(is_array($errorMessages))
            {
            
                foreach ($errorMessages as $messages) {
                    $response['errors'][]['message'] = $messages;
                }
                $response['message'] = $response['errors'][0]['message'];
                
            }else if(!is_string($errorMessages) &&  is_array($errorMessages->toArray()) && count($errorMessages->toArray()) > 0) {
                foreach ($errorMessages->toArray() as $messages) {
                    $response['errors'][]['message'] = $messages[0];
                }
                $response['message'] = $response['errors'][0]['message'];
            }
        }

        return response()->json($response);
    }
   
}
