<?php

namespace App\Http\Controllers\Frontend\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Models\User;
use Validator;
use PhpParser\Node\Stmt\Return_;

class VerifiedAlreadyController extends Controller
{
    /**
     * Display the email Already validated Issue.
     *
     * @return \Illuminate\View\View
     */
    public function display()
    {
        return view('frontend.auth.already-verified');
    }
}

