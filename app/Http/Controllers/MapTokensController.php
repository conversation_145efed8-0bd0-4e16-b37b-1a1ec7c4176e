<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\TagToken;
use App\Models\Tags as TagsModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Services\EncryptDecrypt;

class MapTokensController extends Controller
{
    // this handles the save process of tag and tokens  
    public function store(Request $request)
    { 
        $token = $request->header('token');
        if(!isset($token)){
            return response()->json(['error'=>'Token value Required!','status'=>'False']);
        }
        $cc = new EncryptDecrypt;
        $code_decrypt  =  $cc->codeDecrypt($token);
        $token_data = explode("_", $code_decrypt);
        $tokenTimestamp = $token_data[1];
        $tokenDate = date('Y-m-d', $tokenTimestamp);
        $currentDateAsString = date('Y-m-d');
    
        if (!($token_data[0] == 'TOKEN' && $tokenDate === $currentDateAsString)) {
            return response()->json(['error'=>'Authentication failed!','status'=>'False']);
        } 
    
       $validator = Validator::make($request->all(), [
        'tag_id' => 'required',
        'tokens' => 'required|array'
        ]);
    
        if (!$validator->passes()) {
            return [
                'error'=>'true',
                'message'=>$validator->errors()->all() 
            ];
        }

        $getTokens = TagToken::where('tag_id',$request->tag_id)->whereIn('tokens',$request->tokens)->get();
        $DBTokens = array_column( $getTokens->toArray(),'tokens' );
        $newTokens = array_diff( $request->tokens, $DBTokens );

        if(empty($newTokens)){
            return response()->json(['error'=>'Duplicate token found for same tag!','status'=>'False','duplicate_token'=> $DBTokens]); 
        }

        $data = [];
        foreach($newTokens as $token)
        {
            $data[] = [
                'tag_id' => $request->tag_id, 
                'tokens' => $token,
                'created_at' => now(),
                'updated_at' => now()
              ];
        }
      
        $result = TagToken::insert($data);
        if(!$result){
            return response()->json(['error'=>'Something went wrong, Tokens not added!','status'=>'False']); 
        }
        return response()->json(['message'=>'Tokens successfully added','status'=>'True','duplicate_token'=> $DBTokens]);
    }

    // this handles the Show Tags List  
    public function getTags(){
        $tags = TagsModel::select('id','tag')->get();
        if(empty($tags)){
            return response()->json(['message'=>'No Records Found']);
        }
        return response()->json(['data'=> $tags]); 
    }

    public function createToken()
    {
        $str = 'TOKEN_'.time();
        $cc = new EncryptDecrypt;
        $code_encrypt  =  $cc->codeEncrypt($str);
        return response()->json(['token'=> $code_encrypt]); 
    }
}