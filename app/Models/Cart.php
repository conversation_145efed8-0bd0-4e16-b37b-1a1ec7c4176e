<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    use HasFactory;
    public function partner()
    {
        return $this->belongsTo(Partners::class,'partner_id');
    }
    public function giftCodesValue()
    {
        return $this->belongsTo(GiftValues::class,'gift_value_id');
    }

    public static function getTagsDataByUserId($userID){
        $udata = UserVouchers::select('voucher_code')->where('user_id',$userID)->get()->toArray();
        $vouchers = array_column($udata,'voucher_code');
        $tagViaToken = TagToken::select('tag_id')->whereIn('tokens',$vouchers)->get()->toArray();
        $tagIDS = array_column($tagViaToken,'tag_id');

        return $tagIDS;
    }
}
