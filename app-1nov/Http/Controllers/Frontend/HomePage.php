<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Models\Faq as FaqModel; 
use App\Models\CmsPages;
use App\Models\Partners;
use App\Models\PartnerCategory;
use App\Services\Cart as CartService;
class HomePage extends BaseController
{
    function updateExpiredCodes(CartService $cart)
    {
        $cart->updateExpiredCodes();
    }
    // this returns the homepage of site
    public function index(CartService $cart)
    {
        if(isset($_REQUEST['duplicate']) && $_REQUEST['duplicate']=='1')
        {
            $cart->getDuplicateCodes();
        }
        $cart->updateExpiredCodes();
        // this gets all brands list
        $brands_array = $this->getBrandsList();
       
        // this gets all partner categories
         $partner_category = PartnerCategory::all();
        $desktop_banner = CmsPages::where('page_type',CmsPages::DESKTOPBANNER)->first();
        $mobile_banner = CmsPages::where('page_type',CmsPages::MOBILEBANNER)->first();
        return view('/frontend/pages/homepage', [
              'brands' => $brands_array,
              'partner_category' => $partner_category,
              'page_no'=>1,
              'desktop_banner'=>$desktop_banner,
              'mobile_banner' => $mobile_banner
          ]);
    }
    // this function returns all brands
    public function getBrandsList()
    {
        $brands = Partners::where('status',Partners::ACTIVE)->orderBy('id','desc')->get();
        return array_chunk($brands->toArray(), 10);
        
    }
    // this handles how it works screen
    public function howItWorks()
    {
        $brands_array = $this->getBrandsList();
        $faqs = FaqModel::where('status',FaqModel::ACTIVE)->offset(0)->limit(5)->get();
        return view('/frontend/pages/how-it-works', [
            'brands' => $brands_array,
              'faqs' =>$faqs,
              'page_no'=>2
          ]);
    }
    // this handles the faq screen
    public function faqs()
    {
       
        $faqs = FaqModel::where('status',FaqModel::ACTIVE)->get();
        return view('/frontend/pages/faqs', [
            
              'faqs' =>$faqs
             
          ]);
    }
    // terms and conditions page
     public function termsAndConditions()
     {
        $cms_page = CmsPages::where('page_type',CmsPages::TERMSANDCONDITION)->first();
        return view('/frontend/pages/terms-and-conditions', [
              'cms_page' =>$cms_page,
              'meta_keywords'=>$cms_page->meta_keywords,
              'meta_description'=>$cms_page->meta_description
          ]);
     }
     // this handles the privacy policy screen
     public function privacyPolicy()
     {
        $cms_page = CmsPages::where('page_type',CmsPages::PRIVACYPOLICY)->first();
        return view('/frontend/pages/privacy-policy', [
              'cms_page' =>$cms_page,
              'meta_keywords'=>$cms_page->meta_keywords,
              'meta_description'=>$cms_page->meta_description
          ]);
     }
     // this handles the veriy success page 
     public function verifySuccess(CartService $cart)
     {
        // this add products in cart from user cookies
        $cart->setBasketToLogin();
        return view('/frontend/pages/verify-success');
     }
   
}
