<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Models\Partners as PartnerModel; 
use App\Models\GiftValues;
use App\Services\Tillo;
use DB;
use Auth;
class PartnerFront extends BaseController
{
    // this handles the view screen of where to spend page
    public function whereToSpend()
    {
        $partner_data = PartnerModel::with('category.categoryImages')->where('partners.status',PartnerModel::ACTIVE)->orderBy('partners.partner_category_id')->get();
        $partner_array = array();
        foreach($partner_data as $partner)
        {
            $partner_array[$partner->partner_category_id]['category_name'] =$partner->category->category_name;
            $partner_array[$partner->partner_category_id]['category_description'] =$partner->category->category_description;
            $partner_array[$partner->partner_category_id]['images'] = $partner->category->categoryImages;
            $partner_array[$partner->partner_category_id]['partners'][] = $partner;
        }
     
        return view('/frontend/pages/where_to_spend', [
                'partner_listing' => $partner_array,
                'page_no'=>3
          ]);
    }
    // this handles the partner detail page screen
    public function partnerDetail($slug='',Tillo $tillo)
    {
        $partner_data = PartnerModel::where('slug',$slug)->where('status',PartnerModel::ACTIVE)->first();
        $partner_code = $partner_data->partner_code;
        $partnerOpenAmountData = $partner_data->partnerOpenAmounts();
        $partnerOpenAmountData = empty($partnerOpenAmountData) ? [] : $partnerOpenAmountData->toArray();
        // gets all gift codes of partner
        $gift_values = GiftValues::where('status',GiftValues::ACTIVE)->where(DB::raw('date(expiry_date)'),'>',date('Y-m-d'))->where('partner_code',$partner_code)->groupBy('gift_value')->orderBy('gift_value','ASC')->get();
        $partner_status = $partner_data->status;
        $tillo_api_data = array();
        if($partner_status==2)
        {
         $gift_values = array();
        
        }
        if($partner_data->partner_type==2)
        {
            $tillo_api_data = $tillo->getBrandDetails($slug);
            
        }
        
       
        
        return view('/frontend/pages/partner_detail', [
            'partner_data' => $partner_data,
            'partner_open_amounts' => $partnerOpenAmountData,
            'partner_gift'=>$gift_values,
            'meta_keywords'=>$partner_data->meta_keywords,
            'meta_description'=>$partner_data->meta_description,
            'tillo_api_data'=> $tillo_api_data
          ]);
       
    }
}
