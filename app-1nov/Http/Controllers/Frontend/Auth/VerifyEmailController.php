<?php

namespace App\Http\Controllers\Frontend\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use App\Models\UserVouchers;
use App\Services\Epay;
use Redirect;

class VerifyEmailController extends Controller
{
    /**
     * Mark the authenticated user's email address as verified.
     *
     * @param  \Illuminate\Foundation\Auth\EmailVerificationRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function __invoke(EmailVerificationRequest $request)
    {
        $epay = new Epay;
        $userVoucher = new UserVouchers;
        $user_id = $request->user()->id;
        $redeem_points = $userVoucher->redeemVoucher($user_id,$epay);
        if($redeem_points==0)
        {
            return Redirect::to(RouteServiceProvider::REGISTERERROR);    
        }
       
        if ($request->user()->hasVerifiedEmail()) {
             return redirect()->intended(RouteServiceProvider::FRONTEMAILVERIFIED);   
        }      

        if ($request->user()->markEmailAsVerified()) {
            $user_id = $request->user()->id;
            $redeem_points = $userVoucher->redeemVoucher($user_id,$epay);
            event(new Verified($request->user()));
        }

        return Redirect::to(RouteServiceProvider::FRONTEMAILVERIFIED);
    }
}
