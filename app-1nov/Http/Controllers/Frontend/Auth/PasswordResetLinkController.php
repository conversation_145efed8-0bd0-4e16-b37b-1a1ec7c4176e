<?php

namespace App\Http\Controllers\Frontend\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Models\User;
use Validator;
use PhpParser\Node\Stmt\Return_;

class PasswordResetLinkController extends Controller
{
    /**
     * Display the password reset link request view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('frontend.auth.forgot-password');
    }

    /**
     * Handle an incoming password reset link request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {        
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);
        $error = 0;
       $return = array();
       $user = User::select('id')->where('email',$request->email)->first();
       if(isset($user->id))
       {
          $userData = User::find($user->id);    
       }
        
        if (!$validator->passes()) {
             $error = 1;
            $return = [
                'error'=>'true',
                'msg'=>$validator->errors()->all() 
            ];
        }else if(!isset($user->id))
        {
            Auth::logout();    
            $error = 1;    
            $return = [
                'error'=>'true',
                'msg'=>__('Enter Valid Email!') 
            ];
        }else if($userData->roles()->where('title', 'admin')->exists()){
            Auth::logout();      
            $error = 1;  
            $return = [
                'error'=>'true',
                'msg'=>__('Access Denied!') 
            ];
        }
        if($error ==1)
        {
            return $return;
        }
         // End Authenticating whether email is valid and whether user has access to make forgot password request

        // We will send the password reset link to this user. Once we have attempted
        // to send the link, we will examine the response then see the message we
        // need to show to the user. Finally, we'll send out a proper response.
        $status = Password::sendResetLink(
            $request->only('email')
        );
        
        if($status == Password::RESET_LINK_SENT){
            return [
                'error'=>'false',
                'msg'=>__($status)
            ];
        } else {
            return [
                'error'=>'true',
                'msg'=>__($status) 
            ];
        }
    }
}
