<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Http\Requests\Admin\AddTilloCategoryRequest;
use Illuminate\Support\Facades\Storage;
use App\Models\TilloCategory as TilloCategoryModel;

use DB;
use Redirect;

class TilloCategoriesController extends BaseController
{   
    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = 3;
    }
    // this handles the view screen of all Tillo Categories
    public function index()
    {   
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('tillo.home') ], ['link' => "javascript:void(0)", 'name' => __('tillo.tillo')], ['name' =>  __('tillo.tillo_category')]
          ];
        $tillo_data = TilloCategoryModel::orderBy('id','desc')->get();
        return view('/admin/pages/tillo/manage-tilloCategories', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'tillo_data'=> $tillo_data
          ]);
    }
    // this handles the view screen of add Tillo Category
    public function addTilloCategory()
    {
        $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('tillo.home') ], ['link' => "/admin/manage-tillo-categories", 'name' => __('tillo.tillo')], ['name' =>  __('tillo.add-tillo')]
        ];
       
         return view('/admin/pages/tillo/add-tillo-category', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu
          ]);
    }
    // this handles the create Tillo Category 
    public function createTilloCategory(AddTilloCategoryRequest $request)
    {
      $TilloModel = new TilloCategoryModel;
      $TilloModel->category_name = $request->input('category_name');
      $TilloModel->slug = $request->input('slug');
      $TilloModel->save();
      $request->session()->flash('created', __('tillo.added_success'));
      return redirect('admin/manage-tillo-categories');
    }

    // this handles the view screen of Tillo Category
    public function viewTilloCategory($id)
    {
    }

    // this handles the edit screen of Tillo Category
    public function editTilloCategory($id)
    { 
      $tillo_data = TilloCategoryModel::where('id',$id)->first();
      $breadcrumbs = [
        ['link' => "/admin/dashboard", 'name' =>   __('tillo.home') ], ['link' => "/admin/manage-tillo-categories", 'name' => __('tillo.tillo')], ['name' =>  __('tillo.edit-tillo')]
      ];
      return view('/admin/pages/tillo/edit-tillo-category', [
        'breadcrumbs' => $breadcrumbs,
        'active_menu' => $this->active_menu,
        'tillo'=> $tillo_data
      ]);
    }

    // this handles the update process of Tillo Category
    public function updateTilloCategory(AddTilloCategoryRequest $request)
    {
      $request_data = $request->all();
      $id = $request_data['id'];
     
      $tillo_update = TilloCategoryModel::find($id);
      $tilloData = [
        'category_name' => $request->input('category_name'), 
        'slug' => strtolower($request->input('slug'))
      ];
      $tillo_update->update($tilloData);
      $request->session()->flash('created', __('tillo.update_success'));
      return redirect('admin/manage-tillo-categories');
    }

    // this handles the delete of Tillo Category
    public function deleteTilloCategory(Request $request)
    {
      $request_data = $request->all();
      
      $tillo_id = $request_data['tillo_id'];
      TilloCategoryModel::where('id',$tillo_id)->delete();
      return true;
    }
}