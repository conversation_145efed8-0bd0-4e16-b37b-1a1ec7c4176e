<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class AddTilloCategoryRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'category_name'    => [
                'required',
                'max:60'

            ],
            'slug'  => [
                'required',
                'regex:/^[\w-]*$/',
                'max:60'

            ]
        ];
    }
}
