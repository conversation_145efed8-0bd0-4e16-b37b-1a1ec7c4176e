<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class EditPartnerRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'partner_name'    => [
                'required',
                'max:60'
            ],
            'partner_category_id'    => [
                'required',
            ],
            'logo_edit' =>
            [
                'required_without:logo',
            ],
            
            'logo'    => [
                'required_without:logo_edit',
                'mimes:jpeg,png,jpg',
                'max:2048',
            ],

            'upload_image_edit' => [
                'required_without:upload_images',
            ],
            'upload_images'    => [
                'required_without:upload_image_edit',
                // 'image',
                // 'mimes:jpeg,png,jpg',
                // 'max:2048',                
            ],
            'upload_images.*'    => [
                //'required_without:upload_image_edit',
                // 'image',
                'mimes:jpeg,png,jpg',
                 'max:2048',                
            ],
            'short_desc'    => [
                'required',
                'max:200'
            ],
            'partner_description'    => [
                'required',
            ],
            'reason_well'    => [
                'required',
            ],
            'terms_and_conditions'    => [
                'required',
            ],
            'redemeption_process'    => [
                'required',
            ],
            'slug'  => [
                'required',
                'regex:/^[\w-]*$/',
                'max:60'

            ], 
            
        ];
    }
    public function messages()
    {
        return [
            'thumbnail_image_tmp.required_without' =>  'The image field is required.',
            'thumbnail_image_tmp.mimes' =>  'The image must be a file of type: jpeg, png, jpg.',
            'logo.required_without'=> 'The logo field is Required.',
            'upload_images.required_without'=> 'The upload images field is Required.',
            'logo.max' => 'The logo must not be greater than 2mb (2048 kilobytes). ',
            'short_desc.required'=>'Short Description field is required.'
        ];
    }
}

