<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class AddTagRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'tag'    => [
                'required',
                'unique:tags'

            ]
        ];
    }

}