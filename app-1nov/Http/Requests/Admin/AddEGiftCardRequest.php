<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class AddEGiftCardRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
                        
            'eGiftCardCsv'    => [
                'required',
                'mimes:csv,txt',
                 //'mimes:csv',
            //    'mimetypes:application/csv,application/excel,application/vnd.ms-excel,application/vnd.msexcel,text/csv,text/plain',
               //'mimetypes:text/csv',
            // 'in:csv',
            // 'mimetypes:application/csv,application/excel,application/vnd.ms-excel,application/vnd.msexcel,text/csv,text/plain',
            // 'mimes:csv,txt',
                'max:2048',
            ], 
            
        ];
    }
    public function messages()
    {
        return [
            'eGiftCardCsv.mimes' => 'Please upload only CSV file.',
            'thumbnail_image_tmp.required_without' =>  'The image field is required.',
            'thumbnail_image_tmp.mimes' =>  'The image must be a file of type: jpeg, png, jpg.'     
     
        ];
    }
}
