<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class AddPartnerRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            'partner_name'    => [
                'required',
                'max:60'

            ],
            'partner_category_id'    => [
                'required',
            ],            
            'logo'    => [
                'required',
                'mimes:jpeg,png,jpg',
                'max:2048',
               // 'dimensions:max_width=300,max_height=200'
            ],
            
            'upload_images'    => [
                'required',
                // 'mimes:jpeg,png,jpg'
                
            ],
            'upload_images.*'    => [
                // 'required',
                'mimes:jpeg,png,jpg',
                'max:2048',
              //  'dimensions:max_width=300,max_height=200'
                
            ],

            'partner_description'    => [
                'required',
            ],
            'short_desc'    => [
                'required',
                'max:200'
            ],
            'reason_well'    => [
                'required',
            ],
            'terms_and_conditions'    => [
                'required',
            ],
            'redemeption_process'    => [
                'required',
            ],
            'slug'  => [
                'required',
                'regex:/^[\w-]*$/',
                'max:60',
                'unique:partners'

            ], 
            
        ];
    }
    public function messages()
    {
        return [
            'thumbnail_image_tmp.required_without' =>  'The image field is required.',
            'thumbnail_image_tmp.mimes' =>  'The image must be a file of type: jpeg, png, jpg.',
            'logo.max' => 'The logo must not be greater than 2mb (2048 kilobytes). '     ,
            'logo.dimensions'=>'Logo dimesions should 300*200.',
            'upload_images[].dimensions=>Uploaded file dimensions should be 300*200',
            'short_desc.required'=>'Short Description field is required.'
        ];
    }
}
