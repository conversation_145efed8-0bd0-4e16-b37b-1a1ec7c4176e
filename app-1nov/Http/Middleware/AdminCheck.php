<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class AdminCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        if($user->status==3)
        {
          Auth::logout();
          $request->session()->flash('failToLogin', 'Account is deactivated by Admin!');        
          return redirect('/');
        }
        $role =  $user->roles()->select('title')->get();
        $error = 0;
        if(!$role->isEmpty())
        {
            $accountRole = $role[0]->title;
            $url = request()->segments(0);

            if($accountRole == 'admin'){
            
                if(count($url) >0 && $url[0] !== 'admin') {
                    $error = 1;
                    
                    
                }
            }
            elseif($accountRole == 'user'){
                if(count($url) >0 && $url[0] == 'admin') {
                    $error = 1;
                         
                    
                }
            }
        } else {
            $url = request()->segments(0);
            if(count($url) >0 && $url[0] == 'admin') {
                $error = 1;
              
            }
        }
        if($error==1)
        {
            return redirect()->back();
        }
        return $next($request);
    }
}
