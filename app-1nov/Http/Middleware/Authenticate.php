<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        $url = request()->segments(0);
        if(count($url) >0) {
            if($url[0] == 'admin'){
                if (! $request->expectsJson()) {
                    return route('login');
                }
            }
            else {
                if (! $request->expectsJson()) {
                    return route('frontUser.login');
                }
            }
        }    
    }
}
