<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;
    public function partner()
    {
        return $this->belongsTo(Partners::class,'partner_id');
    }
    public function giftCodesValue()
    {
        return $this->belongsTo(GiftValues::class,'gift_value_id');
    }
}
