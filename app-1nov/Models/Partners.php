<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partners extends Model
{
    use HasFactory;
    protected $fillable = [
        'partner_name',
        'partner_category_id',
        'partner_type',
        'denomination_type',
        'tillo_category_id',
        'logo',
        'partner_description',
        'reason_well',
        'terms_and_conditions',
        'redemeption_process',
        'slug',
        'partner_code',
        'status',
        'short_desc',
        'meta_keywords',
        'meta_description'

    ];
    public const ACTIVE = 1;
    public const INACTIVE = 2;
    public const SOFTDELETED = 3;
    public const STATICPARTNER = 1;
    public const DYNAMICPARTNER = 2;
    public const FIXEDVALUE = 0;
    public const OPENVALUE = 1;
    public function category()
    {
        return $this->belongsTo(PartnerCategory::class,'partner_category_id');
    }

    public function partnerImages()
    {
        return $this->hasMany(PartnerImage::class,'partner_id');
    }

    public function giftCodes()
    {
        return $this->hasMany(GiftValues::class,'partner_code','partner_code');
    }

    public function partnerOpenAmounts()
    {
        return $this->hasMany(PartnerOpenAmount::class,'partner_id')->pluck('open_value');
    }
}
