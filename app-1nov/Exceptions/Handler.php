<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Exception;
use Illuminate\Auth\AuthenticationException;
class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
 
    public function render($request, Throwable $exception)
    {  
  
        if($this->isHttpException($exception)){
            if($exception->getStatusCode() == 404)
            {
                $url = request()->segments(0);
                if(count($url) >0 && ($url[0] !== 'admin')) {
                    return redirect()->route('frontUser.404');
                
                } else {
          
                    return redirect()->route('admin.404');
                }
            }
        }
        
        return parent::render($request, $exception);
    }
    
}
