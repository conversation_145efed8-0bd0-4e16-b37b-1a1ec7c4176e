<?php

namespace App\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use App\Models\VoucherTransactions as VoucherTransactionsModel;
use App\Models\UserVouchers;
use App\Models\MyBalance;
use Auth;
class Epay
{
    public const VOUCHER_PREFIX = 'TT';
    protected $epay_username, $epay_password, $epay_txnsalt, $epay_posturl,$user_id;
    public function __construct()
    {
        $currentUser = Auth::user();
        $this->epay_username = \Config::get('values.epay_username');
        $this->epay_password =\Config::get('values.epay_password');
        $this->epay_txnsalt =  \Config::get('values.epay_txnsalt');
        $this->epay_posturl = \Config::get('values.epay_posturl');
        $this->user_id = isset($currentUser)?$currentUser->id:0;
    }

     /**
     *  this method create unique salt for each transaction 
     *
     * returns salt value
     */
    public function createSalt($balance='')
    {
            $salt = $this->epay_txnsalt;
            $last_id = VoucherTransactionsModel::orderBy('id','desc')->first();
            $last_id_val = isset($last_id->id)?$last_id->id:0;
            $trans_id = $balance=='balance'?($last_id_val+1).'_balance':$last_id_val+1;
            return $salt.'_'.time().'_'.$trans_id;
           
    }
     /**
     *  this method checks the status of voucher codes
     *
     * returns status of voucher code
     */
    public function epayVoucherStatus($request)
    {

          if(!$this->isValidVoucher($request['voucher_code']))
          {
            //   echo json_encode(array('status'=>'FAIL','message'=>'Card not recognised.'));
            //   die;
          }
          $resend_voucher = isset($request['resend_voucher'])?1:0;
          if($resend_voucher==1)
          {
             $transaction_id = VoucherTransactionsModel::where('request',$request['voucher_code'])->where('user_id',$this->user_id)->orderBy('id','desc')->first();
             $this->reverseTransaction($transaction_id->txn_id,$request['voucher_code']);
             $this->epay_posturl = \Config::get('values.epay_backupurl');
          }
          $txnid  = $this->createSalt('');  

          $voucher_code = $request['voucher_code']; 
          $request_xml = '<REQUEST TYPE="CARDSTATUS" MODE="">
                            <CARD>
                                <PAN>'.$voucher_code.'</PAN>
                            </CARD>
                                    <LOCALDATETIME>'.date('Y-m-d H:i:s').'</LOCALDATETIME>
                                    <TERMINALID>XXX</TERMINALID>
                                    <USERNAME>'.$this->epay_username.'</USERNAME>
                                    <PASSWORD>'.$this->epay_password.'</PASSWORD>
                                    <TXID>'.$txnid.'</TXID>
                           </REQUEST>';
        $response = Http::withHeaders(['Content-Type' => 'text/xml; charset=utf-8'])->send('POST', $this->epay_posturl, [
                        'body' => $request_xml,
                    ]);
                   
        $balance= 0;
        if($response->successful())
        {
            $clean_xml = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $response);
            $xml = simplexml_load_string($clean_xml, "SimpleXMLElement", LIBXML_NOCDATA);
            $json = json_encode($xml);
            $response_json = json_decode($json,TRUE);
            $card_status = isset($response_json['CARDSTATUS'])?$response_json['CARDSTATUS']:'FAIL';
            $balance = isset($response_json['BALANCE']['CURRENCY']['BALANCE'])?$response_json['BALANCE']['CURRENCY']['BALANCE']:0;
            $card_status = $balance==0 && $card_status!='FAIL'?'REDEEMED':$card_status;
        }
        else{
            $card_status = 'FAIL';  
            $response_json = $response->body();
        }
        $add_new_voucher = isset($request['add_new_voucher'])?1:0;
        if($add_new_voucher==1)
        {
            $is_used = $this->isVoucherUsed($request['voucher_code']);
            $card_status = isset($is_used)?'USED':$card_status;
        }
         $card_flag = $this->getVoucherFlag($card_status);
         $save_transaction_response = new VoucherTransactionsModel;
         $save_transaction_response->txn_id = $txnid;
         $save_transaction_response->user_id = $this->user_id;
         $save_transaction_response->response = json_encode($response_json);
         $save_transaction_response->txn_status = $card_status;
         $save_transaction_response->request = $voucher_code;
         $save_transaction_response->transaction_type = 1;
         $save_transaction_response->save();
         if($add_new_voucher==1 && $card_status=='ACTIVATED')
         {
             
             $flag = $this->addMoreCard($request);

            if($flag==0)
            {
                $card_status = 'fail';
                $card_flag='Oops! Unable to redeem card. Kindly contact provider.';
            }
         }
          
         echo json_encode(array('status'=>$card_status,'message'=>$card_flag));
        
         
         
    }

     /**
     *  this method add more card to account
     *
     * 
     */
    public function addMoreCard($request)
    {
        $voucher_code = $request['voucher_code'];
        $currentUser = Auth::user();
        UserVouchers::create([
            'voucher_code' => $voucher_code,
            'user_id' => $currentUser->id
        ]);
       return $this->redeemPointExtra($currentUser->id,$request);
    }
    /**
     *  this method check if voucher is already used
     *
     * return true/false
     */
    public function isVoucherUsed($voucher_code ='')
    {
       return UserVouchers::where('voucher_code',$voucher_code)->where('redeemed_amount','!=','')->first();
    }

      /**
     *  this method check voucher flag
     *
     * return flag message
     */
    public function getVoucherFlag($card_status=0)
    {
        $val = '';
         switch ($card_status) {
            case 'FAIL':
                $val =   __('message.fail');
              break;
            case "NOT ACTIVATED":
                $val =   __('message.not_activated');
              break;
            case "ACTIVATED":
                $val =   __('message.activated');
              break;
            case "DEACTIVATED": 
              $val =  __('message.deactivated');
              break;
            case "REDEEMED": 
            $val =  __('message.redeemed');
            break;
            case "EXPIRED": 
            $val =  __('message.expired');
            break;
            case "USED": 
                $val =  __('message.used');
                break;
            default:
            $val =  __('message.activated');
            break;
            }
        return $val;
    }

     /**
     *  this method redeemed the value from voucher card
     *
     * return  redeem status
     */
    public function epayRedeem($voucher_code='',$balance='')
    { 
        
        $txnid  = $this->createSalt();  
        
        $save_transaction_response = new VoucherTransactionsModel;
        $save_transaction_response->txn_id = $txnid;
        $save_transaction_response->response = '';
        $save_transaction_response->txn_status = '';
        $save_transaction_response->user_id = $this->user_id;
        $save_transaction_response->transaction_type = 2;
        $save_transaction_response->request = $voucher_code;
        $save_transaction_response->save();
      
        $request = '<REQUEST TYPE="REDEEM">
                    <CARD>
                        <PAN>'.$voucher_code.'</PAN>
                    </CARD>
                        <AMOUNT>'.$balance.'</AMOUNT>
                        <LOCALDATETIME>'.date('Y-m-d H:i:s').'</LOCALDATETIME>
                        <TERMINALID>XXX</TERMINALID>
                        <USERNAME>'.$this->epay_username.'</USERNAME>
                        <PASSWORD>'.$this->epay_password.'</PASSWORD>
                        <TXID>'.$txnid.'</TXID>
                </REQUEST>';
                
               $response = Http::withHeaders(['Content-Type' => 'text/xml; charset=utf-8'])->send('POST', $this->epay_posturl, [
                'body' => $request,
            ]);
          
          
            if($response->successful())
            {
                $clean_xml = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $response);
                $xml = simplexml_load_string($clean_xml, "SimpleXMLElement", LIBXML_NOCDATA);
                $json = json_encode($xml);
                $response_json = json_decode($json,TRUE);
                $redeem_status = isset($response_json['RESULT']) && $response_json['RESULT']==0?'Success':'Failed';
                if($redeem_status=='Success')
                {
                    $user_voucher =  UserVouchers::where('voucher_code',$voucher_code)->orderBy('id','Desc')->first();
                    $user_voucher->redeemed_amount = $balance/100;
                    $user_voucher->save();
                }
                
            }
            else{
                $redeem_status = 'Failed';  
                $response_json = $response->body();
            }
            
            VoucherTransactionsModel::where('txn_id', $txnid)
            ->update([
            'response'=>json_encode($response_json), 
            'txn_status'=>$redeem_status, 
            ]);
           return array('status'=>$redeem_status);

    }

      /**
     *  this method add more voucher value to existing user
     *
     *  
     */
    public function redeemPointExtra($user_id,Request $request)
    {
        $voucher_code = UserVouchers::where('user_id',$user_id)->orderBy('id','Desc')->first();
        $request_data = $request->all();
        $voucher_code_request = isset($request_data['voucher_code'])?$request_data['voucher_code']:$voucher_code->voucher_code;
        $voucher_balance = $this->getVoucherBalance($voucher_code_request);
       $flag = 0;
       
        if($voucher_balance >0)
        {
            $redeem_balance = $this->epayRedeem($voucher_code_request,$voucher_balance);
            if($redeem_balance['status']=='Success')
            {
                $voucher_balance = $voucher_balance/100;
                 MyBalance::updateOrCreate(
                    ['user_id' => $user_id],
                    ['balance' =>\DB::raw('balance + '.$voucher_balance),'user_id'=>$user_id]
                );
                $flag = 1;
            }
        }
        return $flag;
    }

      /**
     *  this method add voucher value to new user
     *
     *  
     */
    public function redeemPoint($user_id='')
    {
        $voucher_code = UserVouchers::where('user_id',$user_id)->orderBy('id','Desc')->first();
       $voucher_balance = $this->getVoucherBalance($voucher_code->voucher_code); 
        $flag = 0;
        if($voucher_balance >0)
        {
            $redeem_balance = $this->epayRedeem($voucher_code->voucher_code,$voucher_balance);
            if($redeem_balance['status']=='Success')
            {
                $voucher_balance = $voucher_balance/100;
                MyBalance::updateOrCreate(
                    ['user_id' => $user_id],
                    ['balance' =>\DB::raw('balance + '.$voucher_balance),'user_id'=>$user_id]
                );
                $flag = 1;
            }
        }
        
        return $flag;
        
     
       
    }
  /**
     *  this method returns the voucher balance
     *
     *  
     */
    public function getVoucherBalance($voucher_code='')
    {
        $txnid  = $this->createSalt('balance');  
        $request = '<REQUEST TYPE="CARDSTATUS" MODE="">
                  <CARD>
                      <PAN>'.$voucher_code.'</PAN>
                  </CARD>
                      <LOCALDATETIME>'.date('Y-m-d H:i:s').'</LOCALDATETIME>
                      <TERMINALID>XXX</TERMINALID>
                      <USERNAME>'.$this->epay_username.'</USERNAME>
                      <PASSWORD>'.$this->epay_password.'</PASSWORD>
                      <TXID>'.$txnid.'</TXID>
               </REQUEST>';
        $response = Http::withHeaders(['Content-Type' => 'text/xml; charset=utf-8'])->send('POST', $this->epay_posturl, [
            'body' => $request,
        ]);
    
       $balance = 0;
       if($response->successful())
       {
          $clean_xml = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $response);
          $xml = simplexml_load_string($clean_xml, "SimpleXMLElement", LIBXML_NOCDATA);
          $json = json_encode($xml);
          $response_json = json_decode($json,TRUE);
          $balance = isset($response_json['BALANCE']['CURRENCY']['BALANCE'])?$response_json['BALANCE']['CURRENCY']['BALANCE']:0;
       }
       return $balance;
             
    }

      /**
     *  this method is used in cron to auto cancle incomlete transactions
     *
     *  
     */
    public function autoCancelTransaction()
    {
        $empty_response = VoucherTransactionsModel::where('response','')->where('txn_status','')->where('transaction_type',2)->get();
        foreach($empty_response as $empty)
        {
            $transaction_id = $empty->txn_id;
            $voucher_code  = $empty->request;
            $this->reverseTransaction($transaction_id,$voucher_code);
        }
    }

      /**
     *  this method revers the transaction by transaction id and voucher code
     *
     *  
     */
    public function reverseTransaction($transaction_id,$voucher_code)
    {
        $txnid  = $this->createSalt('');  
        $request = '<REQUEST TYPE="CANCEL">
                            <CARD>
                                <PAN>'.$voucher_code.'</PAN>
                            </CARD>
                                <LOCALDATETIME>'.date('Y-m-d H:i:s').'</LOCALDATETIME>
                                <TERMINALID>XXX</TERMINALID>
                                <USERNAME>'.$this->epay_username.'</USERNAME>
                                <PASSWORD>'.$this->epay_password.'</PASSWORD>
                                <TXID>'.$txnid.'123</TXID>
                                <TXREF>'.$transaction_id.'</TXREF>
                        </REQUEST>';
            $response = Http::withHeaders(['Content-Type' => 'text/xml; charset=utf-8'])->send('POST', $this->epay_posturl, [
            'body' => $request,
            ]);
            
            $cancel_status = 'Fail';
            if($response->successful())
            {
                $clean_xml = str_ireplace(['SOAP-ENV:', 'SOAP:'], '', $response);
            
                $xml = simplexml_load_string($clean_xml, "SimpleXMLElement", LIBXML_NOCDATA);
                $json = json_encode($xml);
                $response_json = json_decode($json,TRUE);
                
               $cancel_status = $response_json['RESULT']==0?'Reversed':'Fail';
               VoucherTransactionsModel::where('txn_id', $transaction_id)
               ->update([
               'response'=>'', 
               'txn_status'=>'Error', 
               ]);
              
             }else{
                $cancel_status = 'Failed';  
                $response_json = $response->body();
            }

            $save_transaction_response = new VoucherTransactionsModel;
            $save_transaction_response->txn_id = $txnid;
            $save_transaction_response->user_id = $this->user_id;
            $save_transaction_response->response = json_encode($response_json);
            $save_transaction_response->txn_status = $cancel_status;
            $save_transaction_response->transaction_type = 3;
            $save_transaction_response->request = $voucher_code;
            $save_transaction_response->save();


           
    }

    private function isValidVoucher(string $voucher_code):bool
    {
        $voucherPrefix = substr($voucher_code, 0, 2);
        if($voucherPrefix == self::VOUCHER_PREFIX)
        {
            return true;
        }
        return false;
    }
}