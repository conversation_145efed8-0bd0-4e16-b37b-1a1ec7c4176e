<?php

namespace App\Services;

use App\Models\DynamicGiftData;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use App\Models\Partners; 
use App\Services\EncryptDecrypt;
use Auth;

class Tillo
{
   
    protected $tillo_api_key, $tillo_api_secret, $tillo_accessurl,$user_id;
    public function __construct() {
        $this->tillo_api_key = \Config::get('values.tillo_api_key');
        $this->tillo_api_secret =\Config::get('values.tillo_api_secret');
        $this->tillo_accessurl =\Config::get('values.tillo_accessurl');
        date_default_timezone_set('GMT'); 
        $currentUser = Auth::user();
        $this->user_id = isset($currentUser)?$currentUser->id:0;
    }

    public function getBrandDetails ($slug){
      
        $slug = $slug ? $slug : ''; 
         $timestamp  =  strtotime(gmdate('Y-m-d H:i:s')) * 1000;
      
        if(!empty($slug)){
              $data = $this->tillo_api_key.'-GET-brands-'.$slug.'-'.$timestamp;
           
            
              $secretKey = $this->tillo_api_secret; // Replace with your actual secret key
            $signature = hash_hmac("sha256", $data, $secretKey);
              
             $url = urldecode($this->tillo_accessurl.'/brands?currency=GBP&detail=true&country=GB&brand='.$slug);
           
            $response = Http::withHeaders(
                [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'API-Key' => $this->tillo_api_key,
                    'Signature' =>  $signature,
                    'Timestamp' =>  $timestamp
                ])->send('GET',$url);
                 
                 if($response->successful()){
                   $apiData = json_decode($response->body());
                  //dd( $apiData ->data->brands->$slug->digital_denominations);
                   $denomination_values = isset( $apiData ->data->brands->$slug->digital_denominations) ? $apiData ->data->brands->$slug->digital_denominations : [];
                   
                   return $denomination_values;
                   
                }
        }
    }

    public function issueDigitalCard($basket)
    {
        $partner_data = Partners::where('id',$basket->partner_id)->first();
        $clientId = substr(str_shuffle(md5("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".time())),0,10);
        $cc = new EncryptDecrypt;
        $slug = $partner_data['slug'] ? $partner_data['slug'] : '';
        $amount = $basket['dynamic_partner_amount'] ?  $basket['dynamic_partner_amount'] : '';
        $timestamp = strtotime(gmdate('Y-m-d H:i:s')) * 1000; 
        
        $data = $this->tillo_api_key.'-POST-digital-issue-'.$clientId.'-'.$slug.'-GBP-'.$amount.'-'.$timestamp;
            
        $secretKey = $this->tillo_api_secret; // Replace with your actual secret key
        $signature = hash_hmac("sha256", $data, $secretKey);
        $url = urldecode($this->tillo_accessurl.'/digital/issue');
        
        $body = [
            'client_request_id' => $clientId,
            'brand' =>  $slug,
            'face_value' => [
                'amount'=> $amount, //
                'currency'=> "GBP"
            ],
            'delivery_method' => 'url',
            'fulfilment_by' => 'partner',
            'sector' => 'gift-card-mall',
        ];
                       
        $response = Http::withHeaders(
            [
                'Content-Type' => 'application/json; charset=utf-8',
                'Accept' => 'application/json',
                'API-Key' => $this->tillo_api_key,
                'Signature' =>  $signature,
                'Timestamp' =>  $timestamp
            ])->send('POST',$url,[
                'body' =>  json_encode($body)
            ]);

            $save_transaction_response = new DynamicGiftData;

            $last_id = DynamicGiftData::orderBy('id','desc')->first();
            $last_id_val = isset($last_id->id)?$last_id->id:0;
            $transaction_id = 'DGV_'.$last_id_val.'_'.time();

            $save_transaction_response->txn_id = $transaction_id;
            $save_transaction_response->user_id = $this->user_id;
            $save_transaction_response->partner_id = $basket->partner_id ? $basket->partner_id: '';
            $save_transaction_response->amount = $amount;
            $response_json = json_decode($response->body());
            
            if(!$response->successful()){
                $status = 'FAIL';  
                $save_transaction_response->response = json_encode($response_json);
                $save_transaction_response->txn_status = $status;
                $save_transaction_response->save();
                return array('status'=>'tilloError','code'=>$response_json->code);
            }

            $response_json->data->url = $cc->codeEncrypt($response_json->data->url);
            $status = 'Success';
            $save_transaction_response->response = json_encode($response_json);
            $save_transaction_response->txn_status = $status;
            $save_transaction_response->save();
            $dynamicGiftURL = isset($response_json->data->url) ? $response_json ->data->url : '';
            return array('gifturl'=>$dynamicGiftURL,'code'=>$response_json->code);

    }
}