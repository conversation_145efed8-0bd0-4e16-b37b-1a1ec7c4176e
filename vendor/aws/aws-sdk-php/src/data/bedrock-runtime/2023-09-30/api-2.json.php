<?php
// This file was auto-generated from sdk-root/src/data/bedrock-runtime/2023-09-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-09-30', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'bedrock-runtime', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Bedrock Runtime', 'serviceId' => 'Bedrock Runtime', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-runtime-2023-09-30', ], 'operations' => [ 'ApplyGuardrail' => [ 'name' => 'ApplyGuardrail', 'http' => [ 'method' => 'POST', 'requestUri' => '/guardrail/{guardrailIdentifier}/version/{guardrailVersion}/apply', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ApplyGuardrailRequest', ], 'output' => [ 'shape' => 'ApplyGuardrailResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'Converse' => [ 'name' => 'Converse', 'http' => [ 'method' => 'POST', 'requestUri' => '/model/{modelId}/converse', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConverseRequest', ], 'output' => [ 'shape' => 'ConverseResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ModelTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ModelNotReadyException', ], [ 'shape' => 'ModelErrorException', ], ], ], 'ConverseStream' => [ 'name' => 'ConverseStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/model/{modelId}/converse-stream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ConverseStreamRequest', ], 'output' => [ 'shape' => 'ConverseStreamResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ModelTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ModelNotReadyException', ], [ 'shape' => 'ModelErrorException', ], ], ], 'GetAsyncInvoke' => [ 'name' => 'GetAsyncInvoke', 'http' => [ 'method' => 'GET', 'requestUri' => '/async-invoke/{invocationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAsyncInvokeRequest', ], 'output' => [ 'shape' => 'GetAsyncInvokeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'InvokeModel' => [ 'name' => 'InvokeModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/model/{modelId}/invoke', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeModelRequest', ], 'output' => [ 'shape' => 'InvokeModelResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ModelTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ModelNotReadyException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ModelErrorException', ], ], ], 'InvokeModelWithResponseStream' => [ 'name' => 'InvokeModelWithResponseStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/model/{modelId}/invoke-with-response-stream', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeModelWithResponseStreamRequest', ], 'output' => [ 'shape' => 'InvokeModelWithResponseStreamResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ModelTimeoutException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ModelStreamErrorException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ModelNotReadyException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ModelErrorException', ], ], ], 'ListAsyncInvokes' => [ 'name' => 'ListAsyncInvokes', 'http' => [ 'method' => 'GET', 'requestUri' => '/async-invoke', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAsyncInvokesRequest', ], 'output' => [ 'shape' => 'ListAsyncInvokesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartAsyncInvoke' => [ 'name' => 'StartAsyncInvoke', 'http' => [ 'method' => 'POST', 'requestUri' => '/async-invoke', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAsyncInvokeRequest', ], 'output' => [ 'shape' => 'StartAsyncInvokeResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'AnyToolChoice' => [ 'type' => 'structure', 'members' => [], ], 'ApplyGuardrailRequest' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', 'guardrailVersion', 'source', 'content', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'uri', 'locationName' => 'guardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', 'location' => 'uri', 'locationName' => 'guardrailVersion', ], 'source' => [ 'shape' => 'GuardrailContentSource', ], 'content' => [ 'shape' => 'GuardrailContentBlockList', ], ], ], 'ApplyGuardrailResponse' => [ 'type' => 'structure', 'required' => [ 'usage', 'action', 'outputs', 'assessments', ], 'members' => [ 'usage' => [ 'shape' => 'GuardrailUsage', ], 'action' => [ 'shape' => 'GuardrailAction', ], 'outputs' => [ 'shape' => 'GuardrailOutputContentList', ], 'assessments' => [ 'shape' => 'GuardrailAssessmentList', ], 'guardrailCoverage' => [ 'shape' => 'GuardrailCoverage', ], ], ], 'AsyncInvokeArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:[a-z0-9\\-]+:bedrock:[a-z0-9\\-]*:[0-9]*:(provisioned-model|foundation-model)/.+', ], 'AsyncInvokeIdempotencyToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[!-~]*', ], 'AsyncInvokeIdentifier' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z_\\.\\-/0-9:]+', ], 'AsyncInvokeMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'sensitive' => true, ], 'AsyncInvokeOutputDataConfig' => [ 'type' => 'structure', 'members' => [ 's3OutputDataConfig' => [ 'shape' => 'AsyncInvokeS3OutputDataConfig', ], ], 'union' => true, ], 'AsyncInvokeS3OutputDataConfig' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'bucketOwner' => [ 'shape' => 'AccountId', ], ], ], 'AsyncInvokeStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Completed', 'Failed', ], ], 'AsyncInvokeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AsyncInvokeSummary', ], ], 'AsyncInvokeSummary' => [ 'type' => 'structure', 'required' => [ 'invocationArn', 'modelArn', 'submitTime', 'outputDataConfig', ], 'members' => [ 'invocationArn' => [ 'shape' => 'InvocationArn', ], 'modelArn' => [ 'shape' => 'AsyncInvokeArn', ], 'clientRequestToken' => [ 'shape' => 'AsyncInvokeIdempotencyToken', ], 'status' => [ 'shape' => 'AsyncInvokeStatus', ], 'failureMessage' => [ 'shape' => 'AsyncInvokeMessage', ], 'submitTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'outputDataConfig' => [ 'shape' => 'AsyncInvokeOutputDataConfig', ], ], ], 'AutoToolChoice' => [ 'type' => 'structure', 'members' => [], ], 'Blob' => [ 'type' => 'blob', ], 'Body' => [ 'type' => 'blob', 'max' => 25000000, 'min' => 0, 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ContentBlock' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], 'image' => [ 'shape' => 'ImageBlock', ], 'document' => [ 'shape' => 'DocumentBlock', ], 'video' => [ 'shape' => 'VideoBlock', ], 'toolUse' => [ 'shape' => 'ToolUseBlock', ], 'toolResult' => [ 'shape' => 'ToolResultBlock', ], 'guardContent' => [ 'shape' => 'GuardrailConverseContentBlock', ], 'reasoningContent' => [ 'shape' => 'ReasoningContentBlock', ], ], 'union' => true, ], 'ContentBlockDelta' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], 'toolUse' => [ 'shape' => 'ToolUseBlockDelta', ], 'reasoningContent' => [ 'shape' => 'ReasoningContentBlockDelta', ], ], 'union' => true, ], 'ContentBlockDeltaEvent' => [ 'type' => 'structure', 'required' => [ 'delta', 'contentBlockIndex', ], 'members' => [ 'delta' => [ 'shape' => 'ContentBlockDelta', ], 'contentBlockIndex' => [ 'shape' => 'NonNegativeInteger', ], ], 'event' => true, ], 'ContentBlockStart' => [ 'type' => 'structure', 'members' => [ 'toolUse' => [ 'shape' => 'ToolUseBlockStart', ], ], 'union' => true, ], 'ContentBlockStartEvent' => [ 'type' => 'structure', 'required' => [ 'start', 'contentBlockIndex', ], 'members' => [ 'start' => [ 'shape' => 'ContentBlockStart', ], 'contentBlockIndex' => [ 'shape' => 'NonNegativeInteger', ], ], 'event' => true, ], 'ContentBlockStopEvent' => [ 'type' => 'structure', 'required' => [ 'contentBlockIndex', ], 'members' => [ 'contentBlockIndex' => [ 'shape' => 'NonNegativeInteger', ], ], 'event' => true, ], 'ContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentBlock', ], ], 'ConversationRole' => [ 'type' => 'string', 'enum' => [ 'user', 'assistant', ], ], 'ConversationalModelId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|([0-9]{12}:imported-model/[a-z0-9]{12})|([0-9]{12}:provisioned-model/[a-z0-9]{12})|([0-9]{12}:(inference-profile|application-inference-profile)/[a-zA-Z0-9-:.]+)))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|(([0-9a-zA-Z][_-]?)+)|([a-zA-Z0-9-:.]+)|(^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:prompt/[0-9a-zA-Z]{10}(?::[0-9]{1,5})?))$|(^arn:aws:sagemaker:[a-z0-9-]+:[0-9]{12}:endpoint/[a-zA-Z0-9-]+$)|(^arn:aws(-[^:]+)?:bedrock:([0-9a-z-]{1,20}):([0-9]{12}):default-prompt-router/[a-zA-Z0-9-:.]+$)', ], 'ConverseMetrics' => [ 'type' => 'structure', 'required' => [ 'latencyMs', ], 'members' => [ 'latencyMs' => [ 'shape' => 'Long', ], ], ], 'ConverseOutput' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'Message', ], ], 'union' => true, ], 'ConverseRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', ], 'members' => [ 'modelId' => [ 'shape' => 'ConversationalModelId', 'location' => 'uri', 'locationName' => 'modelId', ], 'messages' => [ 'shape' => 'Messages', ], 'system' => [ 'shape' => 'SystemContentBlocks', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfiguration', ], 'toolConfig' => [ 'shape' => 'ToolConfiguration', ], 'guardrailConfig' => [ 'shape' => 'GuardrailConfiguration', ], 'additionalModelRequestFields' => [ 'shape' => 'Document', ], 'promptVariables' => [ 'shape' => 'PromptVariableMap', ], 'additionalModelResponseFieldPaths' => [ 'shape' => 'ConverseRequestAdditionalModelResponseFieldPathsList', ], 'requestMetadata' => [ 'shape' => 'RequestMetadata', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], ], 'ConverseRequestAdditionalModelResponseFieldPathsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConverseRequestAdditionalModelResponseFieldPathsListMemberString', ], 'max' => 10, 'min' => 0, ], 'ConverseRequestAdditionalModelResponseFieldPathsListMemberString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ConverseResponse' => [ 'type' => 'structure', 'required' => [ 'output', 'stopReason', 'usage', 'metrics', ], 'members' => [ 'output' => [ 'shape' => 'ConverseOutput', ], 'stopReason' => [ 'shape' => 'StopReason', ], 'usage' => [ 'shape' => 'TokenUsage', ], 'metrics' => [ 'shape' => 'ConverseMetrics', ], 'additionalModelResponseFields' => [ 'shape' => 'Document', ], 'trace' => [ 'shape' => 'ConverseTrace', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], ], 'ConverseStreamMetadataEvent' => [ 'type' => 'structure', 'required' => [ 'usage', 'metrics', ], 'members' => [ 'usage' => [ 'shape' => 'TokenUsage', ], 'metrics' => [ 'shape' => 'ConverseStreamMetrics', ], 'trace' => [ 'shape' => 'ConverseStreamTrace', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], 'event' => true, ], 'ConverseStreamMetrics' => [ 'type' => 'structure', 'required' => [ 'latencyMs', ], 'members' => [ 'latencyMs' => [ 'shape' => 'Long', ], ], ], 'ConverseStreamOutput' => [ 'type' => 'structure', 'members' => [ 'messageStart' => [ 'shape' => 'MessageStartEvent', ], 'contentBlockStart' => [ 'shape' => 'ContentBlockStartEvent', ], 'contentBlockDelta' => [ 'shape' => 'ContentBlockDeltaEvent', ], 'contentBlockStop' => [ 'shape' => 'ContentBlockStopEvent', ], 'messageStop' => [ 'shape' => 'MessageStopEvent', ], 'metadata' => [ 'shape' => 'ConverseStreamMetadataEvent', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'modelStreamErrorException' => [ 'shape' => 'ModelStreamErrorException', ], 'validationException' => [ 'shape' => 'ValidationException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'serviceUnavailableException' => [ 'shape' => 'ServiceUnavailableException', ], ], 'eventstream' => true, ], 'ConverseStreamRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', ], 'members' => [ 'modelId' => [ 'shape' => 'ConversationalModelId', 'location' => 'uri', 'locationName' => 'modelId', ], 'messages' => [ 'shape' => 'Messages', ], 'system' => [ 'shape' => 'SystemContentBlocks', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfiguration', ], 'toolConfig' => [ 'shape' => 'ToolConfiguration', ], 'guardrailConfig' => [ 'shape' => 'GuardrailStreamConfiguration', ], 'additionalModelRequestFields' => [ 'shape' => 'Document', ], 'promptVariables' => [ 'shape' => 'PromptVariableMap', ], 'additionalModelResponseFieldPaths' => [ 'shape' => 'ConverseStreamRequestAdditionalModelResponseFieldPathsList', ], 'requestMetadata' => [ 'shape' => 'RequestMetadata', ], 'performanceConfig' => [ 'shape' => 'PerformanceConfiguration', ], ], ], 'ConverseStreamRequestAdditionalModelResponseFieldPathsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConverseStreamRequestAdditionalModelResponseFieldPathsListMemberString', ], 'max' => 10, 'min' => 0, ], 'ConverseStreamRequestAdditionalModelResponseFieldPathsListMemberString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ConverseStreamResponse' => [ 'type' => 'structure', 'members' => [ 'stream' => [ 'shape' => 'ConverseStreamOutput', ], ], 'payload' => 'stream', ], 'ConverseStreamTrace' => [ 'type' => 'structure', 'members' => [ 'guardrail' => [ 'shape' => 'GuardrailTraceAssessment', ], 'promptRouter' => [ 'shape' => 'PromptRouterTrace', ], ], ], 'ConverseTrace' => [ 'type' => 'structure', 'members' => [ 'guardrail' => [ 'shape' => 'GuardrailTraceAssessment', ], 'promptRouter' => [ 'shape' => 'PromptRouterTrace', ], ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DocumentBlock' => [ 'type' => 'structure', 'required' => [ 'format', 'name', 'source', ], 'members' => [ 'format' => [ 'shape' => 'DocumentFormat', ], 'name' => [ 'shape' => 'DocumentBlockNameString', ], 'source' => [ 'shape' => 'DocumentSource', ], ], ], 'DocumentBlockNameString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, ], 'DocumentFormat' => [ 'type' => 'string', 'enum' => [ 'pdf', 'csv', 'doc', 'docx', 'xls', 'xlsx', 'html', 'txt', 'md', ], ], 'DocumentSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'DocumentSourceBytesBlob', ], ], 'union' => true, ], 'DocumentSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], 'GetAsyncInvokeRequest' => [ 'type' => 'structure', 'required' => [ 'invocationArn', ], 'members' => [ 'invocationArn' => [ 'shape' => 'InvocationArn', 'location' => 'uri', 'locationName' => 'invocationArn', ], ], ], 'GetAsyncInvokeResponse' => [ 'type' => 'structure', 'required' => [ 'invocationArn', 'modelArn', 'status', 'submitTime', 'outputDataConfig', ], 'members' => [ 'invocationArn' => [ 'shape' => 'InvocationArn', ], 'modelArn' => [ 'shape' => 'AsyncInvokeArn', ], 'clientRequestToken' => [ 'shape' => 'AsyncInvokeIdempotencyToken', ], 'status' => [ 'shape' => 'AsyncInvokeStatus', ], 'failureMessage' => [ 'shape' => 'AsyncInvokeMessage', ], 'submitTime' => [ 'shape' => 'Timestamp', ], 'lastModifiedTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'outputDataConfig' => [ 'shape' => 'AsyncInvokeOutputDataConfig', ], ], ], 'GuardrailAction' => [ 'type' => 'string', 'enum' => [ 'NONE', 'GUARDRAIL_INTERVENED', ], ], 'GuardrailAssessment' => [ 'type' => 'structure', 'members' => [ 'topicPolicy' => [ 'shape' => 'GuardrailTopicPolicyAssessment', ], 'contentPolicy' => [ 'shape' => 'GuardrailContentPolicyAssessment', ], 'wordPolicy' => [ 'shape' => 'GuardrailWordPolicyAssessment', ], 'sensitiveInformationPolicy' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAssessment', ], 'contextualGroundingPolicy' => [ 'shape' => 'GuardrailContextualGroundingPolicyAssessment', ], 'invocationMetrics' => [ 'shape' => 'GuardrailInvocationMetrics', ], ], ], 'GuardrailAssessmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailAssessment', ], ], 'GuardrailAssessmentListMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'GuardrailAssessmentList', ], ], 'GuardrailAssessmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'GuardrailAssessment', ], ], 'GuardrailConfiguration' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', 'guardrailVersion', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', ], 'trace' => [ 'shape' => 'GuardrailTrace', ], ], ], 'GuardrailContentBlock' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'GuardrailTextBlock', ], 'image' => [ 'shape' => 'GuardrailImageBlock', ], ], 'union' => true, ], 'GuardrailContentBlockList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentBlock', ], ], 'GuardrailContentFilter' => [ 'type' => 'structure', 'required' => [ 'type', 'confidence', 'action', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContentFilterType', ], 'confidence' => [ 'shape' => 'GuardrailContentFilterConfidence', ], 'filterStrength' => [ 'shape' => 'GuardrailContentFilterStrength', ], 'action' => [ 'shape' => 'GuardrailContentPolicyAction', ], ], ], 'GuardrailContentFilterConfidence' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', ], ], 'GuardrailContentFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentFilter', ], ], 'GuardrailContentFilterStrength' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', ], ], 'GuardrailContentFilterType' => [ 'type' => 'string', 'enum' => [ 'INSULTS', 'HATE', 'SEXUAL', 'VIOLENCE', 'MISCONDUCT', 'PROMPT_ATTACK', ], ], 'GuardrailContentPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailContentPolicyAssessment' => [ 'type' => 'structure', 'required' => [ 'filters', ], 'members' => [ 'filters' => [ 'shape' => 'GuardrailContentFilterList', ], ], ], 'GuardrailContentPolicyUnitsProcessed' => [ 'type' => 'integer', 'box' => true, ], 'GuardrailContentQualifier' => [ 'type' => 'string', 'enum' => [ 'grounding_source', 'query', 'guard_content', ], ], 'GuardrailContentQualifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentQualifier', ], ], 'GuardrailContentSource' => [ 'type' => 'string', 'enum' => [ 'INPUT', 'OUTPUT', ], ], 'GuardrailContextualGroundingFilter' => [ 'type' => 'structure', 'required' => [ 'type', 'threshold', 'score', 'action', ], 'members' => [ 'type' => [ 'shape' => 'GuardrailContextualGroundingFilterType', ], 'threshold' => [ 'shape' => 'GuardrailContextualGroundingFilterThresholdDouble', ], 'score' => [ 'shape' => 'GuardrailContextualGroundingFilterScoreDouble', ], 'action' => [ 'shape' => 'GuardrailContextualGroundingPolicyAction', ], ], ], 'GuardrailContextualGroundingFilterScoreDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'GuardrailContextualGroundingFilterThresholdDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'GuardrailContextualGroundingFilterType' => [ 'type' => 'string', 'enum' => [ 'GROUNDING', 'RELEVANCE', ], ], 'GuardrailContextualGroundingFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContextualGroundingFilter', ], ], 'GuardrailContextualGroundingPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', 'NONE', ], ], 'GuardrailContextualGroundingPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'GuardrailContextualGroundingFilters', ], ], ], 'GuardrailContextualGroundingPolicyUnitsProcessed' => [ 'type' => 'integer', 'box' => true, ], 'GuardrailConverseContentBlock' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'GuardrailConverseTextBlock', ], 'image' => [ 'shape' => 'GuardrailConverseImageBlock', ], ], 'union' => true, ], 'GuardrailConverseContentQualifier' => [ 'type' => 'string', 'enum' => [ 'grounding_source', 'query', 'guard_content', ], ], 'GuardrailConverseContentQualifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailConverseContentQualifier', ], ], 'GuardrailConverseImageBlock' => [ 'type' => 'structure', 'required' => [ 'format', 'source', ], 'members' => [ 'format' => [ 'shape' => 'GuardrailConverseImageFormat', ], 'source' => [ 'shape' => 'GuardrailConverseImageSource', ], ], 'sensitive' => true, ], 'GuardrailConverseImageFormat' => [ 'type' => 'string', 'enum' => [ 'png', 'jpeg', ], ], 'GuardrailConverseImageSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'GuardrailConverseImageSourceBytesBlob', ], ], 'sensitive' => true, 'union' => true, ], 'GuardrailConverseImageSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], 'GuardrailConverseTextBlock' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], 'qualifiers' => [ 'shape' => 'GuardrailConverseContentQualifierList', ], ], ], 'GuardrailCoverage' => [ 'type' => 'structure', 'members' => [ 'textCharacters' => [ 'shape' => 'GuardrailTextCharactersCoverage', ], 'images' => [ 'shape' => 'GuardrailImageCoverage', ], ], ], 'GuardrailCustomWord' => [ 'type' => 'structure', 'required' => [ 'match', 'action', ], 'members' => [ 'match' => [ 'shape' => 'String', ], 'action' => [ 'shape' => 'GuardrailWordPolicyAction', ], ], ], 'GuardrailCustomWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailCustomWord', ], ], 'GuardrailIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '(([a-z0-9]+)|(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:guardrail/[a-z0-9]+))', ], 'GuardrailImageBlock' => [ 'type' => 'structure', 'required' => [ 'format', 'source', ], 'members' => [ 'format' => [ 'shape' => 'GuardrailImageFormat', ], 'source' => [ 'shape' => 'GuardrailImageSource', ], ], 'sensitive' => true, ], 'GuardrailImageCoverage' => [ 'type' => 'structure', 'members' => [ 'guarded' => [ 'shape' => 'ImagesGuarded', ], 'total' => [ 'shape' => 'ImagesTotal', ], ], ], 'GuardrailImageFormat' => [ 'type' => 'string', 'enum' => [ 'png', 'jpeg', ], ], 'GuardrailImageSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'GuardrailImageSourceBytesBlob', ], ], 'sensitive' => true, 'union' => true, ], 'GuardrailImageSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], 'GuardrailInvocationMetrics' => [ 'type' => 'structure', 'members' => [ 'guardrailProcessingLatency' => [ 'shape' => 'GuardrailProcessingLatency', ], 'usage' => [ 'shape' => 'GuardrailUsage', ], 'guardrailCoverage' => [ 'shape' => 'GuardrailCoverage', ], ], ], 'GuardrailManagedWord' => [ 'type' => 'structure', 'required' => [ 'match', 'type', 'action', ], 'members' => [ 'match' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailManagedWordType', ], 'action' => [ 'shape' => 'GuardrailWordPolicyAction', ], ], ], 'GuardrailManagedWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailManagedWord', ], ], 'GuardrailManagedWordType' => [ 'type' => 'string', 'enum' => [ 'PROFANITY', ], ], 'GuardrailOutputContent' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'GuardrailOutputText', ], ], ], 'GuardrailOutputContentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailOutputContent', ], ], 'GuardrailOutputText' => [ 'type' => 'string', ], 'GuardrailPiiEntityFilter' => [ 'type' => 'structure', 'required' => [ 'match', 'type', 'action', ], 'members' => [ 'match' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailPiiEntityType', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAction', ], ], ], 'GuardrailPiiEntityFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPiiEntityFilter', ], ], 'GuardrailPiiEntityType' => [ 'type' => 'string', 'enum' => [ 'ADDRESS', 'AGE', 'AWS_ACCESS_KEY', 'AWS_SECRET_KEY', 'CA_HEALTH_NUMBER', 'CA_SOCIAL_INSURANCE_NUMBER', 'CREDIT_DEBIT_CARD_CVV', 'CREDIT_DEBIT_CARD_EXPIRY', 'CREDIT_DEBIT_CARD_NUMBER', 'DRIVER_ID', 'EMAIL', 'INTERNATIONAL_BANK_ACCOUNT_NUMBER', 'IP_ADDRESS', 'LICENSE_PLATE', 'MAC_ADDRESS', 'NAME', 'PASSWORD', 'PHONE', 'PIN', 'SWIFT_CODE', 'UK_NATIONAL_HEALTH_SERVICE_NUMBER', 'UK_NATIONAL_INSURANCE_NUMBER', 'UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER', 'URL', 'USERNAME', 'US_BANK_ACCOUNT_NUMBER', 'US_BANK_ROUTING_NUMBER', 'US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER', 'US_PASSPORT_NUMBER', 'US_SOCIAL_SECURITY_NUMBER', 'VEHICLE_IDENTIFICATION_NUMBER', ], ], 'GuardrailProcessingLatency' => [ 'type' => 'long', 'box' => true, ], 'GuardrailRegexFilter' => [ 'type' => 'structure', 'required' => [ 'action', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'match' => [ 'shape' => 'String', ], 'regex' => [ 'shape' => 'String', ], 'action' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAction', ], ], ], 'GuardrailRegexFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailRegexFilter', ], ], 'GuardrailSensitiveInformationPolicyAction' => [ 'type' => 'string', 'enum' => [ 'ANONYMIZED', 'BLOCKED', ], ], 'GuardrailSensitiveInformationPolicyAssessment' => [ 'type' => 'structure', 'required' => [ 'piiEntities', 'regexes', ], 'members' => [ 'piiEntities' => [ 'shape' => 'GuardrailPiiEntityFilterList', ], 'regexes' => [ 'shape' => 'GuardrailRegexFilterList', ], ], ], 'GuardrailSensitiveInformationPolicyFreeUnitsProcessed' => [ 'type' => 'integer', 'box' => true, ], 'GuardrailSensitiveInformationPolicyUnitsProcessed' => [ 'type' => 'integer', 'box' => true, ], 'GuardrailStreamConfiguration' => [ 'type' => 'structure', 'required' => [ 'guardrailIdentifier', 'guardrailVersion', ], 'members' => [ 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', ], 'trace' => [ 'shape' => 'GuardrailTrace', ], 'streamProcessingMode' => [ 'shape' => 'GuardrailStreamProcessingMode', ], ], ], 'GuardrailStreamProcessingMode' => [ 'type' => 'string', 'enum' => [ 'sync', 'async', ], ], 'GuardrailTextBlock' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], 'qualifiers' => [ 'shape' => 'GuardrailContentQualifierList', ], ], ], 'GuardrailTextCharactersCoverage' => [ 'type' => 'structure', 'members' => [ 'guarded' => [ 'shape' => 'TextCharactersGuarded', ], 'total' => [ 'shape' => 'TextCharactersTotal', ], ], ], 'GuardrailTopic' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'action', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailTopicType', ], 'action' => [ 'shape' => 'GuardrailTopicPolicyAction', ], ], ], 'GuardrailTopicList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopic', ], ], 'GuardrailTopicPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailTopicPolicyAssessment' => [ 'type' => 'structure', 'required' => [ 'topics', ], 'members' => [ 'topics' => [ 'shape' => 'GuardrailTopicList', ], ], ], 'GuardrailTopicPolicyUnitsProcessed' => [ 'type' => 'integer', 'box' => true, ], 'GuardrailTopicType' => [ 'type' => 'string', 'enum' => [ 'DENY', ], ], 'GuardrailTrace' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'GuardrailTraceAssessment' => [ 'type' => 'structure', 'members' => [ 'modelOutput' => [ 'shape' => 'ModelOutputs', ], 'inputAssessment' => [ 'shape' => 'GuardrailAssessmentMap', ], 'outputAssessments' => [ 'shape' => 'GuardrailAssessmentListMap', ], ], ], 'GuardrailUsage' => [ 'type' => 'structure', 'required' => [ 'topicPolicyUnits', 'contentPolicyUnits', 'wordPolicyUnits', 'sensitiveInformationPolicyUnits', 'sensitiveInformationPolicyFreeUnits', 'contextualGroundingPolicyUnits', ], 'members' => [ 'topicPolicyUnits' => [ 'shape' => 'GuardrailTopicPolicyUnitsProcessed', ], 'contentPolicyUnits' => [ 'shape' => 'GuardrailContentPolicyUnitsProcessed', ], 'wordPolicyUnits' => [ 'shape' => 'GuardrailWordPolicyUnitsProcessed', ], 'sensitiveInformationPolicyUnits' => [ 'shape' => 'GuardrailSensitiveInformationPolicyUnitsProcessed', ], 'sensitiveInformationPolicyFreeUnits' => [ 'shape' => 'GuardrailSensitiveInformationPolicyFreeUnitsProcessed', ], 'contextualGroundingPolicyUnits' => [ 'shape' => 'GuardrailContextualGroundingPolicyUnitsProcessed', ], ], ], 'GuardrailVersion' => [ 'type' => 'string', 'pattern' => '(([1-9][0-9]{0,7})|(DRAFT))', ], 'GuardrailWordPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailWordPolicyAssessment' => [ 'type' => 'structure', 'required' => [ 'customWords', 'managedWordLists', ], 'members' => [ 'customWords' => [ 'shape' => 'GuardrailCustomWordList', ], 'managedWordLists' => [ 'shape' => 'GuardrailManagedWordList', ], ], ], 'GuardrailWordPolicyUnitsProcessed' => [ 'type' => 'integer', 'box' => true, ], 'ImageBlock' => [ 'type' => 'structure', 'required' => [ 'format', 'source', ], 'members' => [ 'format' => [ 'shape' => 'ImageFormat', ], 'source' => [ 'shape' => 'ImageSource', ], ], ], 'ImageFormat' => [ 'type' => 'string', 'enum' => [ 'png', 'jpeg', 'gif', 'webp', ], ], 'ImageSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'ImageSourceBytesBlob', ], ], 'union' => true, ], 'ImageSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], 'ImagesGuarded' => [ 'type' => 'integer', 'box' => true, ], 'ImagesTotal' => [ 'type' => 'integer', 'box' => true, ], 'InferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maxTokens' => [ 'shape' => 'InferenceConfigurationMaxTokensInteger', ], 'temperature' => [ 'shape' => 'InferenceConfigurationTemperatureFloat', ], 'topP' => [ 'shape' => 'InferenceConfigurationTopPFloat', ], 'stopSequences' => [ 'shape' => 'InferenceConfigurationStopSequencesList', ], ], ], 'InferenceConfigurationMaxTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'InferenceConfigurationStopSequencesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 4, 'min' => 0, ], 'InferenceConfigurationTemperatureFloat' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'InferenceConfigurationTopPFloat' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvocationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:async-invoke/[a-z0-9]{12}', ], 'InvokeModelIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|([0-9]{12}:imported-model/[a-z0-9]{12})|([0-9]{12}:provisioned-model/[a-z0-9]{12})|([0-9]{12}:(inference-profile|application-inference-profile)/[a-zA-Z0-9-:.]+)))|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|(([0-9a-zA-Z][_-]?)+)|([a-zA-Z0-9-:.]+)$|(^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:prompt/[0-9a-zA-Z]{10}(?::[0-9]{1,5})?))$|(^arn:aws:sagemaker:[a-z0-9-]+:[0-9]{12}:endpoint/[a-zA-Z0-9-]+$)|(^arn:aws(-[^:]+)?:bedrock:([0-9a-z-]{1,20}):([0-9]{12}):default-prompt-router/[a-zA-Z0-9-:.]+$)', ], 'InvokeModelRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', ], 'members' => [ 'body' => [ 'shape' => 'Body', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'accept' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'Accept', ], 'modelId' => [ 'shape' => 'InvokeModelIdentifier', 'location' => 'uri', 'locationName' => 'modelId', ], 'trace' => [ 'shape' => 'Trace', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-Trace', ], 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-GuardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-GuardrailVersion', ], 'performanceConfigLatency' => [ 'shape' => 'PerformanceConfigLatency', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-PerformanceConfig-Latency', ], ], 'payload' => 'body', ], 'InvokeModelResponse' => [ 'type' => 'structure', 'required' => [ 'body', 'contentType', ], 'members' => [ 'body' => [ 'shape' => 'Body', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'performanceConfigLatency' => [ 'shape' => 'PerformanceConfigLatency', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-PerformanceConfig-Latency', ], ], 'payload' => 'body', ], 'InvokeModelWithResponseStreamRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', ], 'members' => [ 'body' => [ 'shape' => 'Body', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'Content-Type', ], 'accept' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-Accept', ], 'modelId' => [ 'shape' => 'InvokeModelIdentifier', 'location' => 'uri', 'locationName' => 'modelId', ], 'trace' => [ 'shape' => 'Trace', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-Trace', ], 'guardrailIdentifier' => [ 'shape' => 'GuardrailIdentifier', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-GuardrailIdentifier', ], 'guardrailVersion' => [ 'shape' => 'GuardrailVersion', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-GuardrailVersion', ], 'performanceConfigLatency' => [ 'shape' => 'PerformanceConfigLatency', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-PerformanceConfig-Latency', ], ], 'payload' => 'body', ], 'InvokeModelWithResponseStreamResponse' => [ 'type' => 'structure', 'required' => [ 'body', 'contentType', ], 'members' => [ 'body' => [ 'shape' => 'ResponseStream', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-Content-Type', ], 'performanceConfigLatency' => [ 'shape' => 'PerformanceConfigLatency', 'location' => 'header', 'locationName' => 'X-Amzn-Bedrock-PerformanceConfig-Latency', ], ], 'payload' => 'body', ], 'InvokedModelId' => [ 'type' => 'string', 'pattern' => '(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}::foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([a-z0-9-]{1,63}[.]){0,2}[a-z0-9-]{1,63}([:][a-z0-9-]{1,63}){0,2})|(arn:aws(|-us-gov|-cn|-iso|-iso-b):bedrock:(|[0-9a-z-]{1,20}):(|[0-9]{12}):inference-profile/[a-zA-Z0-9-:.]+)', ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws(-[^:]+)?:kms:[a-zA-Z0-9-]*:[0-9]{12}:((key/[a-zA-Z0-9-]{36})|(alias/[a-zA-Z0-9-_/]+))', ], 'ListAsyncInvokesRequest' => [ 'type' => 'structure', 'members' => [ 'submitTimeAfter' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'submitTimeAfter', ], 'submitTimeBefore' => [ 'shape' => 'Timestamp', 'location' => 'querystring', 'locationName' => 'submitTimeBefore', ], 'statusEquals' => [ 'shape' => 'AsyncInvokeStatus', 'location' => 'querystring', 'locationName' => 'statusEquals', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'sortBy' => [ 'shape' => 'SortAsyncInvocationBy', 'location' => 'querystring', 'locationName' => 'sortBy', ], 'sortOrder' => [ 'shape' => 'SortOrder', 'location' => 'querystring', 'locationName' => 'sortOrder', ], ], ], 'ListAsyncInvokesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'asyncInvokeSummaries' => [ 'shape' => 'AsyncInvokeSummaries', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'Message' => [ 'type' => 'structure', 'required' => [ 'role', 'content', ], 'members' => [ 'role' => [ 'shape' => 'ConversationRole', ], 'content' => [ 'shape' => 'ContentBlocks', ], ], ], 'MessageStartEvent' => [ 'type' => 'structure', 'required' => [ 'role', ], 'members' => [ 'role' => [ 'shape' => 'ConversationRole', ], ], 'event' => true, ], 'MessageStopEvent' => [ 'type' => 'structure', 'required' => [ 'stopReason', ], 'members' => [ 'stopReason' => [ 'shape' => 'StopReason', ], 'additionalModelResponseFields' => [ 'shape' => 'Document', ], ], 'event' => true, ], 'Messages' => [ 'type' => 'list', 'member' => [ 'shape' => 'Message', ], ], 'MimeType' => [ 'type' => 'string', ], 'ModelErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'originalStatusCode' => [ 'shape' => 'StatusCode', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'ModelInputPayload' => [ 'type' => 'structure', 'members' => [], 'document' => true, 'sensitive' => true, ], 'ModelNotReadyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ModelOutputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailOutputText', ], ], 'ModelStreamErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'originalStatusCode' => [ 'shape' => 'StatusCode', ], 'originalMessage' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'ModelTimeoutException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 408, 'senderFault' => true, ], 'exception' => true, ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]*', ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NonNegativeInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '\\S*', ], 'PartBody' => [ 'type' => 'blob', 'max' => 1000000, 'min' => 0, 'sensitive' => true, ], 'PayloadPart' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'PartBody', ], ], 'event' => true, 'sensitive' => true, ], 'PerformanceConfigLatency' => [ 'type' => 'string', 'enum' => [ 'standard', 'optimized', ], ], 'PerformanceConfiguration' => [ 'type' => 'structure', 'members' => [ 'latency' => [ 'shape' => 'PerformanceConfigLatency', ], ], ], 'PromptRouterTrace' => [ 'type' => 'structure', 'members' => [ 'invokedModelId' => [ 'shape' => 'InvokedModelId', ], ], ], 'PromptVariableMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'PromptVariableValues', ], 'sensitive' => true, ], 'PromptVariableValues' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'union' => true, ], 'ReasoningContentBlock' => [ 'type' => 'structure', 'members' => [ 'reasoningText' => [ 'shape' => 'ReasoningTextBlock', ], 'redactedContent' => [ 'shape' => 'Blob', ], ], 'sensitive' => true, 'union' => true, ], 'ReasoningContentBlockDelta' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'String', ], 'redactedContent' => [ 'shape' => 'Blob', ], 'signature' => [ 'shape' => 'String', ], ], 'sensitive' => true, 'union' => true, ], 'ReasoningTextBlock' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], 'signature' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RequestMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'RequestMetadataKeyString', ], 'value' => [ 'shape' => 'RequestMetadataValueString', ], 'max' => 16, 'min' => 1, 'sensitive' => true, ], 'RequestMetadataKeyString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s:_@$#=/+,-.]{1,256}', ], 'RequestMetadataValueString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9\\s:_@$#=/+,-.]{0,256}', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseStream' => [ 'type' => 'structure', 'members' => [ 'chunk' => [ 'shape' => 'PayloadPart', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'modelStreamErrorException' => [ 'shape' => 'ModelStreamErrorException', ], 'validationException' => [ 'shape' => 'ValidationException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'modelTimeoutException' => [ 'shape' => 'ModelTimeoutException', ], 'serviceUnavailableException' => [ 'shape' => 'ServiceUnavailableException', ], ], 'eventstream' => true, ], 'S3Location' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3Uri', ], 'bucketOwner' => [ 'shape' => 'AccountId', ], ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SortAsyncInvocationBy' => [ 'type' => 'string', 'enum' => [ 'SubmissionTime', ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'Ascending', 'Descending', ], ], 'SpecificToolChoice' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'ToolName', ], ], ], 'StartAsyncInvokeRequest' => [ 'type' => 'structure', 'required' => [ 'modelId', 'modelInput', 'outputDataConfig', ], 'members' => [ 'clientRequestToken' => [ 'shape' => 'AsyncInvokeIdempotencyToken', 'idempotencyToken' => true, ], 'modelId' => [ 'shape' => 'AsyncInvokeIdentifier', ], 'modelInput' => [ 'shape' => 'ModelInputPayload', ], 'outputDataConfig' => [ 'shape' => 'AsyncInvokeOutputDataConfig', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'StartAsyncInvokeResponse' => [ 'type' => 'structure', 'required' => [ 'invocationArn', ], 'members' => [ 'invocationArn' => [ 'shape' => 'InvocationArn', ], ], ], 'StatusCode' => [ 'type' => 'integer', 'box' => true, 'max' => 599, 'min' => 100, ], 'StopReason' => [ 'type' => 'string', 'enum' => [ 'end_turn', 'tool_use', 'max_tokens', 'stop_sequence', 'guardrail_intervened', 'content_filtered', ], ], 'String' => [ 'type' => 'string', ], 'SystemContentBlock' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'NonEmptyString', ], 'guardContent' => [ 'shape' => 'GuardrailConverseContentBlock', ], ], 'union' => true, ], 'SystemContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'SystemContentBlock', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9\\s._:/=+@-]*', ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9\\s._:/=+@-]*', ], 'TextCharactersGuarded' => [ 'type' => 'integer', 'box' => true, ], 'TextCharactersTotal' => [ 'type' => 'integer', 'box' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TokenUsage' => [ 'type' => 'structure', 'required' => [ 'inputTokens', 'outputTokens', 'totalTokens', ], 'members' => [ 'inputTokens' => [ 'shape' => 'TokenUsageInputTokensInteger', ], 'outputTokens' => [ 'shape' => 'TokenUsageOutputTokensInteger', ], 'totalTokens' => [ 'shape' => 'TokenUsageTotalTokensInteger', ], ], ], 'TokenUsageInputTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'TokenUsageOutputTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'TokenUsageTotalTokensInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'Tool' => [ 'type' => 'structure', 'members' => [ 'toolSpec' => [ 'shape' => 'ToolSpecification', ], ], 'union' => true, ], 'ToolChoice' => [ 'type' => 'structure', 'members' => [ 'auto' => [ 'shape' => 'AutoToolChoice', ], 'any' => [ 'shape' => 'AnyToolChoice', ], 'tool' => [ 'shape' => 'SpecificToolChoice', ], ], 'union' => true, ], 'ToolConfiguration' => [ 'type' => 'structure', 'required' => [ 'tools', ], 'members' => [ 'tools' => [ 'shape' => 'ToolConfigurationToolsList', ], 'toolChoice' => [ 'shape' => 'ToolChoice', ], ], ], 'ToolConfigurationToolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tool', ], 'min' => 1, ], 'ToolInputSchema' => [ 'type' => 'structure', 'members' => [ 'json' => [ 'shape' => 'Document', ], ], 'union' => true, ], 'ToolName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ToolResultBlock' => [ 'type' => 'structure', 'required' => [ 'toolUseId', 'content', ], 'members' => [ 'toolUseId' => [ 'shape' => 'ToolUseId', ], 'content' => [ 'shape' => 'ToolResultContentBlocks', ], 'status' => [ 'shape' => 'ToolResultStatus', ], ], ], 'ToolResultContentBlock' => [ 'type' => 'structure', 'members' => [ 'json' => [ 'shape' => 'Document', ], 'text' => [ 'shape' => 'String', ], 'image' => [ 'shape' => 'ImageBlock', ], 'document' => [ 'shape' => 'DocumentBlock', ], 'video' => [ 'shape' => 'VideoBlock', ], ], 'union' => true, ], 'ToolResultContentBlocks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ToolResultContentBlock', ], ], 'ToolResultStatus' => [ 'type' => 'string', 'enum' => [ 'success', 'error', ], ], 'ToolSpecification' => [ 'type' => 'structure', 'required' => [ 'name', 'inputSchema', ], 'members' => [ 'name' => [ 'shape' => 'ToolName', ], 'description' => [ 'shape' => 'NonEmptyString', ], 'inputSchema' => [ 'shape' => 'ToolInputSchema', ], ], ], 'ToolUseBlock' => [ 'type' => 'structure', 'required' => [ 'toolUseId', 'name', 'input', ], 'members' => [ 'toolUseId' => [ 'shape' => 'ToolUseId', ], 'name' => [ 'shape' => 'ToolName', ], 'input' => [ 'shape' => 'Document', ], ], ], 'ToolUseBlockDelta' => [ 'type' => 'structure', 'required' => [ 'input', ], 'members' => [ 'input' => [ 'shape' => 'String', ], ], ], 'ToolUseBlockStart' => [ 'type' => 'structure', 'required' => [ 'toolUseId', 'name', ], 'members' => [ 'toolUseId' => [ 'shape' => 'ToolUseId', ], 'name' => [ 'shape' => 'ToolName', ], ], ], 'ToolUseId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'Trace' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VideoBlock' => [ 'type' => 'structure', 'required' => [ 'format', 'source', ], 'members' => [ 'format' => [ 'shape' => 'VideoFormat', ], 'source' => [ 'shape' => 'VideoSource', ], ], ], 'VideoFormat' => [ 'type' => 'string', 'enum' => [ 'mkv', 'mov', 'mp4', 'webm', 'flv', 'mpeg', 'mpg', 'wmv', 'three_gp', ], ], 'VideoSource' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'VideoSourceBytesBlob', ], 's3Location' => [ 'shape' => 'S3Location', ], ], 'union' => true, ], 'VideoSourceBytesBlob' => [ 'type' => 'blob', 'min' => 1, ], ],];
