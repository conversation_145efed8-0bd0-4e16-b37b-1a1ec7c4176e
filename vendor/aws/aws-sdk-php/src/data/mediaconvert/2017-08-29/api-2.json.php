<?php
// This file was auto-generated from sdk-root/src/data/mediaconvert/2017-08-29/api-2.json
return [ 'metadata' => [ 'apiVersion' => '2017-08-29', 'endpointPrefix' => 'mediaconvert', 'signingName' => 'mediaconvert', 'serviceFullName' => 'AWS Elemental MediaConvert', 'serviceId' => 'MediaConvert', 'protocol' => 'rest-json', 'jsonVersion' => '1.1', 'uid' => 'mediaconvert-2017-08-29', 'signatureVersion' => 'v4', 'serviceAbbreviation' => 'MediaConvert', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateCertificate' => [ 'name' => 'AssociateCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/certificates', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AssociateCertificateRequest', ], 'output' => [ 'shape' => 'AssociateCertificateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CancelJob' => [ 'name' => 'CancelJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-08-29/jobs/{id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CancelJobRequest', ], 'output' => [ 'shape' => 'CancelJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/jobs', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateJobTemplate' => [ 'name' => 'CreateJobTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/jobTemplates', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateJobTemplateRequest', ], 'output' => [ 'shape' => 'CreateJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreatePreset' => [ 'name' => 'CreatePreset', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/presets', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePresetRequest', ], 'output' => [ 'shape' => 'CreatePresetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'CreateQueue' => [ 'name' => 'CreateQueue', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/queues', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateQueueRequest', ], 'output' => [ 'shape' => 'CreateQueueResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteJobTemplate' => [ 'name' => 'DeleteJobTemplate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-08-29/jobTemplates/{name}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteJobTemplateRequest', ], 'output' => [ 'shape' => 'DeleteJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeletePolicy' => [ 'name' => 'DeletePolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-08-29/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePolicyRequest', ], 'output' => [ 'shape' => 'DeletePolicyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeletePreset' => [ 'name' => 'DeletePreset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-08-29/presets/{name}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeletePresetRequest', ], 'output' => [ 'shape' => 'DeletePresetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteQueue' => [ 'name' => 'DeleteQueue', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-08-29/queues/{name}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteQueueRequest', ], 'output' => [ 'shape' => 'DeleteQueueResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'DescribeEndpoints' => [ 'name' => 'DescribeEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/endpoints', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeEndpointsRequest', ], 'output' => [ 'shape' => 'DescribeEndpointsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'DescribeEndpoints and account specific endpoints are no longer required. We recommend that you send your requests directly to the regional endpoint instead.', ], 'DisassociateCertificate' => [ 'name' => 'DisassociateCertificate', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2017-08-29/certificates/{arn}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DisassociateCertificateRequest', ], 'output' => [ 'shape' => 'DisassociateCertificateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/jobs/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetJobTemplate' => [ 'name' => 'GetJobTemplate', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/jobTemplates/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetJobTemplateRequest', ], 'output' => [ 'shape' => 'GetJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPolicyRequest', ], 'output' => [ 'shape' => 'GetPolicyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetPreset' => [ 'name' => 'GetPreset', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/presets/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPresetRequest', ], 'output' => [ 'shape' => 'GetPresetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetQueue' => [ 'name' => 'GetQueue', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/queues/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetQueueRequest', ], 'output' => [ 'shape' => 'GetQueueResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListJobTemplates' => [ 'name' => 'ListJobTemplates', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/jobTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobTemplatesRequest', ], 'output' => [ 'shape' => 'ListJobTemplatesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListJobs' => [ 'name' => 'ListJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListJobsRequest', ], 'output' => [ 'shape' => 'ListJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListPresets' => [ 'name' => 'ListPresets', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/presets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPresetsRequest', ], 'output' => [ 'shape' => 'ListPresetsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListQueues' => [ 'name' => 'ListQueues', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/queues', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListQueuesRequest', ], 'output' => [ 'shape' => 'ListQueuesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListVersions' => [ 'name' => 'ListVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/versions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVersionsRequest', ], 'output' => [ 'shape' => 'ListVersionsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'Probe' => [ 'name' => 'Probe', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/probe', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ProbeRequest', ], 'output' => [ 'shape' => 'ProbeResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'PutPolicy' => [ 'name' => 'PutPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2017-08-29/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPolicyRequest', ], 'output' => [ 'shape' => 'PutPolicyResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'SearchJobs' => [ 'name' => 'SearchJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/2017-08-29/search', 'responseCode' => 200, ], 'input' => [ 'shape' => 'SearchJobsRequest', ], 'output' => [ 'shape' => 'SearchJobsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/2017-08-29/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2017-08-29/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateJobTemplate' => [ 'name' => 'UpdateJobTemplate', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2017-08-29/jobTemplates/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateJobTemplateRequest', ], 'output' => [ 'shape' => 'UpdateJobTemplateResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdatePreset' => [ 'name' => 'UpdatePreset', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2017-08-29/presets/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePresetRequest', ], 'output' => [ 'shape' => 'UpdatePresetResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], 'UpdateQueue' => [ 'name' => 'UpdateQueue', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2017-08-29/queues/{name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateQueueRequest', ], 'output' => [ 'shape' => 'UpdateQueueResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'InternalServerErrorException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AacAudioDescriptionBroadcasterMix' => [ 'type' => 'string', 'enum' => [ 'BROADCASTER_MIXED_AD', 'NORMAL', ], ], 'AacCodecProfile' => [ 'type' => 'string', 'enum' => [ 'LC', 'HEV1', 'HEV2', ], ], 'AacCodingMode' => [ 'type' => 'string', 'enum' => [ 'AD_RECEIVER_MIX', 'CODING_MODE_1_0', 'CODING_MODE_1_1', 'CODING_MODE_2_0', 'CODING_MODE_5_1', ], ], 'AacRateControlMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'VBR', ], ], 'AacRawFormat' => [ 'type' => 'string', 'enum' => [ 'LATM_LOAS', 'NONE', ], ], 'AacSettings' => [ 'type' => 'structure', 'members' => [ 'AudioDescriptionBroadcasterMix' => [ 'shape' => 'AacAudioDescriptionBroadcasterMix', 'locationName' => 'audioDescriptionBroadcasterMix', ], 'Bitrate' => [ 'shape' => '__integerMin6000Max1024000', 'locationName' => 'bitrate', ], 'CodecProfile' => [ 'shape' => 'AacCodecProfile', 'locationName' => 'codecProfile', ], 'CodingMode' => [ 'shape' => 'AacCodingMode', 'locationName' => 'codingMode', ], 'RateControlMode' => [ 'shape' => 'AacRateControlMode', 'locationName' => 'rateControlMode', ], 'RawFormat' => [ 'shape' => 'AacRawFormat', 'locationName' => 'rawFormat', ], 'SampleRate' => [ 'shape' => '__integerMin8000Max96000', 'locationName' => 'sampleRate', ], 'Specification' => [ 'shape' => 'AacSpecification', 'locationName' => 'specification', ], 'VbrQuality' => [ 'shape' => 'AacVbrQuality', 'locationName' => 'vbrQuality', ], ], ], 'AacSpecification' => [ 'type' => 'string', 'enum' => [ 'MPEG2', 'MPEG4', ], ], 'AacVbrQuality' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM_LOW', 'MEDIUM_HIGH', 'HIGH', ], ], 'Ac3BitstreamMode' => [ 'type' => 'string', 'enum' => [ 'COMPLETE_MAIN', 'COMMENTARY', 'DIALOGUE', 'EMERGENCY', 'HEARING_IMPAIRED', 'MUSIC_AND_EFFECTS', 'VISUALLY_IMPAIRED', 'VOICE_OVER', ], ], 'Ac3CodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_1_0', 'CODING_MODE_1_1', 'CODING_MODE_2_0', 'CODING_MODE_3_2_LFE', ], ], 'Ac3DynamicRangeCompressionLine' => [ 'type' => 'string', 'enum' => [ 'FILM_STANDARD', 'FILM_LIGHT', 'MUSIC_STANDARD', 'MUSIC_LIGHT', 'SPEECH', 'NONE', ], ], 'Ac3DynamicRangeCompressionProfile' => [ 'type' => 'string', 'enum' => [ 'FILM_STANDARD', 'NONE', ], ], 'Ac3DynamicRangeCompressionRf' => [ 'type' => 'string', 'enum' => [ 'FILM_STANDARD', 'FILM_LIGHT', 'MUSIC_STANDARD', 'MUSIC_LIGHT', 'SPEECH', 'NONE', ], ], 'Ac3LfeFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Ac3MetadataControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'Ac3Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin64000Max640000', 'locationName' => 'bitrate', ], 'BitstreamMode' => [ 'shape' => 'Ac3BitstreamMode', 'locationName' => 'bitstreamMode', ], 'CodingMode' => [ 'shape' => 'Ac3CodingMode', 'locationName' => 'codingMode', ], 'Dialnorm' => [ 'shape' => '__integerMin1Max31', 'locationName' => 'dialnorm', ], 'DynamicRangeCompressionLine' => [ 'shape' => 'Ac3DynamicRangeCompressionLine', 'locationName' => 'dynamicRangeCompressionLine', ], 'DynamicRangeCompressionProfile' => [ 'shape' => 'Ac3DynamicRangeCompressionProfile', 'locationName' => 'dynamicRangeCompressionProfile', ], 'DynamicRangeCompressionRf' => [ 'shape' => 'Ac3DynamicRangeCompressionRf', 'locationName' => 'dynamicRangeCompressionRf', ], 'LfeFilter' => [ 'shape' => 'Ac3LfeFilter', 'locationName' => 'lfeFilter', ], 'MetadataControl' => [ 'shape' => 'Ac3MetadataControl', 'locationName' => 'metadataControl', ], 'SampleRate' => [ 'shape' => '__integerMin48000Max48000', 'locationName' => 'sampleRate', ], ], ], 'AccelerationMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'PREFERRED', ], ], 'AccelerationSettings' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'AccelerationMode', 'locationName' => 'mode', ], ], 'required' => [ 'Mode', ], ], 'AccelerationStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_APPLICABLE', 'IN_PROGRESS', 'ACCELERATED', 'NOT_ACCELERATED', ], ], 'AdvancedInputFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AdvancedInputFilterAddTexture' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AdvancedInputFilterSettings' => [ 'type' => 'structure', 'members' => [ 'AddTexture' => [ 'shape' => 'AdvancedInputFilterAddTexture', 'locationName' => 'addTexture', ], 'Sharpening' => [ 'shape' => 'AdvancedInputFilterSharpen', 'locationName' => 'sharpening', ], ], ], 'AdvancedInputFilterSharpen' => [ 'type' => 'string', 'enum' => [ 'OFF', 'LOW', 'HIGH', ], ], 'AfdSignaling' => [ 'type' => 'string', 'enum' => [ 'NONE', 'AUTO', 'FIXED', ], ], 'AiffSettings' => [ 'type' => 'structure', 'members' => [ 'BitDepth' => [ 'shape' => '__integerMin16Max24', 'locationName' => 'bitDepth', ], 'Channels' => [ 'shape' => '__integerMin1Max64', 'locationName' => 'channels', ], 'SampleRate' => [ 'shape' => '__integerMin8000Max192000', 'locationName' => 'sampleRate', ], ], ], 'AllowedRenditionSize' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'height', ], 'Required' => [ 'shape' => 'RequiredFlag', 'locationName' => 'required', ], 'Width' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'width', ], ], ], 'AlphaBehavior' => [ 'type' => 'string', 'enum' => [ 'DISCARD', 'REMAP_TO_LUMA', ], ], 'AncillaryConvert608To708' => [ 'type' => 'string', 'enum' => [ 'UPCONVERT', 'DISABLED', ], ], 'AncillarySourceSettings' => [ 'type' => 'structure', 'members' => [ 'Convert608To708' => [ 'shape' => 'AncillaryConvert608To708', 'locationName' => 'convert608To708', ], 'SourceAncillaryChannelNumber' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'sourceAncillaryChannelNumber', ], 'TerminateCaptions' => [ 'shape' => 'AncillaryTerminateCaptions', 'locationName' => 'terminateCaptions', ], ], ], 'AncillaryTerminateCaptions' => [ 'type' => 'string', 'enum' => [ 'END_OF_INPUT', 'DISABLED', ], ], 'AntiAlias' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'AssociateCertificateRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], ], 'required' => [ 'Arn', ], ], 'AssociateCertificateResponse' => [ 'type' => 'structure', 'members' => [], ], 'AudioChannelTag' => [ 'type' => 'string', 'enum' => [ 'L', 'R', 'C', 'LFE', 'LS', 'RS', 'LC', 'RC', 'CS', 'LSD', 'RSD', 'TCS', 'VHL', 'VHC', 'VHR', 'TBL', 'TBC', 'TBR', 'RSL', 'RSR', 'LW', 'RW', 'LFE2', 'LT', 'RT', 'HI', 'NAR', 'M', ], ], 'AudioChannelTaggingSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelTag' => [ 'shape' => 'AudioChannelTag', 'locationName' => 'channelTag', ], 'ChannelTags' => [ 'shape' => '__listOfAudioChannelTag', 'locationName' => 'channelTags', ], ], ], 'AudioCodec' => [ 'type' => 'string', 'enum' => [ 'AAC', 'MP2', 'MP3', 'WAV', 'AIFF', 'AC3', 'EAC3', 'EAC3_ATMOS', 'VORBIS', 'OPUS', 'PASSTHROUGH', 'FLAC', ], ], 'AudioCodecSettings' => [ 'type' => 'structure', 'members' => [ 'AacSettings' => [ 'shape' => 'AacSettings', 'locationName' => 'aacSettings', ], 'Ac3Settings' => [ 'shape' => 'Ac3Settings', 'locationName' => 'ac3Settings', ], 'AiffSettings' => [ 'shape' => 'AiffSettings', 'locationName' => 'aiffSettings', ], 'Codec' => [ 'shape' => 'AudioCodec', 'locationName' => 'codec', ], 'Eac3AtmosSettings' => [ 'shape' => 'Eac3AtmosSettings', 'locationName' => 'eac3AtmosSettings', ], 'Eac3Settings' => [ 'shape' => 'Eac3Settings', 'locationName' => 'eac3Settings', ], 'FlacSettings' => [ 'shape' => 'FlacSettings', 'locationName' => 'flacSettings', ], 'Mp2Settings' => [ 'shape' => 'Mp2Settings', 'locationName' => 'mp2Settings', ], 'Mp3Settings' => [ 'shape' => 'Mp3Settings', 'locationName' => 'mp3Settings', ], 'OpusSettings' => [ 'shape' => 'OpusSettings', 'locationName' => 'opusSettings', ], 'VorbisSettings' => [ 'shape' => 'VorbisSettings', 'locationName' => 'vorbisSettings', ], 'WavSettings' => [ 'shape' => 'WavSettings', 'locationName' => 'wavSettings', ], ], ], 'AudioDefaultSelection' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'NOT_DEFAULT', ], ], 'AudioDescription' => [ 'type' => 'structure', 'members' => [ 'AudioChannelTaggingSettings' => [ 'shape' => 'AudioChannelTaggingSettings', 'locationName' => 'audioChannelTaggingSettings', ], 'AudioNormalizationSettings' => [ 'shape' => 'AudioNormalizationSettings', 'locationName' => 'audioNormalizationSettings', ], 'AudioSourceName' => [ 'shape' => '__stringMax2048', 'locationName' => 'audioSourceName', ], 'AudioType' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'audioType', ], 'AudioTypeControl' => [ 'shape' => 'AudioTypeControl', 'locationName' => 'audioTypeControl', ], 'CodecSettings' => [ 'shape' => 'AudioCodecSettings', 'locationName' => 'codecSettings', ], 'CustomLanguageCode' => [ 'shape' => '__stringPatternAZaZ23AZaZ09', 'locationName' => 'customLanguageCode', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'LanguageCodeControl' => [ 'shape' => 'AudioLanguageCodeControl', 'locationName' => 'languageCodeControl', ], 'RemixSettings' => [ 'shape' => 'RemixSettings', 'locationName' => 'remixSettings', ], 'StreamName' => [ 'shape' => '__stringPatternWS', 'locationName' => 'streamName', ], ], ], 'AudioDurationCorrection' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'AUTO', 'TRACK', 'FRAME', 'FORCE', ], ], 'AudioLanguageCodeControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'AudioNormalizationAlgorithm' => [ 'type' => 'string', 'enum' => [ 'ITU_BS_1770_1', 'ITU_BS_1770_2', 'ITU_BS_1770_3', 'ITU_BS_1770_4', ], ], 'AudioNormalizationAlgorithmControl' => [ 'type' => 'string', 'enum' => [ 'CORRECT_AUDIO', 'MEASURE_ONLY', ], ], 'AudioNormalizationLoudnessLogging' => [ 'type' => 'string', 'enum' => [ 'LOG', 'DONT_LOG', ], ], 'AudioNormalizationPeakCalculation' => [ 'type' => 'string', 'enum' => [ 'TRUE_PEAK', 'NONE', ], ], 'AudioNormalizationSettings' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'AudioNormalizationAlgorithm', 'locationName' => 'algorithm', ], 'AlgorithmControl' => [ 'shape' => 'AudioNormalizationAlgorithmControl', 'locationName' => 'algorithmControl', ], 'CorrectionGateLevel' => [ 'shape' => '__integerMinNegative70Max0', 'locationName' => 'correctionGateLevel', ], 'LoudnessLogging' => [ 'shape' => 'AudioNormalizationLoudnessLogging', 'locationName' => 'loudnessLogging', ], 'PeakCalculation' => [ 'shape' => 'AudioNormalizationPeakCalculation', 'locationName' => 'peakCalculation', ], 'TargetLkfs' => [ 'shape' => '__doubleMinNegative59Max0', 'locationName' => 'targetLkfs', ], 'TruePeakLimiterThreshold' => [ 'shape' => '__doubleMinNegative8Max0', 'locationName' => 'truePeakLimiterThreshold', ], ], ], 'AudioProperties' => [ 'type' => 'structure', 'members' => [ 'BitDepth' => [ 'shape' => '__integer', 'locationName' => 'bitDepth', ], 'BitRate' => [ 'shape' => '__integer', 'locationName' => 'bitRate', ], 'Channels' => [ 'shape' => '__integer', 'locationName' => 'channels', ], 'FrameRate' => [ 'shape' => 'FrameRate', 'locationName' => 'frameRate', ], 'LanguageCode' => [ 'shape' => '__string', 'locationName' => 'languageCode', ], 'SampleRate' => [ 'shape' => '__integer', 'locationName' => 'sampleRate', ], ], ], 'AudioSelector' => [ 'type' => 'structure', 'members' => [ 'AudioDurationCorrection' => [ 'shape' => 'AudioDurationCorrection', 'locationName' => 'audioDurationCorrection', ], 'CustomLanguageCode' => [ 'shape' => '__stringMin3Max3PatternAZaZ3', 'locationName' => 'customLanguageCode', ], 'DefaultSelection' => [ 'shape' => 'AudioDefaultSelection', 'locationName' => 'defaultSelection', ], 'ExternalAudioFileInput' => [ 'shape' => '__stringPatternS3Https', 'locationName' => 'externalAudioFileInput', ], 'HlsRenditionGroupSettings' => [ 'shape' => 'HlsRenditionGroupSettings', 'locationName' => 'hlsRenditionGroupSettings', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'Offset' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'offset', ], 'Pids' => [ 'shape' => '__listOf__integerMin1Max2147483647', 'locationName' => 'pids', ], 'ProgramSelection' => [ 'shape' => '__integerMin0Max8', 'locationName' => 'programSelection', ], 'RemixSettings' => [ 'shape' => 'RemixSettings', 'locationName' => 'remixSettings', ], 'SelectorType' => [ 'shape' => 'AudioSelectorType', 'locationName' => 'selectorType', ], 'Tracks' => [ 'shape' => '__listOf__integerMin1Max2147483647', 'locationName' => 'tracks', ], ], ], 'AudioSelectorGroup' => [ 'type' => 'structure', 'members' => [ 'AudioSelectorNames' => [ 'shape' => '__listOf__stringMin1', 'locationName' => 'audioSelectorNames', ], ], ], 'AudioSelectorType' => [ 'type' => 'string', 'enum' => [ 'PID', 'TRACK', 'LANGUAGE_CODE', 'HLS_RENDITION_GROUP', ], ], 'AudioTypeControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'AutomatedAbrRule' => [ 'type' => 'structure', 'members' => [ 'AllowedRenditions' => [ 'shape' => '__listOfAllowedRenditionSize', 'locationName' => 'allowedRenditions', ], 'ForceIncludeRenditions' => [ 'shape' => '__listOfForceIncludeRenditionSize', 'locationName' => 'forceIncludeRenditions', ], 'MinBottomRenditionSize' => [ 'shape' => 'MinBottomRenditionSize', 'locationName' => 'minBottomRenditionSize', ], 'MinTopRenditionSize' => [ 'shape' => 'MinTopRenditionSize', 'locationName' => 'minTopRenditionSize', ], 'Type' => [ 'shape' => 'RuleType', 'locationName' => 'type', ], ], ], 'AutomatedAbrSettings' => [ 'type' => 'structure', 'members' => [ 'MaxAbrBitrate' => [ 'shape' => '__integerMin100000Max100000000', 'locationName' => 'maxAbrBitrate', ], 'MaxRenditions' => [ 'shape' => '__integerMin3Max15', 'locationName' => 'maxRenditions', ], 'MinAbrBitrate' => [ 'shape' => '__integerMin100000Max100000000', 'locationName' => 'minAbrBitrate', ], 'Rules' => [ 'shape' => '__listOfAutomatedAbrRule', 'locationName' => 'rules', ], ], ], 'AutomatedEncodingSettings' => [ 'type' => 'structure', 'members' => [ 'AbrSettings' => [ 'shape' => 'AutomatedAbrSettings', 'locationName' => 'abrSettings', ], ], ], 'Av1AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'OFF', 'LOW', 'MEDIUM', 'HIGH', 'HIGHER', 'MAX', ], ], 'Av1BitDepth' => [ 'type' => 'string', 'enum' => [ 'BIT_8', 'BIT_10', ], ], 'Av1FilmGrainSynthesis' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Av1FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Av1FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'Av1QvbrSettings' => [ 'type' => 'structure', 'members' => [ 'QvbrQualityLevel' => [ 'shape' => '__integerMin1Max10', 'locationName' => 'qvbrQualityLevel', ], 'QvbrQualityLevelFineTune' => [ 'shape' => '__doubleMin0Max1', 'locationName' => 'qvbrQualityLevelFineTune', ], ], ], 'Av1RateControlMode' => [ 'type' => 'string', 'enum' => [ 'QVBR', ], ], 'Av1Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'Av1AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'BitDepth' => [ 'shape' => 'Av1BitDepth', 'locationName' => 'bitDepth', ], 'FilmGrainSynthesis' => [ 'shape' => 'Av1FilmGrainSynthesis', 'locationName' => 'filmGrainSynthesis', ], 'FramerateControl' => [ 'shape' => 'Av1FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'Av1FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000Max1152000000', 'locationName' => 'maxBitrate', ], 'NumberBFramesBetweenReferenceFrames' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'numberBFramesBetweenReferenceFrames', ], 'QvbrSettings' => [ 'shape' => 'Av1QvbrSettings', 'locationName' => 'qvbrSettings', ], 'RateControlMode' => [ 'shape' => 'Av1RateControlMode', 'locationName' => 'rateControlMode', ], 'Slices' => [ 'shape' => '__integerMin1Max32', 'locationName' => 'slices', ], 'SpatialAdaptiveQuantization' => [ 'shape' => 'Av1SpatialAdaptiveQuantization', 'locationName' => 'spatialAdaptiveQuantization', ], ], ], 'Av1SpatialAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'AvailBlanking' => [ 'type' => 'structure', 'members' => [ 'AvailBlankingImage' => [ 'shape' => '__stringMin14PatternS3BmpBMPPngPNGHttpsBmpBMPPngPNG', 'locationName' => 'availBlankingImage', ], ], ], 'AvcIntraClass' => [ 'type' => 'string', 'enum' => [ 'CLASS_50', 'CLASS_100', 'CLASS_200', 'CLASS_4K_2K', ], ], 'AvcIntraFramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'AvcIntraFramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'AvcIntraInterlaceMode' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE', 'TOP_FIELD', 'BOTTOM_FIELD', 'FOLLOW_TOP_FIELD', 'FOLLOW_BOTTOM_FIELD', ], ], 'AvcIntraScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'AvcIntraSettings' => [ 'type' => 'structure', 'members' => [ 'AvcIntraClass' => [ 'shape' => 'AvcIntraClass', 'locationName' => 'avcIntraClass', ], 'AvcIntraUhdSettings' => [ 'shape' => 'AvcIntraUhdSettings', 'locationName' => 'avcIntraUhdSettings', ], 'FramerateControl' => [ 'shape' => 'AvcIntraFramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'AvcIntraFramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max1001', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin24Max60000', 'locationName' => 'framerateNumerator', ], 'InterlaceMode' => [ 'shape' => 'AvcIntraInterlaceMode', 'locationName' => 'interlaceMode', ], 'ScanTypeConversionMode' => [ 'shape' => 'AvcIntraScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SlowPal' => [ 'shape' => 'AvcIntraSlowPal', 'locationName' => 'slowPal', ], 'Telecine' => [ 'shape' => 'AvcIntraTelecine', 'locationName' => 'telecine', ], ], ], 'AvcIntraSlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'AvcIntraTelecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'HARD', ], ], 'AvcIntraUhdQualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PASS', 'MULTI_PASS', ], ], 'AvcIntraUhdSettings' => [ 'type' => 'structure', 'members' => [ 'QualityTuningLevel' => [ 'shape' => 'AvcIntraUhdQualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 400, ], ], 'BandwidthReductionFilter' => [ 'type' => 'structure', 'members' => [ 'Sharpening' => [ 'shape' => 'BandwidthReductionFilterSharpening', 'locationName' => 'sharpening', ], 'Strength' => [ 'shape' => 'BandwidthReductionFilterStrength', 'locationName' => 'strength', ], ], ], 'BandwidthReductionFilterSharpening' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'OFF', ], ], 'BandwidthReductionFilterStrength' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', 'AUTO', 'OFF', ], ], 'BillingTagsSource' => [ 'type' => 'string', 'enum' => [ 'QUEUE', 'PRESET', 'JOB_TEMPLATE', 'JOB', ], ], 'BurnInSubtitleStylePassthrough' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'BurninDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Alignment' => [ 'shape' => 'BurninSubtitleAlignment', 'locationName' => 'alignment', ], 'ApplyFontColor' => [ 'shape' => 'BurninSubtitleApplyFontColor', 'locationName' => 'applyFontColor', ], 'BackgroundColor' => [ 'shape' => 'BurninSubtitleBackgroundColor', 'locationName' => 'backgroundColor', ], 'BackgroundOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'backgroundOpacity', ], 'FallbackFont' => [ 'shape' => 'BurninSubtitleFallbackFont', 'locationName' => 'fallbackFont', ], 'FontColor' => [ 'shape' => 'BurninSubtitleFontColor', 'locationName' => 'fontColor', ], 'FontFileBold' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileBold', ], 'FontFileBoldItalic' => [ 'shape' => '__string', 'locationName' => 'fontFileBoldItalic', ], 'FontFileItalic' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileItalic', ], 'FontFileRegular' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileRegular', ], 'FontOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'fontOpacity', ], 'FontResolution' => [ 'shape' => '__integerMin96Max600', 'locationName' => 'fontResolution', ], 'FontScript' => [ 'shape' => 'FontScript', 'locationName' => 'fontScript', ], 'FontSize' => [ 'shape' => '__integerMin0Max96', 'locationName' => 'fontSize', ], 'HexFontColor' => [ 'shape' => '__stringMin6Max8Pattern09aFAF609aFAF2', 'locationName' => 'hexFontColor', ], 'OutlineColor' => [ 'shape' => 'BurninSubtitleOutlineColor', 'locationName' => 'outlineColor', ], 'OutlineSize' => [ 'shape' => '__integerMin0Max10', 'locationName' => 'outlineSize', ], 'RemoveRubyReserveAttributes' => [ 'shape' => 'RemoveRubyReserveAttributes', 'locationName' => 'removeRubyReserveAttributes', ], 'ShadowColor' => [ 'shape' => 'BurninSubtitleShadowColor', 'locationName' => 'shadowColor', ], 'ShadowOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'shadowOpacity', ], 'ShadowXOffset' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'shadowXOffset', ], 'ShadowYOffset' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'shadowYOffset', ], 'StylePassthrough' => [ 'shape' => 'BurnInSubtitleStylePassthrough', 'locationName' => 'stylePassthrough', ], 'TeletextSpacing' => [ 'shape' => 'BurninSubtitleTeletextSpacing', 'locationName' => 'teletextSpacing', ], 'XPosition' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'xPosition', ], 'YPosition' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'yPosition', ], ], ], 'BurninSubtitleAlignment' => [ 'type' => 'string', 'enum' => [ 'CENTERED', 'LEFT', 'AUTO', ], ], 'BurninSubtitleApplyFontColor' => [ 'type' => 'string', 'enum' => [ 'WHITE_TEXT_ONLY', 'ALL_TEXT', ], ], 'BurninSubtitleBackgroundColor' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BLACK', 'WHITE', 'AUTO', ], ], 'BurninSubtitleFallbackFont' => [ 'type' => 'string', 'enum' => [ 'BEST_MATCH', 'MONOSPACED_SANSSERIF', 'MONOSPACED_SERIF', 'PROPORTIONAL_SANSSERIF', 'PROPORTIONAL_SERIF', ], ], 'BurninSubtitleFontColor' => [ 'type' => 'string', 'enum' => [ 'WHITE', 'BLACK', 'YELLOW', 'RED', 'GREEN', 'BLUE', 'HEX', 'AUTO', ], ], 'BurninSubtitleOutlineColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'WHITE', 'YELLOW', 'RED', 'GREEN', 'BLUE', 'AUTO', ], ], 'BurninSubtitleShadowColor' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BLACK', 'WHITE', 'AUTO', ], ], 'BurninSubtitleTeletextSpacing' => [ 'type' => 'string', 'enum' => [ 'FIXED_GRID', 'PROPORTIONAL', 'AUTO', ], ], 'CancelJobRequest' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', 'location' => 'uri', ], ], 'required' => [ 'Id', ], ], 'CancelJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'CaptionDescription' => [ 'type' => 'structure', 'members' => [ 'CaptionSelectorName' => [ 'shape' => '__stringMin1', 'locationName' => 'captionSelectorName', ], 'CustomLanguageCode' => [ 'shape' => '__stringPatternAZaZ23AZaZ', 'locationName' => 'customLanguageCode', ], 'DestinationSettings' => [ 'shape' => 'CaptionDestinationSettings', 'locationName' => 'destinationSettings', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'LanguageDescription' => [ 'shape' => '__string', 'locationName' => 'languageDescription', ], ], ], 'CaptionDescriptionPreset' => [ 'type' => 'structure', 'members' => [ 'CustomLanguageCode' => [ 'shape' => '__stringPatternAZaZ23AZaZ', 'locationName' => 'customLanguageCode', ], 'DestinationSettings' => [ 'shape' => 'CaptionDestinationSettings', 'locationName' => 'destinationSettings', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'LanguageDescription' => [ 'shape' => '__string', 'locationName' => 'languageDescription', ], ], ], 'CaptionDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'BurninDestinationSettings' => [ 'shape' => 'BurninDestinationSettings', 'locationName' => 'burninDestinationSettings', ], 'DestinationType' => [ 'shape' => 'CaptionDestinationType', 'locationName' => 'destinationType', ], 'DvbSubDestinationSettings' => [ 'shape' => 'DvbSubDestinationSettings', 'locationName' => 'dvbSubDestinationSettings', ], 'EmbeddedDestinationSettings' => [ 'shape' => 'EmbeddedDestinationSettings', 'locationName' => 'embeddedDestinationSettings', ], 'ImscDestinationSettings' => [ 'shape' => 'ImscDestinationSettings', 'locationName' => 'imscDestinationSettings', ], 'SccDestinationSettings' => [ 'shape' => 'SccDestinationSettings', 'locationName' => 'sccDestinationSettings', ], 'SrtDestinationSettings' => [ 'shape' => 'SrtDestinationSettings', 'locationName' => 'srtDestinationSettings', ], 'TeletextDestinationSettings' => [ 'shape' => 'TeletextDestinationSettings', 'locationName' => 'teletextDestinationSettings', ], 'TtmlDestinationSettings' => [ 'shape' => 'TtmlDestinationSettings', 'locationName' => 'ttmlDestinationSettings', ], 'WebvttDestinationSettings' => [ 'shape' => 'WebvttDestinationSettings', 'locationName' => 'webvttDestinationSettings', ], ], ], 'CaptionDestinationType' => [ 'type' => 'string', 'enum' => [ 'BURN_IN', 'DVB_SUB', 'EMBEDDED', 'EMBEDDED_PLUS_SCTE20', 'IMSC', 'SCTE20_PLUS_EMBEDDED', 'SCC', 'SRT', 'SMI', 'TELETEXT', 'TTML', 'WEBVTT', ], ], 'CaptionSelector' => [ 'type' => 'structure', 'members' => [ 'CustomLanguageCode' => [ 'shape' => '__stringMin3Max3PatternAZaZ3', 'locationName' => 'customLanguageCode', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'SourceSettings' => [ 'shape' => 'CaptionSourceSettings', 'locationName' => 'sourceSettings', ], ], ], 'CaptionSourceByteRateLimit' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CaptionSourceConvertPaintOnToPopOn' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CaptionSourceFramerate' => [ 'type' => 'structure', 'members' => [ 'FramerateDenominator' => [ 'shape' => '__integerMin1Max1001', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max60000', 'locationName' => 'framerateNumerator', ], ], ], 'CaptionSourceSettings' => [ 'type' => 'structure', 'members' => [ 'AncillarySourceSettings' => [ 'shape' => 'AncillarySourceSettings', 'locationName' => 'ancillarySourceSettings', ], 'DvbSubSourceSettings' => [ 'shape' => 'DvbSubSourceSettings', 'locationName' => 'dvbSubSourceSettings', ], 'EmbeddedSourceSettings' => [ 'shape' => 'EmbeddedSourceSettings', 'locationName' => 'embeddedSourceSettings', ], 'FileSourceSettings' => [ 'shape' => 'FileSourceSettings', 'locationName' => 'fileSourceSettings', ], 'SourceType' => [ 'shape' => 'CaptionSourceType', 'locationName' => 'sourceType', ], 'TeletextSourceSettings' => [ 'shape' => 'TeletextSourceSettings', 'locationName' => 'teletextSourceSettings', ], 'TrackSourceSettings' => [ 'shape' => 'TrackSourceSettings', 'locationName' => 'trackSourceSettings', ], 'WebvttHlsSourceSettings' => [ 'shape' => 'WebvttHlsSourceSettings', 'locationName' => 'webvttHlsSourceSettings', ], ], ], 'CaptionSourceType' => [ 'type' => 'string', 'enum' => [ 'ANCILLARY', 'DVB_SUB', 'EMBEDDED', 'SCTE20', 'SCC', 'TTML', 'STL', 'SRT', 'SMI', 'SMPTE_TT', 'TELETEXT', 'NULL_SOURCE', 'IMSC', 'WEBVTT', ], ], 'ChannelMapping' => [ 'type' => 'structure', 'members' => [ 'OutputChannels' => [ 'shape' => '__listOfOutputChannelMapping', 'locationName' => 'outputChannels', ], ], ], 'ChromaPositionMode' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'FORCE_CENTER', 'FORCE_TOP_LEFT', ], ], 'ClipLimits' => [ 'type' => 'structure', 'members' => [ 'MaximumRGBTolerance' => [ 'shape' => '__integerMin90Max105', 'locationName' => 'maximumRGBTolerance', ], 'MaximumYUV' => [ 'shape' => '__integerMin920Max1023', 'locationName' => 'maximumYUV', ], 'MinimumRGBTolerance' => [ 'shape' => '__integerMinNegative5Max10', 'locationName' => 'minimumRGBTolerance', ], 'MinimumYUV' => [ 'shape' => '__integerMin0Max128', 'locationName' => 'minimumYUV', ], ], ], 'CmafAdditionalManifest' => [ 'type' => 'structure', 'members' => [ 'ManifestNameModifier' => [ 'shape' => '__stringMin1', 'locationName' => 'manifestNameModifier', ], 'SelectedOutputs' => [ 'shape' => '__listOf__stringMin1', 'locationName' => 'selectedOutputs', ], ], ], 'CmafClientCache' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'CmafCodecSpecification' => [ 'type' => 'string', 'enum' => [ 'RFC_6381', 'RFC_4281', ], ], 'CmafEncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'ConstantInitializationVector' => [ 'shape' => '__stringMin32Max32Pattern09aFAF32', 'locationName' => 'constantInitializationVector', ], 'EncryptionMethod' => [ 'shape' => 'CmafEncryptionType', 'locationName' => 'encryptionMethod', ], 'InitializationVectorInManifest' => [ 'shape' => 'CmafInitializationVectorInManifest', 'locationName' => 'initializationVectorInManifest', ], 'SpekeKeyProvider' => [ 'shape' => 'SpekeKeyProviderCmaf', 'locationName' => 'spekeKeyProvider', ], 'StaticKeyProvider' => [ 'shape' => 'StaticKeyProvider', 'locationName' => 'staticKeyProvider', ], 'Type' => [ 'shape' => 'CmafKeyProviderType', 'locationName' => 'type', ], ], ], 'CmafEncryptionType' => [ 'type' => 'string', 'enum' => [ 'SAMPLE_AES', 'AES_CTR', ], ], 'CmafGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AdditionalManifests' => [ 'shape' => '__listOfCmafAdditionalManifest', 'locationName' => 'additionalManifests', ], 'BaseUrl' => [ 'shape' => '__string', 'locationName' => 'baseUrl', ], 'ClientCache' => [ 'shape' => 'CmafClientCache', 'locationName' => 'clientCache', ], 'CodecSpecification' => [ 'shape' => 'CmafCodecSpecification', 'locationName' => 'codecSpecification', ], 'DashIFrameTrickPlayNameModifier' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'dashIFrameTrickPlayNameModifier', ], 'DashManifestStyle' => [ 'shape' => 'DashManifestStyle', 'locationName' => 'dashManifestStyle', ], 'Destination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'destination', ], 'DestinationSettings' => [ 'shape' => 'DestinationSettings', 'locationName' => 'destinationSettings', ], 'Encryption' => [ 'shape' => 'CmafEncryptionSettings', 'locationName' => 'encryption', ], 'FragmentLength' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'fragmentLength', ], 'ImageBasedTrickPlay' => [ 'shape' => 'CmafImageBasedTrickPlay', 'locationName' => 'imageBasedTrickPlay', ], 'ImageBasedTrickPlaySettings' => [ 'shape' => 'CmafImageBasedTrickPlaySettings', 'locationName' => 'imageBasedTrickPlaySettings', ], 'ManifestCompression' => [ 'shape' => 'CmafManifestCompression', 'locationName' => 'manifestCompression', ], 'ManifestDurationFormat' => [ 'shape' => 'CmafManifestDurationFormat', 'locationName' => 'manifestDurationFormat', ], 'MinBufferTime' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'minBufferTime', ], 'MinFinalSegmentLength' => [ 'shape' => '__doubleMin0Max2147483647', 'locationName' => 'minFinalSegmentLength', ], 'MpdManifestBandwidthType' => [ 'shape' => 'CmafMpdManifestBandwidthType', 'locationName' => 'mpdManifestBandwidthType', ], 'MpdProfile' => [ 'shape' => 'CmafMpdProfile', 'locationName' => 'mpdProfile', ], 'PtsOffsetHandlingForBFrames' => [ 'shape' => 'CmafPtsOffsetHandlingForBFrames', 'locationName' => 'ptsOffsetHandlingForBFrames', ], 'SegmentControl' => [ 'shape' => 'CmafSegmentControl', 'locationName' => 'segmentControl', ], 'SegmentLength' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'segmentLength', ], 'SegmentLengthControl' => [ 'shape' => 'CmafSegmentLengthControl', 'locationName' => 'segmentLengthControl', ], 'StreamInfResolution' => [ 'shape' => 'CmafStreamInfResolution', 'locationName' => 'streamInfResolution', ], 'TargetDurationCompatibilityMode' => [ 'shape' => 'CmafTargetDurationCompatibilityMode', 'locationName' => 'targetDurationCompatibilityMode', ], 'VideoCompositionOffsets' => [ 'shape' => 'CmafVideoCompositionOffsets', 'locationName' => 'videoCompositionOffsets', ], 'WriteDashManifest' => [ 'shape' => 'CmafWriteDASHManifest', 'locationName' => 'writeDashManifest', ], 'WriteHlsManifest' => [ 'shape' => 'CmafWriteHLSManifest', 'locationName' => 'writeHlsManifest', ], 'WriteSegmentTimelineInRepresentation' => [ 'shape' => 'CmafWriteSegmentTimelineInRepresentation', 'locationName' => 'writeSegmentTimelineInRepresentation', ], ], ], 'CmafImageBasedTrickPlay' => [ 'type' => 'string', 'enum' => [ 'NONE', 'THUMBNAIL', 'THUMBNAIL_AND_FULLFRAME', 'ADVANCED', ], ], 'CmafImageBasedTrickPlaySettings' => [ 'type' => 'structure', 'members' => [ 'IntervalCadence' => [ 'shape' => 'CmafIntervalCadence', 'locationName' => 'intervalCadence', ], 'ThumbnailHeight' => [ 'shape' => '__integerMin2Max4096', 'locationName' => 'thumbnailHeight', ], 'ThumbnailInterval' => [ 'shape' => '__doubleMin0Max2147483647', 'locationName' => 'thumbnailInterval', ], 'ThumbnailWidth' => [ 'shape' => '__integerMin8Max4096', 'locationName' => 'thumbnailWidth', ], 'TileHeight' => [ 'shape' => '__integerMin1Max2048', 'locationName' => 'tileHeight', ], 'TileWidth' => [ 'shape' => '__integerMin1Max512', 'locationName' => 'tileWidth', ], ], ], 'CmafInitializationVectorInManifest' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'CmafIntervalCadence' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_IFRAME', 'FOLLOW_CUSTOM', ], ], 'CmafKeyProviderType' => [ 'type' => 'string', 'enum' => [ 'SPEKE', 'STATIC_KEY', ], ], 'CmafManifestCompression' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'NONE', ], ], 'CmafManifestDurationFormat' => [ 'type' => 'string', 'enum' => [ 'FLOATING_POINT', 'INTEGER', ], ], 'CmafMpdManifestBandwidthType' => [ 'type' => 'string', 'enum' => [ 'AVERAGE', 'MAX', ], ], 'CmafMpdProfile' => [ 'type' => 'string', 'enum' => [ 'MAIN_PROFILE', 'ON_DEMAND_PROFILE', ], ], 'CmafPtsOffsetHandlingForBFrames' => [ 'type' => 'string', 'enum' => [ 'ZERO_BASED', 'MATCH_INITIAL_PTS', ], ], 'CmafSegmentControl' => [ 'type' => 'string', 'enum' => [ 'SINGLE_FILE', 'SEGMENTED_FILES', ], ], 'CmafSegmentLengthControl' => [ 'type' => 'string', 'enum' => [ 'EXACT', 'GOP_MULTIPLE', ], ], 'CmafStreamInfResolution' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'CmafTargetDurationCompatibilityMode' => [ 'type' => 'string', 'enum' => [ 'LEGACY', 'SPEC_COMPLIANT', ], ], 'CmafVideoCompositionOffsets' => [ 'type' => 'string', 'enum' => [ 'SIGNED', 'UNSIGNED', ], ], 'CmafWriteDASHManifest' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'CmafWriteHLSManifest' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'CmafWriteSegmentTimelineInRepresentation' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CmfcAudioDuration' => [ 'type' => 'string', 'enum' => [ 'DEFAULT_CODEC_DURATION', 'MATCH_VIDEO_DURATION', ], ], 'CmfcAudioTrackType' => [ 'type' => 'string', 'enum' => [ 'ALTERNATE_AUDIO_AUTO_SELECT_DEFAULT', 'ALTERNATE_AUDIO_AUTO_SELECT', 'ALTERNATE_AUDIO_NOT_AUTO_SELECT', 'AUDIO_ONLY_VARIANT_STREAM', ], ], 'CmfcDescriptiveVideoServiceFlag' => [ 'type' => 'string', 'enum' => [ 'DONT_FLAG', 'FLAG', ], ], 'CmfcIFrameOnlyManifest' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'CmfcKlvMetadata' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'CmfcManifestMetadataSignaling' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'CmfcScte35Esam' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'NONE', ], ], 'CmfcScte35Source' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'CmfcSettings' => [ 'type' => 'structure', 'members' => [ 'AudioDuration' => [ 'shape' => 'CmfcAudioDuration', 'locationName' => 'audioDuration', ], 'AudioGroupId' => [ 'shape' => '__string', 'locationName' => 'audioGroupId', ], 'AudioRenditionSets' => [ 'shape' => '__string', 'locationName' => 'audioRenditionSets', ], 'AudioTrackType' => [ 'shape' => 'CmfcAudioTrackType', 'locationName' => 'audioTrackType', ], 'DescriptiveVideoServiceFlag' => [ 'shape' => 'CmfcDescriptiveVideoServiceFlag', 'locationName' => 'descriptiveVideoServiceFlag', ], 'IFrameOnlyManifest' => [ 'shape' => 'CmfcIFrameOnlyManifest', 'locationName' => 'iFrameOnlyManifest', ], 'KlvMetadata' => [ 'shape' => 'CmfcKlvMetadata', 'locationName' => 'klvMetadata', ], 'ManifestMetadataSignaling' => [ 'shape' => 'CmfcManifestMetadataSignaling', 'locationName' => 'manifestMetadataSignaling', ], 'Scte35Esam' => [ 'shape' => 'CmfcScte35Esam', 'locationName' => 'scte35Esam', ], 'Scte35Source' => [ 'shape' => 'CmfcScte35Source', 'locationName' => 'scte35Source', ], 'TimedMetadata' => [ 'shape' => 'CmfcTimedMetadata', 'locationName' => 'timedMetadata', ], 'TimedMetadataBoxVersion' => [ 'shape' => 'CmfcTimedMetadataBoxVersion', 'locationName' => 'timedMetadataBoxVersion', ], 'TimedMetadataSchemeIdUri' => [ 'shape' => '__stringMax1000', 'locationName' => 'timedMetadataSchemeIdUri', ], 'TimedMetadataValue' => [ 'shape' => '__stringMax1000', 'locationName' => 'timedMetadataValue', ], ], ], 'CmfcTimedMetadata' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'CmfcTimedMetadataBoxVersion' => [ 'type' => 'string', 'enum' => [ 'VERSION_0', 'VERSION_1', ], ], 'Codec' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'AAC', 'AC3', 'EAC3', 'FLAC', 'MP3', 'OPUS', 'PCM', 'VORBIS', 'AV1', 'AVC', 'HEVC', 'MJPEG', 'MP4V', 'MPEG2', 'PRORES', 'THEORA', 'VP8', 'VP9', 'C608', 'C708', 'WEBVTT', ], ], 'ColorConversion3DLUTSetting' => [ 'type' => 'structure', 'members' => [ 'FileInput' => [ 'shape' => '__stringMin14PatternS3CubeCUBEHttpsCubeCUBE', 'locationName' => 'fileInput', ], 'InputColorSpace' => [ 'shape' => 'ColorSpace', 'locationName' => 'inputColorSpace', ], 'InputMasteringLuminance' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'inputMasteringLuminance', ], 'OutputColorSpace' => [ 'shape' => 'ColorSpace', 'locationName' => 'outputColorSpace', ], 'OutputMasteringLuminance' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'outputMasteringLuminance', ], ], ], 'ColorCorrector' => [ 'type' => 'structure', 'members' => [ 'Brightness' => [ 'shape' => '__integerMin1Max100', 'locationName' => 'brightness', ], 'ClipLimits' => [ 'shape' => 'ClipLimits', 'locationName' => 'clipLimits', ], 'ColorSpaceConversion' => [ 'shape' => 'ColorSpaceConversion', 'locationName' => 'colorSpaceConversion', ], 'Contrast' => [ 'shape' => '__integerMin1Max100', 'locationName' => 'contrast', ], 'Hdr10Metadata' => [ 'shape' => 'Hdr10Metadata', 'locationName' => 'hdr10Metadata', ], 'HdrToSdrToneMapper' => [ 'shape' => 'HDRToSDRToneMapper', 'locationName' => 'hdrToSdrToneMapper', ], 'Hue' => [ 'shape' => '__integerMinNegative180Max180', 'locationName' => 'hue', ], 'MaxLuminance' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'maxLuminance', ], 'SampleRangeConversion' => [ 'shape' => 'SampleRangeConversion', 'locationName' => 'sampleRangeConversion', ], 'Saturation' => [ 'shape' => '__integerMin1Max100', 'locationName' => 'saturation', ], 'SdrReferenceWhiteLevel' => [ 'shape' => '__integerMin100Max1000', 'locationName' => 'sdrReferenceWhiteLevel', ], ], ], 'ColorMetadata' => [ 'type' => 'string', 'enum' => [ 'IGNORE', 'INSERT', ], ], 'ColorPrimaries' => [ 'type' => 'string', 'enum' => [ 'ITU_709', 'UNSPECIFIED', 'RESERVED', 'ITU_470M', 'ITU_470BG', 'SMPTE_170M', 'SMPTE_240M', 'GENERIC_FILM', 'ITU_2020', 'SMPTE_428_1', 'SMPTE_431_2', 'SMPTE_EG_432_1', 'IPT', 'SMPTE_2067XYZ', 'EBU_3213_E', 'LAST', ], ], 'ColorSpace' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'REC_601', 'REC_709', 'HDR10', 'HLG_2020', 'P3DCI', 'P3D65_SDR', 'P3D65_HDR', ], ], 'ColorSpaceConversion' => [ 'type' => 'string', 'enum' => [ 'NONE', 'FORCE_601', 'FORCE_709', 'FORCE_HDR10', 'FORCE_HLG_2020', 'FORCE_P3DCI', 'FORCE_P3D65_SDR', 'FORCE_P3D65_HDR', ], ], 'ColorSpaceUsage' => [ 'type' => 'string', 'enum' => [ 'FORCE', 'FALLBACK', ], ], 'Commitment' => [ 'type' => 'string', 'enum' => [ 'ONE_YEAR', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 409, ], ], 'Container' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__double', 'locationName' => 'duration', ], 'Format' => [ 'shape' => 'Format', 'locationName' => 'format', ], 'Tracks' => [ 'shape' => '__listOfTrack', 'locationName' => 'tracks', ], ], ], 'ContainerSettings' => [ 'type' => 'structure', 'members' => [ 'CmfcSettings' => [ 'shape' => 'CmfcSettings', 'locationName' => 'cmfcSettings', ], 'Container' => [ 'shape' => 'ContainerType', 'locationName' => 'container', ], 'F4vSettings' => [ 'shape' => 'F4vSettings', 'locationName' => 'f4vSettings', ], 'M2tsSettings' => [ 'shape' => 'M2tsSettings', 'locationName' => 'm2tsSettings', ], 'M3u8Settings' => [ 'shape' => 'M3u8Settings', 'locationName' => 'm3u8Settings', ], 'MovSettings' => [ 'shape' => 'MovSettings', 'locationName' => 'movSettings', ], 'Mp4Settings' => [ 'shape' => 'Mp4Settings', 'locationName' => 'mp4Settings', ], 'MpdSettings' => [ 'shape' => 'MpdSettings', 'locationName' => 'mpdSettings', ], 'MxfSettings' => [ 'shape' => 'MxfSettings', 'locationName' => 'mxfSettings', ], ], ], 'ContainerType' => [ 'type' => 'string', 'enum' => [ 'F4V', 'GIF', 'ISMV', 'M2TS', 'M3U8', 'CMFC', 'MOV', 'MP4', 'MPD', 'MXF', 'OGG', 'WEBM', 'RAW', 'Y4M', ], ], 'CopyProtectionAction' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'STRIP', ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'members' => [ 'AccelerationSettings' => [ 'shape' => 'AccelerationSettings', 'locationName' => 'accelerationSettings', ], 'BillingTagsSource' => [ 'shape' => 'BillingTagsSource', 'locationName' => 'billingTagsSource', ], 'ClientRequestToken' => [ 'shape' => '__string', 'locationName' => 'clientRequestToken', 'idempotencyToken' => true, ], 'HopDestinations' => [ 'shape' => '__listOfHopDestination', 'locationName' => 'hopDestinations', ], 'JobEngineVersion' => [ 'shape' => '__string', 'locationName' => 'jobEngineVersion', ], 'JobTemplate' => [ 'shape' => '__string', 'locationName' => 'jobTemplate', ], 'Priority' => [ 'shape' => '__integerMinNegative50Max50', 'locationName' => 'priority', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', ], 'Role' => [ 'shape' => '__string', 'locationName' => 'role', ], 'Settings' => [ 'shape' => 'JobSettings', 'locationName' => 'settings', ], 'SimulateReservedQueue' => [ 'shape' => 'SimulateReservedQueue', 'locationName' => 'simulateReservedQueue', ], 'StatusUpdateInterval' => [ 'shape' => 'StatusUpdateInterval', 'locationName' => 'statusUpdateInterval', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], 'UserMetadata' => [ 'shape' => '__mapOf__string', 'locationName' => 'userMetadata', ], ], 'required' => [ 'Role', 'Settings', ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'members' => [ 'Job' => [ 'shape' => 'Job', 'locationName' => 'job', ], ], ], 'CreateJobTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'AccelerationSettings' => [ 'shape' => 'AccelerationSettings', 'locationName' => 'accelerationSettings', ], 'Category' => [ 'shape' => '__string', 'locationName' => 'category', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'HopDestinations' => [ 'shape' => '__listOfHopDestination', 'locationName' => 'hopDestinations', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Priority' => [ 'shape' => '__integerMinNegative50Max50', 'locationName' => 'priority', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', ], 'Settings' => [ 'shape' => 'JobTemplateSettings', 'locationName' => 'settings', ], 'StatusUpdateInterval' => [ 'shape' => 'StatusUpdateInterval', 'locationName' => 'statusUpdateInterval', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'Settings', 'Name', ], ], 'CreateJobTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'JobTemplate' => [ 'shape' => 'JobTemplate', 'locationName' => 'jobTemplate', ], ], ], 'CreatePresetRequest' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => '__string', 'locationName' => 'category', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Settings' => [ 'shape' => 'PresetSettings', 'locationName' => 'settings', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'Settings', 'Name', ], ], 'CreatePresetResponse' => [ 'type' => 'structure', 'members' => [ 'Preset' => [ 'shape' => 'Preset', 'locationName' => 'preset', ], ], ], 'CreateQueueRequest' => [ 'type' => 'structure', 'members' => [ 'ConcurrentJobs' => [ 'shape' => '__integer', 'locationName' => 'concurrentJobs', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'locationName' => 'pricingPlan', ], 'ReservationPlanSettings' => [ 'shape' => 'ReservationPlanSettings', 'locationName' => 'reservationPlanSettings', ], 'Status' => [ 'shape' => 'QueueStatus', 'locationName' => 'status', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'Name', ], ], 'CreateQueueResponse' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'Queue', 'locationName' => 'queue', ], ], ], 'DashAdditionalManifest' => [ 'type' => 'structure', 'members' => [ 'ManifestNameModifier' => [ 'shape' => '__stringMin1', 'locationName' => 'manifestNameModifier', ], 'SelectedOutputs' => [ 'shape' => '__listOf__stringMin1', 'locationName' => 'selectedOutputs', ], ], ], 'DashIsoEncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'PlaybackDeviceCompatibility' => [ 'shape' => 'DashIsoPlaybackDeviceCompatibility', 'locationName' => 'playbackDeviceCompatibility', ], 'SpekeKeyProvider' => [ 'shape' => 'SpekeKeyProvider', 'locationName' => 'spekeKeyProvider', ], ], ], 'DashIsoGroupAudioChannelConfigSchemeIdUri' => [ 'type' => 'string', 'enum' => [ 'MPEG_CHANNEL_CONFIGURATION', 'DOLBY_CHANNEL_CONFIGURATION', ], ], 'DashIsoGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AdditionalManifests' => [ 'shape' => '__listOfDashAdditionalManifest', 'locationName' => 'additionalManifests', ], 'AudioChannelConfigSchemeIdUri' => [ 'shape' => 'DashIsoGroupAudioChannelConfigSchemeIdUri', 'locationName' => 'audioChannelConfigSchemeIdUri', ], 'BaseUrl' => [ 'shape' => '__string', 'locationName' => 'baseUrl', ], 'DashIFrameTrickPlayNameModifier' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'dashIFrameTrickPlayNameModifier', ], 'DashManifestStyle' => [ 'shape' => 'DashManifestStyle', 'locationName' => 'dashManifestStyle', ], 'Destination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'destination', ], 'DestinationSettings' => [ 'shape' => 'DestinationSettings', 'locationName' => 'destinationSettings', ], 'Encryption' => [ 'shape' => 'DashIsoEncryptionSettings', 'locationName' => 'encryption', ], 'FragmentLength' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'fragmentLength', ], 'HbbtvCompliance' => [ 'shape' => 'DashIsoHbbtvCompliance', 'locationName' => 'hbbtvCompliance', ], 'ImageBasedTrickPlay' => [ 'shape' => 'DashIsoImageBasedTrickPlay', 'locationName' => 'imageBasedTrickPlay', ], 'ImageBasedTrickPlaySettings' => [ 'shape' => 'DashIsoImageBasedTrickPlaySettings', 'locationName' => 'imageBasedTrickPlaySettings', ], 'MinBufferTime' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'minBufferTime', ], 'MinFinalSegmentLength' => [ 'shape' => '__doubleMin0Max2147483647', 'locationName' => 'minFinalSegmentLength', ], 'MpdManifestBandwidthType' => [ 'shape' => 'DashIsoMpdManifestBandwidthType', 'locationName' => 'mpdManifestBandwidthType', ], 'MpdProfile' => [ 'shape' => 'DashIsoMpdProfile', 'locationName' => 'mpdProfile', ], 'PtsOffsetHandlingForBFrames' => [ 'shape' => 'DashIsoPtsOffsetHandlingForBFrames', 'locationName' => 'ptsOffsetHandlingForBFrames', ], 'SegmentControl' => [ 'shape' => 'DashIsoSegmentControl', 'locationName' => 'segmentControl', ], 'SegmentLength' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'segmentLength', ], 'SegmentLengthControl' => [ 'shape' => 'DashIsoSegmentLengthControl', 'locationName' => 'segmentLengthControl', ], 'VideoCompositionOffsets' => [ 'shape' => 'DashIsoVideoCompositionOffsets', 'locationName' => 'videoCompositionOffsets', ], 'WriteSegmentTimelineInRepresentation' => [ 'shape' => 'DashIsoWriteSegmentTimelineInRepresentation', 'locationName' => 'writeSegmentTimelineInRepresentation', ], ], ], 'DashIsoHbbtvCompliance' => [ 'type' => 'string', 'enum' => [ 'HBBTV_1_5', 'NONE', ], ], 'DashIsoImageBasedTrickPlay' => [ 'type' => 'string', 'enum' => [ 'NONE', 'THUMBNAIL', 'THUMBNAIL_AND_FULLFRAME', 'ADVANCED', ], ], 'DashIsoImageBasedTrickPlaySettings' => [ 'type' => 'structure', 'members' => [ 'IntervalCadence' => [ 'shape' => 'DashIsoIntervalCadence', 'locationName' => 'intervalCadence', ], 'ThumbnailHeight' => [ 'shape' => '__integerMin1Max4096', 'locationName' => 'thumbnailHeight', ], 'ThumbnailInterval' => [ 'shape' => '__doubleMin0Max2147483647', 'locationName' => 'thumbnailInterval', ], 'ThumbnailWidth' => [ 'shape' => '__integerMin8Max4096', 'locationName' => 'thumbnailWidth', ], 'TileHeight' => [ 'shape' => '__integerMin1Max2048', 'locationName' => 'tileHeight', ], 'TileWidth' => [ 'shape' => '__integerMin1Max512', 'locationName' => 'tileWidth', ], ], ], 'DashIsoIntervalCadence' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_IFRAME', 'FOLLOW_CUSTOM', ], ], 'DashIsoMpdManifestBandwidthType' => [ 'type' => 'string', 'enum' => [ 'AVERAGE', 'MAX', ], ], 'DashIsoMpdProfile' => [ 'type' => 'string', 'enum' => [ 'MAIN_PROFILE', 'ON_DEMAND_PROFILE', ], ], 'DashIsoPlaybackDeviceCompatibility' => [ 'type' => 'string', 'enum' => [ 'CENC_V1', 'UNENCRYPTED_SEI', ], ], 'DashIsoPtsOffsetHandlingForBFrames' => [ 'type' => 'string', 'enum' => [ 'ZERO_BASED', 'MATCH_INITIAL_PTS', ], ], 'DashIsoSegmentControl' => [ 'type' => 'string', 'enum' => [ 'SINGLE_FILE', 'SEGMENTED_FILES', ], ], 'DashIsoSegmentLengthControl' => [ 'type' => 'string', 'enum' => [ 'EXACT', 'GOP_MULTIPLE', ], ], 'DashIsoVideoCompositionOffsets' => [ 'type' => 'string', 'enum' => [ 'SIGNED', 'UNSIGNED', ], ], 'DashIsoWriteSegmentTimelineInRepresentation' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DashManifestStyle' => [ 'type' => 'string', 'enum' => [ 'BASIC', 'COMPACT', 'DISTINCT', ], ], 'DataProperties' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => '__string', 'locationName' => 'languageCode', ], ], ], 'DecryptionMode' => [ 'type' => 'string', 'enum' => [ 'AES_CTR', 'AES_CBC', 'AES_GCM', ], ], 'DeinterlaceAlgorithm' => [ 'type' => 'string', 'enum' => [ 'INTERPOLATE', 'INTERPOLATE_TICKER', 'BLEND', 'BLEND_TICKER', 'LINEAR_INTERPOLATION', ], ], 'Deinterlacer' => [ 'type' => 'structure', 'members' => [ 'Algorithm' => [ 'shape' => 'DeinterlaceAlgorithm', 'locationName' => 'algorithm', ], 'Control' => [ 'shape' => 'DeinterlacerControl', 'locationName' => 'control', ], 'Mode' => [ 'shape' => 'DeinterlacerMode', 'locationName' => 'mode', ], ], ], 'DeinterlacerControl' => [ 'type' => 'string', 'enum' => [ 'FORCE_ALL_FRAMES', 'NORMAL', ], ], 'DeinterlacerMode' => [ 'type' => 'string', 'enum' => [ 'DEINTERLACE', 'INVERSE_TELECINE', 'ADAPTIVE', ], ], 'DeleteJobTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], ], 'required' => [ 'Name', ], ], 'DeleteJobTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyRequest' => [ 'type' => 'structure', 'members' => [], ], 'DeletePolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePresetRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], ], 'required' => [ 'Name', ], ], 'DeletePresetResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteQueueRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], ], 'required' => [ 'Name', ], ], 'DeleteQueueResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeEndpointsMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'GET_ONLY', ], 'deprecated' => true, 'deprecatedMessage' => 'DescribeEndpoints and account specific endpoints are no longer required. We recommend that you send your requests directly to the regional endpoint instead.', ], 'DescribeEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => '__integer', 'locationName' => 'maxResults', ], 'Mode' => [ 'shape' => 'DescribeEndpointsMode', 'locationName' => 'mode', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], 'deprecated' => true, 'deprecatedMessage' => 'DescribeEndpoints and account specific endpoints are no longer required. We recommend that you send your requests directly to the regional endpoint instead.', ], 'DescribeEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoints' => [ 'shape' => '__listOfEndpoint', 'locationName' => 'endpoints', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], 'deprecated' => true, 'deprecatedMessage' => 'DescribeEndpoints and account specific endpoints are no longer required. We recommend that you send your requests directly to the regional endpoint instead.', ], 'DestinationSettings' => [ 'type' => 'structure', 'members' => [ 'S3Settings' => [ 'shape' => 'S3DestinationSettings', 'locationName' => 's3Settings', ], ], ], 'DisassociateCertificateRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', 'location' => 'uri', ], ], 'required' => [ 'Arn', ], ], 'DisassociateCertificateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DolbyVision' => [ 'type' => 'structure', 'members' => [ 'L6Metadata' => [ 'shape' => 'DolbyVisionLevel6Metadata', 'locationName' => 'l6Metadata', ], 'L6Mode' => [ 'shape' => 'DolbyVisionLevel6Mode', 'locationName' => 'l6Mode', ], 'Mapping' => [ 'shape' => 'DolbyVisionMapping', 'locationName' => 'mapping', ], 'Profile' => [ 'shape' => 'DolbyVisionProfile', 'locationName' => 'profile', ], ], ], 'DolbyVisionLevel6Metadata' => [ 'type' => 'structure', 'members' => [ 'MaxCll' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'maxCll', ], 'MaxFall' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'maxFall', ], ], ], 'DolbyVisionLevel6Mode' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'RECALCULATE', 'SPECIFY', ], ], 'DolbyVisionMapping' => [ 'type' => 'string', 'enum' => [ 'HDR10_NOMAP', 'HDR10_1000', ], ], 'DolbyVisionProfile' => [ 'type' => 'string', 'enum' => [ 'PROFILE_5', 'PROFILE_8_1', ], ], 'DropFrameTimecode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'DvbNitSettings' => [ 'type' => 'structure', 'members' => [ 'NetworkId' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'networkId', ], 'NetworkName' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'networkName', ], 'NitInterval' => [ 'shape' => '__integerMin25Max10000', 'locationName' => 'nitInterval', ], ], ], 'DvbSdtSettings' => [ 'type' => 'structure', 'members' => [ 'OutputSdt' => [ 'shape' => 'OutputSdt', 'locationName' => 'outputSdt', ], 'SdtInterval' => [ 'shape' => '__integerMin25Max2000', 'locationName' => 'sdtInterval', ], 'ServiceName' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'serviceName', ], 'ServiceProviderName' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'serviceProviderName', ], ], ], 'DvbSubDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Alignment' => [ 'shape' => 'DvbSubtitleAlignment', 'locationName' => 'alignment', ], 'ApplyFontColor' => [ 'shape' => 'DvbSubtitleApplyFontColor', 'locationName' => 'applyFontColor', ], 'BackgroundColor' => [ 'shape' => 'DvbSubtitleBackgroundColor', 'locationName' => 'backgroundColor', ], 'BackgroundOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'backgroundOpacity', ], 'DdsHandling' => [ 'shape' => 'DvbddsHandling', 'locationName' => 'ddsHandling', ], 'DdsXCoordinate' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'ddsXCoordinate', ], 'DdsYCoordinate' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'ddsYCoordinate', ], 'FallbackFont' => [ 'shape' => 'DvbSubSubtitleFallbackFont', 'locationName' => 'fallbackFont', ], 'FontColor' => [ 'shape' => 'DvbSubtitleFontColor', 'locationName' => 'fontColor', ], 'FontFileBold' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileBold', ], 'FontFileBoldItalic' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileBoldItalic', ], 'FontFileItalic' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileItalic', ], 'FontFileRegular' => [ 'shape' => '__stringPatternS3TtfHttpsTtf', 'locationName' => 'fontFileRegular', ], 'FontOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'fontOpacity', ], 'FontResolution' => [ 'shape' => '__integerMin96Max600', 'locationName' => 'fontResolution', ], 'FontScript' => [ 'shape' => 'FontScript', 'locationName' => 'fontScript', ], 'FontSize' => [ 'shape' => '__integerMin0Max96', 'locationName' => 'fontSize', ], 'Height' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'height', ], 'HexFontColor' => [ 'shape' => '__stringMin6Max8Pattern09aFAF609aFAF2', 'locationName' => 'hexFontColor', ], 'OutlineColor' => [ 'shape' => 'DvbSubtitleOutlineColor', 'locationName' => 'outlineColor', ], 'OutlineSize' => [ 'shape' => '__integerMin0Max10', 'locationName' => 'outlineSize', ], 'ShadowColor' => [ 'shape' => 'DvbSubtitleShadowColor', 'locationName' => 'shadowColor', ], 'ShadowOpacity' => [ 'shape' => '__integerMin0Max255', 'locationName' => 'shadowOpacity', ], 'ShadowXOffset' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'shadowXOffset', ], 'ShadowYOffset' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'shadowYOffset', ], 'StylePassthrough' => [ 'shape' => 'DvbSubtitleStylePassthrough', 'locationName' => 'stylePassthrough', ], 'SubtitlingType' => [ 'shape' => 'DvbSubtitlingType', 'locationName' => 'subtitlingType', ], 'TeletextSpacing' => [ 'shape' => 'DvbSubtitleTeletextSpacing', 'locationName' => 'teletextSpacing', ], 'Width' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'width', ], 'XPosition' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'xPosition', ], 'YPosition' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'yPosition', ], ], ], 'DvbSubSourceSettings' => [ 'type' => 'structure', 'members' => [ 'Pid' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'pid', ], ], ], 'DvbSubSubtitleFallbackFont' => [ 'type' => 'string', 'enum' => [ 'BEST_MATCH', 'MONOSPACED_SANSSERIF', 'MONOSPACED_SERIF', 'PROPORTIONAL_SANSSERIF', 'PROPORTIONAL_SERIF', ], ], 'DvbSubtitleAlignment' => [ 'type' => 'string', 'enum' => [ 'CENTERED', 'LEFT', 'AUTO', ], ], 'DvbSubtitleApplyFontColor' => [ 'type' => 'string', 'enum' => [ 'WHITE_TEXT_ONLY', 'ALL_TEXT', ], ], 'DvbSubtitleBackgroundColor' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BLACK', 'WHITE', 'AUTO', ], ], 'DvbSubtitleFontColor' => [ 'type' => 'string', 'enum' => [ 'WHITE', 'BLACK', 'YELLOW', 'RED', 'GREEN', 'BLUE', 'HEX', 'AUTO', ], ], 'DvbSubtitleOutlineColor' => [ 'type' => 'string', 'enum' => [ 'BLACK', 'WHITE', 'YELLOW', 'RED', 'GREEN', 'BLUE', 'AUTO', ], ], 'DvbSubtitleShadowColor' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BLACK', 'WHITE', 'AUTO', ], ], 'DvbSubtitleStylePassthrough' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'DvbSubtitleTeletextSpacing' => [ 'type' => 'string', 'enum' => [ 'FIXED_GRID', 'PROPORTIONAL', 'AUTO', ], ], 'DvbSubtitlingType' => [ 'type' => 'string', 'enum' => [ 'HEARING_IMPAIRED', 'STANDARD', ], ], 'DvbTdtSettings' => [ 'type' => 'structure', 'members' => [ 'TdtInterval' => [ 'shape' => '__integerMin1000Max30000', 'locationName' => 'tdtInterval', ], ], ], 'DvbddsHandling' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SPECIFIED', 'NO_DISPLAY_WINDOW', ], ], 'DynamicAudioSelector' => [ 'type' => 'structure', 'members' => [ 'AudioDurationCorrection' => [ 'shape' => 'AudioDurationCorrection', 'locationName' => 'audioDurationCorrection', ], 'ExternalAudioFileInput' => [ 'shape' => '__stringPatternS3Https', 'locationName' => 'externalAudioFileInput', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'Offset' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'offset', ], 'SelectorType' => [ 'shape' => 'DynamicAudioSelectorType', 'locationName' => 'selectorType', ], ], ], 'DynamicAudioSelectorType' => [ 'type' => 'string', 'enum' => [ 'ALL_TRACKS', 'LANGUAGE_CODE', ], ], 'Eac3AtmosBitstreamMode' => [ 'type' => 'string', 'enum' => [ 'COMPLETE_MAIN', ], ], 'Eac3AtmosCodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_AUTO', 'CODING_MODE_5_1_4', 'CODING_MODE_7_1_4', 'CODING_MODE_9_1_6', ], ], 'Eac3AtmosDialogueIntelligence' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Eac3AtmosDownmixControl' => [ 'type' => 'string', 'enum' => [ 'SPECIFIED', 'INITIALIZE_FROM_SOURCE', ], ], 'Eac3AtmosDynamicRangeCompressionLine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'FILM_STANDARD', 'FILM_LIGHT', 'MUSIC_STANDARD', 'MUSIC_LIGHT', 'SPEECH', ], ], 'Eac3AtmosDynamicRangeCompressionRf' => [ 'type' => 'string', 'enum' => [ 'NONE', 'FILM_STANDARD', 'FILM_LIGHT', 'MUSIC_STANDARD', 'MUSIC_LIGHT', 'SPEECH', ], ], 'Eac3AtmosDynamicRangeControl' => [ 'type' => 'string', 'enum' => [ 'SPECIFIED', 'INITIALIZE_FROM_SOURCE', ], ], 'Eac3AtmosMeteringMode' => [ 'type' => 'string', 'enum' => [ 'LEQ_A', 'ITU_BS_1770_1', 'ITU_BS_1770_2', 'ITU_BS_1770_3', 'ITU_BS_1770_4', ], ], 'Eac3AtmosSettings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin384000Max1024000', 'locationName' => 'bitrate', ], 'BitstreamMode' => [ 'shape' => 'Eac3AtmosBitstreamMode', 'locationName' => 'bitstreamMode', ], 'CodingMode' => [ 'shape' => 'Eac3AtmosCodingMode', 'locationName' => 'codingMode', ], 'DialogueIntelligence' => [ 'shape' => 'Eac3AtmosDialogueIntelligence', 'locationName' => 'dialogueIntelligence', ], 'DownmixControl' => [ 'shape' => 'Eac3AtmosDownmixControl', 'locationName' => 'downmixControl', ], 'DynamicRangeCompressionLine' => [ 'shape' => 'Eac3AtmosDynamicRangeCompressionLine', 'locationName' => 'dynamicRangeCompressionLine', ], 'DynamicRangeCompressionRf' => [ 'shape' => 'Eac3AtmosDynamicRangeCompressionRf', 'locationName' => 'dynamicRangeCompressionRf', ], 'DynamicRangeControl' => [ 'shape' => 'Eac3AtmosDynamicRangeControl', 'locationName' => 'dynamicRangeControl', ], 'LoRoCenterMixLevel' => [ 'shape' => '__doubleMinNegative6Max3', 'locationName' => 'loRoCenterMixLevel', ], 'LoRoSurroundMixLevel' => [ 'shape' => '__doubleMinNegative60MaxNegative1', 'locationName' => 'loRoSurroundMixLevel', ], 'LtRtCenterMixLevel' => [ 'shape' => '__doubleMinNegative6Max3', 'locationName' => 'ltRtCenterMixLevel', ], 'LtRtSurroundMixLevel' => [ 'shape' => '__doubleMinNegative60MaxNegative1', 'locationName' => 'ltRtSurroundMixLevel', ], 'MeteringMode' => [ 'shape' => 'Eac3AtmosMeteringMode', 'locationName' => 'meteringMode', ], 'SampleRate' => [ 'shape' => '__integerMin48000Max48000', 'locationName' => 'sampleRate', ], 'SpeechThreshold' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'speechThreshold', ], 'StereoDownmix' => [ 'shape' => 'Eac3AtmosStereoDownmix', 'locationName' => 'stereoDownmix', ], 'SurroundExMode' => [ 'shape' => 'Eac3AtmosSurroundExMode', 'locationName' => 'surroundExMode', ], ], ], 'Eac3AtmosStereoDownmix' => [ 'type' => 'string', 'enum' => [ 'NOT_INDICATED', 'STEREO', 'SURROUND', 'DPL2', ], ], 'Eac3AtmosSurroundExMode' => [ 'type' => 'string', 'enum' => [ 'NOT_INDICATED', 'ENABLED', 'DISABLED', ], ], 'Eac3AttenuationControl' => [ 'type' => 'string', 'enum' => [ 'ATTENUATE_3_DB', 'NONE', ], ], 'Eac3BitstreamMode' => [ 'type' => 'string', 'enum' => [ 'COMPLETE_MAIN', 'COMMENTARY', 'EMERGENCY', 'HEARING_IMPAIRED', 'VISUALLY_IMPAIRED', ], ], 'Eac3CodingMode' => [ 'type' => 'string', 'enum' => [ 'CODING_MODE_1_0', 'CODING_MODE_2_0', 'CODING_MODE_3_2', ], ], 'Eac3DcFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Eac3DynamicRangeCompressionLine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'FILM_STANDARD', 'FILM_LIGHT', 'MUSIC_STANDARD', 'MUSIC_LIGHT', 'SPEECH', ], ], 'Eac3DynamicRangeCompressionRf' => [ 'type' => 'string', 'enum' => [ 'NONE', 'FILM_STANDARD', 'FILM_LIGHT', 'MUSIC_STANDARD', 'MUSIC_LIGHT', 'SPEECH', ], ], 'Eac3LfeControl' => [ 'type' => 'string', 'enum' => [ 'LFE', 'NO_LFE', ], ], 'Eac3LfeFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Eac3MetadataControl' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_INPUT', 'USE_CONFIGURED', ], ], 'Eac3PassthroughControl' => [ 'type' => 'string', 'enum' => [ 'WHEN_POSSIBLE', 'NO_PASSTHROUGH', ], ], 'Eac3PhaseControl' => [ 'type' => 'string', 'enum' => [ 'SHIFT_90_DEGREES', 'NO_SHIFT', ], ], 'Eac3Settings' => [ 'type' => 'structure', 'members' => [ 'AttenuationControl' => [ 'shape' => 'Eac3AttenuationControl', 'locationName' => 'attenuationControl', ], 'Bitrate' => [ 'shape' => '__integerMin32000Max3024000', 'locationName' => 'bitrate', ], 'BitstreamMode' => [ 'shape' => 'Eac3BitstreamMode', 'locationName' => 'bitstreamMode', ], 'CodingMode' => [ 'shape' => 'Eac3CodingMode', 'locationName' => 'codingMode', ], 'DcFilter' => [ 'shape' => 'Eac3DcFilter', 'locationName' => 'dcFilter', ], 'Dialnorm' => [ 'shape' => '__integerMin1Max31', 'locationName' => 'dialnorm', ], 'DynamicRangeCompressionLine' => [ 'shape' => 'Eac3DynamicRangeCompressionLine', 'locationName' => 'dynamicRangeCompressionLine', ], 'DynamicRangeCompressionRf' => [ 'shape' => 'Eac3DynamicRangeCompressionRf', 'locationName' => 'dynamicRangeCompressionRf', ], 'LfeControl' => [ 'shape' => 'Eac3LfeControl', 'locationName' => 'lfeControl', ], 'LfeFilter' => [ 'shape' => 'Eac3LfeFilter', 'locationName' => 'lfeFilter', ], 'LoRoCenterMixLevel' => [ 'shape' => '__doubleMinNegative60Max3', 'locationName' => 'loRoCenterMixLevel', ], 'LoRoSurroundMixLevel' => [ 'shape' => '__doubleMinNegative60MaxNegative1', 'locationName' => 'loRoSurroundMixLevel', ], 'LtRtCenterMixLevel' => [ 'shape' => '__doubleMinNegative60Max3', 'locationName' => 'ltRtCenterMixLevel', ], 'LtRtSurroundMixLevel' => [ 'shape' => '__doubleMinNegative60MaxNegative1', 'locationName' => 'ltRtSurroundMixLevel', ], 'MetadataControl' => [ 'shape' => 'Eac3MetadataControl', 'locationName' => 'metadataControl', ], 'PassthroughControl' => [ 'shape' => 'Eac3PassthroughControl', 'locationName' => 'passthroughControl', ], 'PhaseControl' => [ 'shape' => 'Eac3PhaseControl', 'locationName' => 'phaseControl', ], 'SampleRate' => [ 'shape' => '__integerMin48000Max48000', 'locationName' => 'sampleRate', ], 'StereoDownmix' => [ 'shape' => 'Eac3StereoDownmix', 'locationName' => 'stereoDownmix', ], 'SurroundExMode' => [ 'shape' => 'Eac3SurroundExMode', 'locationName' => 'surroundExMode', ], 'SurroundMode' => [ 'shape' => 'Eac3SurroundMode', 'locationName' => 'surroundMode', ], ], ], 'Eac3StereoDownmix' => [ 'type' => 'string', 'enum' => [ 'NOT_INDICATED', 'LO_RO', 'LT_RT', 'DPL2', ], ], 'Eac3SurroundExMode' => [ 'type' => 'string', 'enum' => [ 'NOT_INDICATED', 'ENABLED', 'DISABLED', ], ], 'Eac3SurroundMode' => [ 'type' => 'string', 'enum' => [ 'NOT_INDICATED', 'ENABLED', 'DISABLED', ], ], 'EmbeddedConvert608To708' => [ 'type' => 'string', 'enum' => [ 'UPCONVERT', 'DISABLED', ], ], 'EmbeddedDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Destination608ChannelNumber' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'destination608ChannelNumber', ], 'Destination708ServiceNumber' => [ 'shape' => '__integerMin1Max6', 'locationName' => 'destination708ServiceNumber', ], ], ], 'EmbeddedSourceSettings' => [ 'type' => 'structure', 'members' => [ 'Convert608To708' => [ 'shape' => 'EmbeddedConvert608To708', 'locationName' => 'convert608To708', ], 'Source608ChannelNumber' => [ 'shape' => '__integerMin1Max4', 'locationName' => 'source608ChannelNumber', ], 'Source608TrackNumber' => [ 'shape' => '__integerMin1Max1', 'locationName' => 'source608TrackNumber', ], 'TerminateCaptions' => [ 'shape' => 'EmbeddedTerminateCaptions', 'locationName' => 'terminateCaptions', ], ], ], 'EmbeddedTerminateCaptions' => [ 'type' => 'string', 'enum' => [ 'END_OF_INPUT', 'DISABLED', ], ], 'EmbeddedTimecodeOverride' => [ 'type' => 'string', 'enum' => [ 'NONE', 'USE_MDPM', ], ], 'EncryptionContractConfiguration' => [ 'type' => 'structure', 'members' => [ 'SpekeAudioPreset' => [ 'shape' => 'PresetSpeke20Audio', 'locationName' => 'spekeAudioPreset', ], 'SpekeVideoPreset' => [ 'shape' => 'PresetSpeke20Video', 'locationName' => 'spekeVideoPreset', ], ], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], ], ], 'EsamManifestConfirmConditionNotification' => [ 'type' => 'structure', 'members' => [ 'MccXml' => [ 'shape' => '__stringPatternSNManifestConfirmConditionNotificationNS', 'locationName' => 'mccXml', ], ], ], 'EsamSettings' => [ 'type' => 'structure', 'members' => [ 'ManifestConfirmConditionNotification' => [ 'shape' => 'EsamManifestConfirmConditionNotification', 'locationName' => 'manifestConfirmConditionNotification', ], 'ResponseSignalPreroll' => [ 'shape' => '__integerMin0Max30000', 'locationName' => 'responseSignalPreroll', ], 'SignalProcessingNotification' => [ 'shape' => 'EsamSignalProcessingNotification', 'locationName' => 'signalProcessingNotification', ], ], ], 'EsamSignalProcessingNotification' => [ 'type' => 'structure', 'members' => [ 'SccXml' => [ 'shape' => '__stringPatternSNSignalProcessingNotificationNS', 'locationName' => 'sccXml', ], ], ], 'ExceptionBody' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'ExtendedDataServices' => [ 'type' => 'structure', 'members' => [ 'CopyProtectionAction' => [ 'shape' => 'CopyProtectionAction', 'locationName' => 'copyProtectionAction', ], 'VchipAction' => [ 'shape' => 'VchipAction', 'locationName' => 'vchipAction', ], ], ], 'F4vMoovPlacement' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE_DOWNLOAD', 'NORMAL', ], ], 'F4vSettings' => [ 'type' => 'structure', 'members' => [ 'MoovPlacement' => [ 'shape' => 'F4vMoovPlacement', 'locationName' => 'moovPlacement', ], ], ], 'FileGroupSettings' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'destination', ], 'DestinationSettings' => [ 'shape' => 'DestinationSettings', 'locationName' => 'destinationSettings', ], ], ], 'FileSourceConvert608To708' => [ 'type' => 'string', 'enum' => [ 'UPCONVERT', 'DISABLED', ], ], 'FileSourceSettings' => [ 'type' => 'structure', 'members' => [ 'ByteRateLimit' => [ 'shape' => 'CaptionSourceByteRateLimit', 'locationName' => 'byteRateLimit', ], 'Convert608To708' => [ 'shape' => 'FileSourceConvert608To708', 'locationName' => 'convert608To708', ], 'ConvertPaintToPop' => [ 'shape' => 'CaptionSourceConvertPaintOnToPopOn', 'locationName' => 'convertPaintToPop', ], 'Framerate' => [ 'shape' => 'CaptionSourceFramerate', 'locationName' => 'framerate', ], 'SourceFile' => [ 'shape' => '__stringMin14PatternS3SccSCCTtmlTTMLDfxpDFXPStlSTLSrtSRTXmlXMLSmiSMIVttVTTWebvttWEBVTTHttpsSccSCCTtmlTTMLDfxpDFXPStlSTLSrtSRTXmlXMLSmiSMIVttVTTWebvttWEBVTT', 'locationName' => 'sourceFile', ], 'TimeDelta' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'timeDelta', ], 'TimeDeltaUnits' => [ 'shape' => 'FileSourceTimeDeltaUnits', 'locationName' => 'timeDeltaUnits', ], ], ], 'FileSourceTimeDeltaUnits' => [ 'type' => 'string', 'enum' => [ 'SECONDS', 'MILLISECONDS', ], ], 'FlacSettings' => [ 'type' => 'structure', 'members' => [ 'BitDepth' => [ 'shape' => '__integerMin16Max24', 'locationName' => 'bitDepth', ], 'Channels' => [ 'shape' => '__integerMin1Max8', 'locationName' => 'channels', ], 'SampleRate' => [ 'shape' => '__integerMin22050Max48000', 'locationName' => 'sampleRate', ], ], ], 'FontScript' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'HANS', 'HANT', ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 403, ], ], 'ForceIncludeRenditionSize' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'height', ], 'Width' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'width', ], ], ], 'Format' => [ 'type' => 'string', 'enum' => [ 'mp4', 'quicktime', 'matroska', 'webm', ], ], 'FrameCaptureSettings' => [ 'type' => 'structure', 'members' => [ 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'MaxCaptures' => [ 'shape' => '__integerMin1Max10000000', 'locationName' => 'maxCaptures', ], 'Quality' => [ 'shape' => '__integerMin1Max100', 'locationName' => 'quality', ], ], ], 'FrameRate' => [ 'type' => 'structure', 'members' => [ 'Denominator' => [ 'shape' => '__integer', 'locationName' => 'denominator', ], 'Numerator' => [ 'shape' => '__integer', 'locationName' => 'numerator', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => '__string', 'locationName' => 'id', 'location' => 'uri', ], ], 'required' => [ 'Id', ], ], 'GetJobResponse' => [ 'type' => 'structure', 'members' => [ 'Job' => [ 'shape' => 'Job', 'locationName' => 'job', ], ], ], 'GetJobTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], ], 'required' => [ 'Name', ], ], 'GetJobTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'JobTemplate' => [ 'shape' => 'JobTemplate', 'locationName' => 'jobTemplate', ], ], ], 'GetPolicyRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', 'locationName' => 'policy', ], ], ], 'GetPresetRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], ], 'required' => [ 'Name', ], ], 'GetPresetResponse' => [ 'type' => 'structure', 'members' => [ 'Preset' => [ 'shape' => 'Preset', 'locationName' => 'preset', ], ], ], 'GetQueueRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], ], 'required' => [ 'Name', ], ], 'GetQueueResponse' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'Queue', 'locationName' => 'queue', ], ], ], 'GifFramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'GifFramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', ], ], 'GifSettings' => [ 'type' => 'structure', 'members' => [ 'FramerateControl' => [ 'shape' => 'GifFramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'GifFramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], ], ], 'H264AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'OFF', 'AUTO', 'LOW', 'MEDIUM', 'HIGH', 'HIGHER', 'MAX', ], ], 'H264CodecLevel' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'LEVEL_1', 'LEVEL_1_1', 'LEVEL_1_2', 'LEVEL_1_3', 'LEVEL_2', 'LEVEL_2_1', 'LEVEL_2_2', 'LEVEL_3', 'LEVEL_3_1', 'LEVEL_3_2', 'LEVEL_4', 'LEVEL_4_1', 'LEVEL_4_2', 'LEVEL_5', 'LEVEL_5_1', 'LEVEL_5_2', ], ], 'H264CodecProfile' => [ 'type' => 'string', 'enum' => [ 'BASELINE', 'HIGH', 'HIGH_10BIT', 'HIGH_422', 'HIGH_422_10BIT', 'MAIN', ], ], 'H264DynamicSubGop' => [ 'type' => 'string', 'enum' => [ 'ADAPTIVE', 'STATIC', ], ], 'H264EndOfStreamMarkers' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'SUPPRESS', ], ], 'H264EntropyEncoding' => [ 'type' => 'string', 'enum' => [ 'CABAC', 'CAVLC', ], ], 'H264FieldEncoding' => [ 'type' => 'string', 'enum' => [ 'PAFF', 'FORCE_FIELD', 'MBAFF', ], ], 'H264FlickerAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'H264FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'H264GopBReference' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', 'AUTO', ], ], 'H264InterlaceMode' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE', 'TOP_FIELD', 'BOTTOM_FIELD', 'FOLLOW_TOP_FIELD', 'FOLLOW_BOTTOM_FIELD', ], ], 'H264ParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'H264QualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PASS', 'SINGLE_PASS_HQ', 'MULTI_PASS_HQ', ], ], 'H264QvbrSettings' => [ 'type' => 'structure', 'members' => [ 'MaxAverageBitrate' => [ 'shape' => '__integerMin1000Max1152000000', 'locationName' => 'maxAverageBitrate', ], 'QvbrQualityLevel' => [ 'shape' => '__integerMin1Max10', 'locationName' => 'qvbrQualityLevel', ], 'QvbrQualityLevelFineTune' => [ 'shape' => '__doubleMin0Max1', 'locationName' => 'qvbrQualityLevelFineTune', ], ], ], 'H264RateControlMode' => [ 'type' => 'string', 'enum' => [ 'VBR', 'CBR', 'QVBR', ], ], 'H264RepeatPps' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264SaliencyAwareEncoding' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'PREFERRED', ], ], 'H264ScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'H264SceneChangeDetect' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'TRANSITION_DETECTION', ], ], 'H264Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'H264AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'BandwidthReductionFilter' => [ 'shape' => 'BandwidthReductionFilter', 'locationName' => 'bandwidthReductionFilter', ], 'Bitrate' => [ 'shape' => '__integerMin1000Max1152000000', 'locationName' => 'bitrate', ], 'CodecLevel' => [ 'shape' => 'H264CodecLevel', 'locationName' => 'codecLevel', ], 'CodecProfile' => [ 'shape' => 'H264CodecProfile', 'locationName' => 'codecProfile', ], 'DynamicSubGop' => [ 'shape' => 'H264DynamicSubGop', 'locationName' => 'dynamicSubGop', ], 'EndOfStreamMarkers' => [ 'shape' => 'H264EndOfStreamMarkers', 'locationName' => 'endOfStreamMarkers', ], 'EntropyEncoding' => [ 'shape' => 'H264EntropyEncoding', 'locationName' => 'entropyEncoding', ], 'FieldEncoding' => [ 'shape' => 'H264FieldEncoding', 'locationName' => 'fieldEncoding', ], 'FlickerAdaptiveQuantization' => [ 'shape' => 'H264FlickerAdaptiveQuantization', 'locationName' => 'flickerAdaptiveQuantization', ], 'FramerateControl' => [ 'shape' => 'H264FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'H264FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'GopBReference' => [ 'shape' => 'H264GopBReference', 'locationName' => 'gopBReference', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'gopClosedCadence', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'H264GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'HrdBufferFinalFillPercentage' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'hrdBufferFinalFillPercentage', ], 'HrdBufferInitialFillPercentage' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'hrdBufferInitialFillPercentage', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max1152000000', 'locationName' => 'hrdBufferSize', ], 'InterlaceMode' => [ 'shape' => 'H264InterlaceMode', 'locationName' => 'interlaceMode', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000Max1152000000', 'locationName' => 'maxBitrate', ], 'MinIInterval' => [ 'shape' => '__integerMin0Max30', 'locationName' => 'minIInterval', ], 'NumberBFramesBetweenReferenceFrames' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'numberBFramesBetweenReferenceFrames', ], 'NumberReferenceFrames' => [ 'shape' => '__integerMin1Max6', 'locationName' => 'numberReferenceFrames', ], 'ParControl' => [ 'shape' => 'H264ParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parNumerator', ], 'QualityTuningLevel' => [ 'shape' => 'H264QualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'QvbrSettings' => [ 'shape' => 'H264QvbrSettings', 'locationName' => 'qvbrSettings', ], 'RateControlMode' => [ 'shape' => 'H264RateControlMode', 'locationName' => 'rateControlMode', ], 'RepeatPps' => [ 'shape' => 'H264RepeatPps', 'locationName' => 'repeatPps', ], 'SaliencyAwareEncoding' => [ 'shape' => 'H264SaliencyAwareEncoding', 'locationName' => 'saliencyAwareEncoding', ], 'ScanTypeConversionMode' => [ 'shape' => 'H264ScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SceneChangeDetect' => [ 'shape' => 'H264SceneChangeDetect', 'locationName' => 'sceneChangeDetect', ], 'Slices' => [ 'shape' => '__integerMin1Max32', 'locationName' => 'slices', ], 'SlowPal' => [ 'shape' => 'H264SlowPal', 'locationName' => 'slowPal', ], 'Softness' => [ 'shape' => '__integerMin0Max128', 'locationName' => 'softness', ], 'SpatialAdaptiveQuantization' => [ 'shape' => 'H264SpatialAdaptiveQuantization', 'locationName' => 'spatialAdaptiveQuantization', ], 'Syntax' => [ 'shape' => 'H264Syntax', 'locationName' => 'syntax', ], 'Telecine' => [ 'shape' => 'H264Telecine', 'locationName' => 'telecine', ], 'TemporalAdaptiveQuantization' => [ 'shape' => 'H264TemporalAdaptiveQuantization', 'locationName' => 'temporalAdaptiveQuantization', ], 'UnregisteredSeiTimecode' => [ 'shape' => 'H264UnregisteredSeiTimecode', 'locationName' => 'unregisteredSeiTimecode', ], 'WriteMp4PackagingType' => [ 'shape' => 'H264WriteMp4PackagingType', 'locationName' => 'writeMp4PackagingType', ], ], ], 'H264SlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264SpatialAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264Syntax' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'RP2027', ], ], 'H264Telecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SOFT', 'HARD', ], ], 'H264TemporalAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264UnregisteredSeiTimecode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H264WriteMp4PackagingType' => [ 'type' => 'string', 'enum' => [ 'AVC1', 'AVC3', ], ], 'H265AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'OFF', 'LOW', 'MEDIUM', 'HIGH', 'HIGHER', 'MAX', 'AUTO', ], ], 'H265AlternateTransferFunctionSei' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265CodecLevel' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'LEVEL_1', 'LEVEL_2', 'LEVEL_2_1', 'LEVEL_3', 'LEVEL_3_1', 'LEVEL_4', 'LEVEL_4_1', 'LEVEL_5', 'LEVEL_5_1', 'LEVEL_5_2', 'LEVEL_6', 'LEVEL_6_1', 'LEVEL_6_2', ], ], 'H265CodecProfile' => [ 'type' => 'string', 'enum' => [ 'MAIN_MAIN', 'MAIN_HIGH', 'MAIN10_MAIN', 'MAIN10_HIGH', 'MAIN_422_8BIT_MAIN', 'MAIN_422_8BIT_HIGH', 'MAIN_422_10BIT_MAIN', 'MAIN_422_10BIT_HIGH', ], ], 'H265Deblocking' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'H265DynamicSubGop' => [ 'type' => 'string', 'enum' => [ 'ADAPTIVE', 'STATIC', ], ], 'H265EndOfStreamMarkers' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'SUPPRESS', ], ], 'H265FlickerAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'H265FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'H265GopBReference' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', 'AUTO', ], ], 'H265InterlaceMode' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE', 'TOP_FIELD', 'BOTTOM_FIELD', 'FOLLOW_TOP_FIELD', 'FOLLOW_BOTTOM_FIELD', ], ], 'H265ParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'H265QualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PASS', 'SINGLE_PASS_HQ', 'MULTI_PASS_HQ', ], ], 'H265QvbrSettings' => [ 'type' => 'structure', 'members' => [ 'MaxAverageBitrate' => [ 'shape' => '__integerMin1000Max1466400000', 'locationName' => 'maxAverageBitrate', ], 'QvbrQualityLevel' => [ 'shape' => '__integerMin1Max10', 'locationName' => 'qvbrQualityLevel', ], 'QvbrQualityLevelFineTune' => [ 'shape' => '__doubleMin0Max1', 'locationName' => 'qvbrQualityLevelFineTune', ], ], ], 'H265RateControlMode' => [ 'type' => 'string', 'enum' => [ 'VBR', 'CBR', 'QVBR', ], ], 'H265SampleAdaptiveOffsetFilterMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'ADAPTIVE', 'OFF', ], ], 'H265ScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'H265SceneChangeDetect' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'TRANSITION_DETECTION', ], ], 'H265Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'H265AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'AlternateTransferFunctionSei' => [ 'shape' => 'H265AlternateTransferFunctionSei', 'locationName' => 'alternateTransferFunctionSei', ], 'BandwidthReductionFilter' => [ 'shape' => 'BandwidthReductionFilter', 'locationName' => 'bandwidthReductionFilter', ], 'Bitrate' => [ 'shape' => '__integerMin1000Max1466400000', 'locationName' => 'bitrate', ], 'CodecLevel' => [ 'shape' => 'H265CodecLevel', 'locationName' => 'codecLevel', ], 'CodecProfile' => [ 'shape' => 'H265CodecProfile', 'locationName' => 'codecProfile', ], 'Deblocking' => [ 'shape' => 'H265Deblocking', 'locationName' => 'deblocking', ], 'DynamicSubGop' => [ 'shape' => 'H265DynamicSubGop', 'locationName' => 'dynamicSubGop', ], 'EndOfStreamMarkers' => [ 'shape' => 'H265EndOfStreamMarkers', 'locationName' => 'endOfStreamMarkers', ], 'FlickerAdaptiveQuantization' => [ 'shape' => 'H265FlickerAdaptiveQuantization', 'locationName' => 'flickerAdaptiveQuantization', ], 'FramerateControl' => [ 'shape' => 'H265FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'H265FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'GopBReference' => [ 'shape' => 'H265GopBReference', 'locationName' => 'gopBReference', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'gopClosedCadence', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'H265GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'HrdBufferFinalFillPercentage' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'hrdBufferFinalFillPercentage', ], 'HrdBufferInitialFillPercentage' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'hrdBufferInitialFillPercentage', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max1466400000', 'locationName' => 'hrdBufferSize', ], 'InterlaceMode' => [ 'shape' => 'H265InterlaceMode', 'locationName' => 'interlaceMode', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000Max1466400000', 'locationName' => 'maxBitrate', ], 'MinIInterval' => [ 'shape' => '__integerMin0Max30', 'locationName' => 'minIInterval', ], 'NumberBFramesBetweenReferenceFrames' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'numberBFramesBetweenReferenceFrames', ], 'NumberReferenceFrames' => [ 'shape' => '__integerMin1Max6', 'locationName' => 'numberReferenceFrames', ], 'ParControl' => [ 'shape' => 'H265ParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parNumerator', ], 'QualityTuningLevel' => [ 'shape' => 'H265QualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'QvbrSettings' => [ 'shape' => 'H265QvbrSettings', 'locationName' => 'qvbrSettings', ], 'RateControlMode' => [ 'shape' => 'H265RateControlMode', 'locationName' => 'rateControlMode', ], 'SampleAdaptiveOffsetFilterMode' => [ 'shape' => 'H265SampleAdaptiveOffsetFilterMode', 'locationName' => 'sampleAdaptiveOffsetFilterMode', ], 'ScanTypeConversionMode' => [ 'shape' => 'H265ScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SceneChangeDetect' => [ 'shape' => 'H265SceneChangeDetect', 'locationName' => 'sceneChangeDetect', ], 'Slices' => [ 'shape' => '__integerMin1Max32', 'locationName' => 'slices', ], 'SlowPal' => [ 'shape' => 'H265SlowPal', 'locationName' => 'slowPal', ], 'SpatialAdaptiveQuantization' => [ 'shape' => 'H265SpatialAdaptiveQuantization', 'locationName' => 'spatialAdaptiveQuantization', ], 'Telecine' => [ 'shape' => 'H265Telecine', 'locationName' => 'telecine', ], 'TemporalAdaptiveQuantization' => [ 'shape' => 'H265TemporalAdaptiveQuantization', 'locationName' => 'temporalAdaptiveQuantization', ], 'TemporalIds' => [ 'shape' => 'H265TemporalIds', 'locationName' => 'temporalIds', ], 'Tiles' => [ 'shape' => 'H265Tiles', 'locationName' => 'tiles', ], 'UnregisteredSeiTimecode' => [ 'shape' => 'H265UnregisteredSeiTimecode', 'locationName' => 'unregisteredSeiTimecode', ], 'WriteMp4PackagingType' => [ 'shape' => 'H265WriteMp4PackagingType', 'locationName' => 'writeMp4PackagingType', ], ], ], 'H265SlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265SpatialAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265Telecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SOFT', 'HARD', ], ], 'H265TemporalAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265TemporalIds' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265Tiles' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265UnregisteredSeiTimecode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'H265WriteMp4PackagingType' => [ 'type' => 'string', 'enum' => [ 'HVC1', 'HEV1', ], ], 'HDRToSDRToneMapper' => [ 'type' => 'string', 'enum' => [ 'PRESERVE_DETAILS', 'VIBRANT', ], ], 'Hdr10Metadata' => [ 'type' => 'structure', 'members' => [ 'BluePrimaryX' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'bluePrimaryX', ], 'BluePrimaryY' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'bluePrimaryY', ], 'GreenPrimaryX' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'greenPrimaryX', ], 'GreenPrimaryY' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'greenPrimaryY', ], 'MaxContentLightLevel' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'maxContentLightLevel', ], 'MaxFrameAverageLightLevel' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'maxFrameAverageLightLevel', ], 'MaxLuminance' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'maxLuminance', ], 'MinLuminance' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'minLuminance', ], 'RedPrimaryX' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'redPrimaryX', ], 'RedPrimaryY' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'redPrimaryY', ], 'WhitePointX' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'whitePointX', ], 'WhitePointY' => [ 'shape' => '__integerMin0Max50000', 'locationName' => 'whitePointY', ], ], ], 'Hdr10Plus' => [ 'type' => 'structure', 'members' => [ 'MasteringMonitorNits' => [ 'shape' => '__integerMin0Max4000', 'locationName' => 'masteringMonitorNits', ], 'TargetMonitorNits' => [ 'shape' => '__integerMin0Max4000', 'locationName' => 'targetMonitorNits', ], ], ], 'HlsAdMarkers' => [ 'type' => 'string', 'enum' => [ 'ELEMENTAL', 'ELEMENTAL_SCTE35', ], ], 'HlsAdditionalManifest' => [ 'type' => 'structure', 'members' => [ 'ManifestNameModifier' => [ 'shape' => '__stringMin1', 'locationName' => 'manifestNameModifier', ], 'SelectedOutputs' => [ 'shape' => '__listOf__stringMin1', 'locationName' => 'selectedOutputs', ], ], ], 'HlsAudioOnlyContainer' => [ 'type' => 'string', 'enum' => [ 'AUTOMATIC', 'M2TS', ], ], 'HlsAudioOnlyHeader' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'HlsAudioTrackType' => [ 'type' => 'string', 'enum' => [ 'ALTERNATE_AUDIO_AUTO_SELECT_DEFAULT', 'ALTERNATE_AUDIO_AUTO_SELECT', 'ALTERNATE_AUDIO_NOT_AUTO_SELECT', 'AUDIO_ONLY_VARIANT_STREAM', ], ], 'HlsCaptionLanguageMapping' => [ 'type' => 'structure', 'members' => [ 'CaptionChannel' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'captionChannel', ], 'CustomLanguageCode' => [ 'shape' => '__stringMin3Max3PatternAZaZ3', 'locationName' => 'customLanguageCode', ], 'LanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'languageCode', ], 'LanguageDescription' => [ 'shape' => '__string', 'locationName' => 'languageDescription', ], ], ], 'HlsCaptionLanguageSetting' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'OMIT', 'NONE', ], ], 'HlsCaptionSegmentLengthControl' => [ 'type' => 'string', 'enum' => [ 'LARGE_SEGMENTS', 'MATCH_VIDEO', ], ], 'HlsClientCache' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'HlsCodecSpecification' => [ 'type' => 'string', 'enum' => [ 'RFC_6381', 'RFC_4281', ], ], 'HlsDescriptiveVideoServiceFlag' => [ 'type' => 'string', 'enum' => [ 'DONT_FLAG', 'FLAG', ], ], 'HlsDirectoryStructure' => [ 'type' => 'string', 'enum' => [ 'SINGLE_DIRECTORY', 'SUBDIRECTORY_PER_STREAM', ], ], 'HlsEncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'ConstantInitializationVector' => [ 'shape' => '__stringMin32Max32Pattern09aFAF32', 'locationName' => 'constantInitializationVector', ], 'EncryptionMethod' => [ 'shape' => 'HlsEncryptionType', 'locationName' => 'encryptionMethod', ], 'InitializationVectorInManifest' => [ 'shape' => 'HlsInitializationVectorInManifest', 'locationName' => 'initializationVectorInManifest', ], 'OfflineEncrypted' => [ 'shape' => 'HlsOfflineEncrypted', 'locationName' => 'offlineEncrypted', ], 'SpekeKeyProvider' => [ 'shape' => 'SpekeKeyProvider', 'locationName' => 'spekeKeyProvider', ], 'StaticKeyProvider' => [ 'shape' => 'StaticKeyProvider', 'locationName' => 'staticKeyProvider', ], 'Type' => [ 'shape' => 'HlsKeyProviderType', 'locationName' => 'type', ], ], ], 'HlsEncryptionType' => [ 'type' => 'string', 'enum' => [ 'AES128', 'SAMPLE_AES', ], ], 'HlsGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AdMarkers' => [ 'shape' => '__listOfHlsAdMarkers', 'locationName' => 'adMarkers', ], 'AdditionalManifests' => [ 'shape' => '__listOfHlsAdditionalManifest', 'locationName' => 'additionalManifests', ], 'AudioOnlyHeader' => [ 'shape' => 'HlsAudioOnlyHeader', 'locationName' => 'audioOnlyHeader', ], 'BaseUrl' => [ 'shape' => '__string', 'locationName' => 'baseUrl', ], 'CaptionLanguageMappings' => [ 'shape' => '__listOfHlsCaptionLanguageMapping', 'locationName' => 'captionLanguageMappings', ], 'CaptionLanguageSetting' => [ 'shape' => 'HlsCaptionLanguageSetting', 'locationName' => 'captionLanguageSetting', ], 'CaptionSegmentLengthControl' => [ 'shape' => 'HlsCaptionSegmentLengthControl', 'locationName' => 'captionSegmentLengthControl', ], 'ClientCache' => [ 'shape' => 'HlsClientCache', 'locationName' => 'clientCache', ], 'CodecSpecification' => [ 'shape' => 'HlsCodecSpecification', 'locationName' => 'codecSpecification', ], 'Destination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'destination', ], 'DestinationSettings' => [ 'shape' => 'DestinationSettings', 'locationName' => 'destinationSettings', ], 'DirectoryStructure' => [ 'shape' => 'HlsDirectoryStructure', 'locationName' => 'directoryStructure', ], 'Encryption' => [ 'shape' => 'HlsEncryptionSettings', 'locationName' => 'encryption', ], 'ImageBasedTrickPlay' => [ 'shape' => 'HlsImageBasedTrickPlay', 'locationName' => 'imageBasedTrickPlay', ], 'ImageBasedTrickPlaySettings' => [ 'shape' => 'HlsImageBasedTrickPlaySettings', 'locationName' => 'imageBasedTrickPlaySettings', ], 'ManifestCompression' => [ 'shape' => 'HlsManifestCompression', 'locationName' => 'manifestCompression', ], 'ManifestDurationFormat' => [ 'shape' => 'HlsManifestDurationFormat', 'locationName' => 'manifestDurationFormat', ], 'MinFinalSegmentLength' => [ 'shape' => '__doubleMin0Max2147483647', 'locationName' => 'minFinalSegmentLength', ], 'MinSegmentLength' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'minSegmentLength', ], 'OutputSelection' => [ 'shape' => 'HlsOutputSelection', 'locationName' => 'outputSelection', ], 'ProgramDateTime' => [ 'shape' => 'HlsProgramDateTime', 'locationName' => 'programDateTime', ], 'ProgramDateTimePeriod' => [ 'shape' => '__integerMin0Max3600', 'locationName' => 'programDateTimePeriod', ], 'ProgressiveWriteHlsManifest' => [ 'shape' => 'HlsProgressiveWriteHlsManifest', 'locationName' => 'progressiveWriteHlsManifest', ], 'SegmentControl' => [ 'shape' => 'HlsSegmentControl', 'locationName' => 'segmentControl', ], 'SegmentLength' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'segmentLength', ], 'SegmentLengthControl' => [ 'shape' => 'HlsSegmentLengthControl', 'locationName' => 'segmentLengthControl', ], 'SegmentsPerSubdirectory' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'segmentsPerSubdirectory', ], 'StreamInfResolution' => [ 'shape' => 'HlsStreamInfResolution', 'locationName' => 'streamInfResolution', ], 'TargetDurationCompatibilityMode' => [ 'shape' => 'HlsTargetDurationCompatibilityMode', 'locationName' => 'targetDurationCompatibilityMode', ], 'TimedMetadataId3Frame' => [ 'shape' => 'HlsTimedMetadataId3Frame', 'locationName' => 'timedMetadataId3Frame', ], 'TimedMetadataId3Period' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'timedMetadataId3Period', ], 'TimestampDeltaMilliseconds' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'timestampDeltaMilliseconds', ], ], ], 'HlsIFrameOnlyManifest' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'HlsImageBasedTrickPlay' => [ 'type' => 'string', 'enum' => [ 'NONE', 'THUMBNAIL', 'THUMBNAIL_AND_FULLFRAME', 'ADVANCED', ], ], 'HlsImageBasedTrickPlaySettings' => [ 'type' => 'structure', 'members' => [ 'IntervalCadence' => [ 'shape' => 'HlsIntervalCadence', 'locationName' => 'intervalCadence', ], 'ThumbnailHeight' => [ 'shape' => '__integerMin2Max4096', 'locationName' => 'thumbnailHeight', ], 'ThumbnailInterval' => [ 'shape' => '__doubleMin0Max2147483647', 'locationName' => 'thumbnailInterval', ], 'ThumbnailWidth' => [ 'shape' => '__integerMin8Max4096', 'locationName' => 'thumbnailWidth', ], 'TileHeight' => [ 'shape' => '__integerMin1Max2048', 'locationName' => 'tileHeight', ], 'TileWidth' => [ 'shape' => '__integerMin1Max512', 'locationName' => 'tileWidth', ], ], ], 'HlsInitializationVectorInManifest' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'HlsIntervalCadence' => [ 'type' => 'string', 'enum' => [ 'FOLLOW_IFRAME', 'FOLLOW_CUSTOM', ], ], 'HlsKeyProviderType' => [ 'type' => 'string', 'enum' => [ 'SPEKE', 'STATIC_KEY', ], ], 'HlsManifestCompression' => [ 'type' => 'string', 'enum' => [ 'GZIP', 'NONE', ], ], 'HlsManifestDurationFormat' => [ 'type' => 'string', 'enum' => [ 'FLOATING_POINT', 'INTEGER', ], ], 'HlsOfflineEncrypted' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'HlsOutputSelection' => [ 'type' => 'string', 'enum' => [ 'MANIFESTS_AND_SEGMENTS', 'SEGMENTS_ONLY', ], ], 'HlsProgramDateTime' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'HlsProgressiveWriteHlsManifest' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'HlsRenditionGroupSettings' => [ 'type' => 'structure', 'members' => [ 'RenditionGroupId' => [ 'shape' => '__string', 'locationName' => 'renditionGroupId', ], 'RenditionLanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'renditionLanguageCode', ], 'RenditionName' => [ 'shape' => '__string', 'locationName' => 'renditionName', ], ], ], 'HlsSegmentControl' => [ 'type' => 'string', 'enum' => [ 'SINGLE_FILE', 'SEGMENTED_FILES', ], ], 'HlsSegmentLengthControl' => [ 'type' => 'string', 'enum' => [ 'EXACT', 'GOP_MULTIPLE', ], ], 'HlsSettings' => [ 'type' => 'structure', 'members' => [ 'AudioGroupId' => [ 'shape' => '__string', 'locationName' => 'audioGroupId', ], 'AudioOnlyContainer' => [ 'shape' => 'HlsAudioOnlyContainer', 'locationName' => 'audioOnlyContainer', ], 'AudioRenditionSets' => [ 'shape' => '__string', 'locationName' => 'audioRenditionSets', ], 'AudioTrackType' => [ 'shape' => 'HlsAudioTrackType', 'locationName' => 'audioTrackType', ], 'DescriptiveVideoServiceFlag' => [ 'shape' => 'HlsDescriptiveVideoServiceFlag', 'locationName' => 'descriptiveVideoServiceFlag', ], 'IFrameOnlyManifest' => [ 'shape' => 'HlsIFrameOnlyManifest', 'locationName' => 'iFrameOnlyManifest', ], 'SegmentModifier' => [ 'shape' => '__string', 'locationName' => 'segmentModifier', ], ], ], 'HlsStreamInfResolution' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'HlsTargetDurationCompatibilityMode' => [ 'type' => 'string', 'enum' => [ 'LEGACY', 'SPEC_COMPLIANT', ], ], 'HlsTimedMetadataId3Frame' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PRIV', 'TDRL', ], ], 'HopDestination' => [ 'type' => 'structure', 'members' => [ 'Priority' => [ 'shape' => '__integerMinNegative50Max50', 'locationName' => 'priority', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', ], 'WaitMinutes' => [ 'shape' => '__integer', 'locationName' => 'waitMinutes', ], ], ], 'Id3Insertion' => [ 'type' => 'structure', 'members' => [ 'Id3' => [ 'shape' => '__stringPatternAZaZ0902', 'locationName' => 'id3', ], 'Timecode' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'timecode', ], ], ], 'ImageInserter' => [ 'type' => 'structure', 'members' => [ 'InsertableImages' => [ 'shape' => '__listOfInsertableImage', 'locationName' => 'insertableImages', ], 'SdrReferenceWhiteLevel' => [ 'shape' => '__integerMin100Max1000', 'locationName' => 'sdrReferenceWhiteLevel', ], ], ], 'ImscAccessibilitySubs' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'ImscDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Accessibility' => [ 'shape' => 'ImscAccessibilitySubs', 'locationName' => 'accessibility', ], 'StylePassthrough' => [ 'shape' => 'ImscStylePassthrough', 'locationName' => 'stylePassthrough', ], ], ], 'ImscStylePassthrough' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Input' => [ 'type' => 'structure', 'members' => [ 'AdvancedInputFilter' => [ 'shape' => 'AdvancedInputFilter', 'locationName' => 'advancedInputFilter', ], 'AdvancedInputFilterSettings' => [ 'shape' => 'AdvancedInputFilterSettings', 'locationName' => 'advancedInputFilterSettings', ], 'AudioSelectorGroups' => [ 'shape' => '__mapOfAudioSelectorGroup', 'locationName' => 'audioSelectorGroups', ], 'AudioSelectors' => [ 'shape' => '__mapOfAudioSelector', 'locationName' => 'audioSelectors', ], 'CaptionSelectors' => [ 'shape' => '__mapOfCaptionSelector', 'locationName' => 'captionSelectors', ], 'Crop' => [ 'shape' => 'Rectangle', 'locationName' => 'crop', ], 'DeblockFilter' => [ 'shape' => 'InputDeblockFilter', 'locationName' => 'deblockFilter', ], 'DecryptionSettings' => [ 'shape' => 'InputDecryptionSettings', 'locationName' => 'decryptionSettings', ], 'DenoiseFilter' => [ 'shape' => 'InputDenoiseFilter', 'locationName' => 'denoiseFilter', ], 'DolbyVisionMetadataXml' => [ 'shape' => '__stringMin14PatternS3XmlXMLHttpsXmlXML', 'locationName' => 'dolbyVisionMetadataXml', ], 'DynamicAudioSelectors' => [ 'shape' => '__mapOfDynamicAudioSelector', 'locationName' => 'dynamicAudioSelectors', ], 'FileInput' => [ 'shape' => '__stringMax2048PatternS3Https', 'locationName' => 'fileInput', ], 'FilterEnable' => [ 'shape' => 'InputFilterEnable', 'locationName' => 'filterEnable', ], 'FilterStrength' => [ 'shape' => '__integerMin0Max5', 'locationName' => 'filterStrength', ], 'ImageInserter' => [ 'shape' => 'ImageInserter', 'locationName' => 'imageInserter', ], 'InputClippings' => [ 'shape' => '__listOfInputClipping', 'locationName' => 'inputClippings', ], 'InputScanType' => [ 'shape' => 'InputScanType', 'locationName' => 'inputScanType', ], 'Position' => [ 'shape' => 'Rectangle', 'locationName' => 'position', ], 'ProgramNumber' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'programNumber', ], 'PsiControl' => [ 'shape' => 'InputPsiControl', 'locationName' => 'psiControl', ], 'SupplementalImps' => [ 'shape' => '__listOf__stringPatternS3ASSETMAPXml', 'locationName' => 'supplementalImps', ], 'TimecodeSource' => [ 'shape' => 'InputTimecodeSource', 'locationName' => 'timecodeSource', ], 'TimecodeStart' => [ 'shape' => '__stringMin11Max11Pattern01D20305D205D', 'locationName' => 'timecodeStart', ], 'VideoGenerator' => [ 'shape' => 'InputVideoGenerator', 'locationName' => 'videoGenerator', ], 'VideoOverlays' => [ 'shape' => '__listOfVideoOverlay', 'locationName' => 'videoOverlays', ], 'VideoSelector' => [ 'shape' => 'VideoSelector', 'locationName' => 'videoSelector', ], ], ], 'InputClipping' => [ 'type' => 'structure', 'members' => [ 'EndTimecode' => [ 'shape' => '__stringPattern010920405090509092090909', 'locationName' => 'endTimecode', ], 'StartTimecode' => [ 'shape' => '__stringPattern010920405090509092090909', 'locationName' => 'startTimecode', ], ], ], 'InputDeblockFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'InputDecryptionSettings' => [ 'type' => 'structure', 'members' => [ 'DecryptionMode' => [ 'shape' => 'DecryptionMode', 'locationName' => 'decryptionMode', ], 'EncryptedDecryptionKey' => [ 'shape' => '__stringMin24Max512PatternAZaZ0902', 'locationName' => 'encryptedDecryptionKey', ], 'InitializationVector' => [ 'shape' => '__stringMin16Max24PatternAZaZ0922AZaZ0916', 'locationName' => 'initializationVector', ], 'KmsKeyRegion' => [ 'shape' => '__stringMin9Max19PatternAZ26EastWestCentralNorthSouthEastWest1912', 'locationName' => 'kmsKeyRegion', ], ], ], 'InputDenoiseFilter' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'InputFilterEnable' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'DISABLE', 'FORCE', ], ], 'InputPolicy' => [ 'type' => 'string', 'enum' => [ 'ALLOWED', 'DISALLOWED', ], ], 'InputPsiControl' => [ 'type' => 'string', 'enum' => [ 'IGNORE_PSI', 'USE_PSI', ], ], 'InputRotate' => [ 'type' => 'string', 'enum' => [ 'DEGREE_0', 'DEGREES_90', 'DEGREES_180', 'DEGREES_270', 'AUTO', ], ], 'InputSampleRange' => [ 'type' => 'string', 'enum' => [ 'FOLLOW', 'FULL_RANGE', 'LIMITED_RANGE', ], ], 'InputScanType' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'PSF', ], ], 'InputTemplate' => [ 'type' => 'structure', 'members' => [ 'AdvancedInputFilter' => [ 'shape' => 'AdvancedInputFilter', 'locationName' => 'advancedInputFilter', ], 'AdvancedInputFilterSettings' => [ 'shape' => 'AdvancedInputFilterSettings', 'locationName' => 'advancedInputFilterSettings', ], 'AudioSelectorGroups' => [ 'shape' => '__mapOfAudioSelectorGroup', 'locationName' => 'audioSelectorGroups', ], 'AudioSelectors' => [ 'shape' => '__mapOfAudioSelector', 'locationName' => 'audioSelectors', ], 'CaptionSelectors' => [ 'shape' => '__mapOfCaptionSelector', 'locationName' => 'captionSelectors', ], 'Crop' => [ 'shape' => 'Rectangle', 'locationName' => 'crop', ], 'DeblockFilter' => [ 'shape' => 'InputDeblockFilter', 'locationName' => 'deblockFilter', ], 'DenoiseFilter' => [ 'shape' => 'InputDenoiseFilter', 'locationName' => 'denoiseFilter', ], 'DolbyVisionMetadataXml' => [ 'shape' => '__stringMin14PatternS3XmlXMLHttpsXmlXML', 'locationName' => 'dolbyVisionMetadataXml', ], 'DynamicAudioSelectors' => [ 'shape' => '__mapOfDynamicAudioSelector', 'locationName' => 'dynamicAudioSelectors', ], 'FilterEnable' => [ 'shape' => 'InputFilterEnable', 'locationName' => 'filterEnable', ], 'FilterStrength' => [ 'shape' => '__integerMin0Max5', 'locationName' => 'filterStrength', ], 'ImageInserter' => [ 'shape' => 'ImageInserter', 'locationName' => 'imageInserter', ], 'InputClippings' => [ 'shape' => '__listOfInputClipping', 'locationName' => 'inputClippings', ], 'InputScanType' => [ 'shape' => 'InputScanType', 'locationName' => 'inputScanType', ], 'Position' => [ 'shape' => 'Rectangle', 'locationName' => 'position', ], 'ProgramNumber' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'programNumber', ], 'PsiControl' => [ 'shape' => 'InputPsiControl', 'locationName' => 'psiControl', ], 'TimecodeSource' => [ 'shape' => 'InputTimecodeSource', 'locationName' => 'timecodeSource', ], 'TimecodeStart' => [ 'shape' => '__stringMin11Max11Pattern01D20305D205D', 'locationName' => 'timecodeStart', ], 'VideoOverlays' => [ 'shape' => '__listOfVideoOverlay', 'locationName' => 'videoOverlays', ], 'VideoSelector' => [ 'shape' => 'VideoSelector', 'locationName' => 'videoSelector', ], ], ], 'InputTimecodeSource' => [ 'type' => 'string', 'enum' => [ 'EMBEDDED', 'ZEROBASED', 'SPECIFIEDSTART', ], ], 'InputVideoGenerator' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => '__integerMin1Max32', 'locationName' => 'channels', ], 'Duration' => [ 'shape' => '__integerMin50Max86400000', 'locationName' => 'duration', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max1001', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max60000', 'locationName' => 'framerateNumerator', ], 'SampleRate' => [ 'shape' => '__integerMin32000Max48000', 'locationName' => 'sampleRate', ], ], ], 'InsertableImage' => [ 'type' => 'structure', 'members' => [ 'Duration' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'duration', ], 'FadeIn' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'fadeIn', ], 'FadeOut' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'fadeOut', ], 'Height' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'height', ], 'ImageInserterInput' => [ 'shape' => '__stringMin14PatternS3BmpBMPPngPNGTgaTGAHttpsBmpBMPPngPNGTgaTGA', 'locationName' => 'imageInserterInput', ], 'ImageX' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'imageX', ], 'ImageY' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'imageY', ], 'Layer' => [ 'shape' => '__integerMin0Max99', 'locationName' => 'layer', ], 'Opacity' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'opacity', ], 'StartTime' => [ 'shape' => '__stringPattern01D20305D205D', 'locationName' => 'startTime', ], 'Width' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'width', ], ], ], 'InternalServerErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 500, ], ], 'Job' => [ 'type' => 'structure', 'members' => [ 'AccelerationSettings' => [ 'shape' => 'AccelerationSettings', 'locationName' => 'accelerationSettings', ], 'AccelerationStatus' => [ 'shape' => 'AccelerationStatus', 'locationName' => 'accelerationStatus', ], 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'BillingTagsSource' => [ 'shape' => 'BillingTagsSource', 'locationName' => 'billingTagsSource', ], 'ClientRequestToken' => [ 'shape' => '__string', 'locationName' => 'clientRequestToken', ], 'CreatedAt' => [ 'shape' => '__timestampUnix', 'locationName' => 'createdAt', ], 'CurrentPhase' => [ 'shape' => 'JobPhase', 'locationName' => 'currentPhase', ], 'ErrorCode' => [ 'shape' => '__integer', 'locationName' => 'errorCode', ], 'ErrorMessage' => [ 'shape' => '__string', 'locationName' => 'errorMessage', ], 'HopDestinations' => [ 'shape' => '__listOfHopDestination', 'locationName' => 'hopDestinations', ], 'Id' => [ 'shape' => '__string', 'locationName' => 'id', ], 'JobEngineVersionRequested' => [ 'shape' => '__string', 'locationName' => 'jobEngineVersionRequested', ], 'JobEngineVersionUsed' => [ 'shape' => '__string', 'locationName' => 'jobEngineVersionUsed', ], 'JobPercentComplete' => [ 'shape' => '__integer', 'locationName' => 'jobPercentComplete', ], 'JobTemplate' => [ 'shape' => '__string', 'locationName' => 'jobTemplate', ], 'Messages' => [ 'shape' => 'JobMessages', 'locationName' => 'messages', ], 'OutputGroupDetails' => [ 'shape' => '__listOfOutputGroupDetail', 'locationName' => 'outputGroupDetails', ], 'Priority' => [ 'shape' => '__integerMinNegative50Max50', 'locationName' => 'priority', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', ], 'QueueTransitions' => [ 'shape' => '__listOfQueueTransition', 'locationName' => 'queueTransitions', ], 'RetryCount' => [ 'shape' => '__integer', 'locationName' => 'retryCount', ], 'Role' => [ 'shape' => '__string', 'locationName' => 'role', ], 'Settings' => [ 'shape' => 'JobSettings', 'locationName' => 'settings', ], 'SimulateReservedQueue' => [ 'shape' => 'SimulateReservedQueue', 'locationName' => 'simulateReservedQueue', ], 'Status' => [ 'shape' => 'JobStatus', 'locationName' => 'status', ], 'StatusUpdateInterval' => [ 'shape' => 'StatusUpdateInterval', 'locationName' => 'statusUpdateInterval', ], 'Timing' => [ 'shape' => 'Timing', 'locationName' => 'timing', ], 'UserMetadata' => [ 'shape' => '__mapOf__string', 'locationName' => 'userMetadata', ], 'Warnings' => [ 'shape' => '__listOfWarningGroup', 'locationName' => 'warnings', ], ], 'required' => [ 'Settings', 'Role', ], ], 'JobEngineVersion' => [ 'type' => 'structure', 'members' => [ 'ExpirationDate' => [ 'shape' => '__timestampUnix', 'locationName' => 'expirationDate', ], 'Version' => [ 'shape' => '__string', 'locationName' => 'version', ], ], ], 'JobMessages' => [ 'type' => 'structure', 'members' => [ 'Info' => [ 'shape' => '__listOf__string', 'locationName' => 'info', ], 'Warning' => [ 'shape' => '__listOf__string', 'locationName' => 'warning', ], ], ], 'JobPhase' => [ 'type' => 'string', 'enum' => [ 'PROBING', 'TRANSCODING', 'UPLOADING', ], ], 'JobSettings' => [ 'type' => 'structure', 'members' => [ 'AdAvailOffset' => [ 'shape' => '__integerMinNegative1000Max1000', 'locationName' => 'adAvailOffset', ], 'AvailBlanking' => [ 'shape' => 'AvailBlanking', 'locationName' => 'availBlanking', ], 'ColorConversion3DLUTSettings' => [ 'shape' => '__listOfColorConversion3DLUTSetting', 'locationName' => 'colorConversion3DLUTSettings', ], 'Esam' => [ 'shape' => 'EsamSettings', 'locationName' => 'esam', ], 'ExtendedDataServices' => [ 'shape' => 'ExtendedDataServices', 'locationName' => 'extendedDataServices', ], 'FollowSource' => [ 'shape' => '__integerMin1Max150', 'locationName' => 'followSource', ], 'Inputs' => [ 'shape' => '__listOfInput', 'locationName' => 'inputs', ], 'KantarWatermark' => [ 'shape' => 'KantarWatermarkSettings', 'locationName' => 'kantarWatermark', ], 'MotionImageInserter' => [ 'shape' => 'MotionImageInserter', 'locationName' => 'motionImageInserter', ], 'NielsenConfiguration' => [ 'shape' => 'NielsenConfiguration', 'locationName' => 'nielsenConfiguration', ], 'NielsenNonLinearWatermark' => [ 'shape' => 'NielsenNonLinearWatermarkSettings', 'locationName' => 'nielsenNonLinearWatermark', ], 'OutputGroups' => [ 'shape' => '__listOfOutputGroup', 'locationName' => 'outputGroups', ], 'TimecodeConfig' => [ 'shape' => 'TimecodeConfig', 'locationName' => 'timecodeConfig', ], 'TimedMetadataInsertion' => [ 'shape' => 'TimedMetadataInsertion', 'locationName' => 'timedMetadataInsertion', ], ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'PROGRESSING', 'COMPLETE', 'CANCELED', 'ERROR', ], ], 'JobTemplate' => [ 'type' => 'structure', 'members' => [ 'AccelerationSettings' => [ 'shape' => 'AccelerationSettings', 'locationName' => 'accelerationSettings', ], 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Category' => [ 'shape' => '__string', 'locationName' => 'category', ], 'CreatedAt' => [ 'shape' => '__timestampUnix', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'HopDestinations' => [ 'shape' => '__listOfHopDestination', 'locationName' => 'hopDestinations', ], 'LastUpdated' => [ 'shape' => '__timestampUnix', 'locationName' => 'lastUpdated', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Priority' => [ 'shape' => '__integerMinNegative50Max50', 'locationName' => 'priority', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', ], 'Settings' => [ 'shape' => 'JobTemplateSettings', 'locationName' => 'settings', ], 'StatusUpdateInterval' => [ 'shape' => 'StatusUpdateInterval', 'locationName' => 'statusUpdateInterval', ], 'Type' => [ 'shape' => 'Type', 'locationName' => 'type', ], ], 'required' => [ 'Settings', 'Name', ], ], 'JobTemplateListBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATION_DATE', 'SYSTEM', ], ], 'JobTemplateSettings' => [ 'type' => 'structure', 'members' => [ 'AdAvailOffset' => [ 'shape' => '__integerMinNegative1000Max1000', 'locationName' => 'adAvailOffset', ], 'AvailBlanking' => [ 'shape' => 'AvailBlanking', 'locationName' => 'availBlanking', ], 'ColorConversion3DLUTSettings' => [ 'shape' => '__listOfColorConversion3DLUTSetting', 'locationName' => 'colorConversion3DLUTSettings', ], 'Esam' => [ 'shape' => 'EsamSettings', 'locationName' => 'esam', ], 'ExtendedDataServices' => [ 'shape' => 'ExtendedDataServices', 'locationName' => 'extendedDataServices', ], 'FollowSource' => [ 'shape' => '__integerMin1Max150', 'locationName' => 'followSource', ], 'Inputs' => [ 'shape' => '__listOfInputTemplate', 'locationName' => 'inputs', ], 'KantarWatermark' => [ 'shape' => 'KantarWatermarkSettings', 'locationName' => 'kantarWatermark', ], 'MotionImageInserter' => [ 'shape' => 'MotionImageInserter', 'locationName' => 'motionImageInserter', ], 'NielsenConfiguration' => [ 'shape' => 'NielsenConfiguration', 'locationName' => 'nielsenConfiguration', ], 'NielsenNonLinearWatermark' => [ 'shape' => 'NielsenNonLinearWatermarkSettings', 'locationName' => 'nielsenNonLinearWatermark', ], 'OutputGroups' => [ 'shape' => '__listOfOutputGroup', 'locationName' => 'outputGroups', ], 'TimecodeConfig' => [ 'shape' => 'TimecodeConfig', 'locationName' => 'timecodeConfig', ], 'TimedMetadataInsertion' => [ 'shape' => 'TimedMetadataInsertion', 'locationName' => 'timedMetadataInsertion', ], ], ], 'KantarWatermarkSettings' => [ 'type' => 'structure', 'members' => [ 'ChannelName' => [ 'shape' => '__stringMin1Max20', 'locationName' => 'channelName', ], 'ContentReference' => [ 'shape' => '__stringMin1Max50PatternAZAZ09', 'locationName' => 'contentReference', ], 'CredentialsSecretName' => [ 'shape' => '__stringMin1Max2048PatternArnAZSecretsmanagerWD12SecretAZAZ09', 'locationName' => 'credentialsSecretName', ], 'FileOffset' => [ 'shape' => '__doubleMin0', 'locationName' => 'fileOffset', ], 'KantarLicenseId' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'kantarLicenseId', ], 'KantarServerUrl' => [ 'shape' => '__stringPatternHttpsKantarmedia', 'locationName' => 'kantarServerUrl', ], 'LogDestination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'logDestination', ], 'Metadata3' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'metadata3', ], 'Metadata4' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'metadata4', ], 'Metadata5' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'metadata5', ], 'Metadata6' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'metadata6', ], 'Metadata7' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'metadata7', ], 'Metadata8' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'metadata8', ], ], ], 'LanguageCode' => [ 'type' => 'string', 'enum' => [ 'ENG', 'SPA', 'FRA', 'DEU', 'GER', 'ZHO', 'ARA', 'HIN', 'JPN', 'RUS', 'POR', 'ITA', 'URD', 'VIE', 'KOR', 'PAN', 'ABK', 'AAR', 'AFR', 'AKA', 'SQI', 'AMH', 'ARG', 'HYE', 'ASM', 'AVA', 'AVE', 'AYM', 'AZE', 'BAM', 'BAK', 'EUS', 'BEL', 'BEN', 'BIH', 'BIS', 'BOS', 'BRE', 'BUL', 'MYA', 'CAT', 'KHM', 'CHA', 'CHE', 'NYA', 'CHU', 'CHV', 'COR', 'COS', 'CRE', 'HRV', 'CES', 'DAN', 'DIV', 'NLD', 'DZO', 'ENM', 'EPO', 'EST', 'EWE', 'FAO', 'FIJ', 'FIN', 'FRM', 'FUL', 'GLA', 'GLG', 'LUG', 'KAT', 'ELL', 'GRN', 'GUJ', 'HAT', 'HAU', 'HEB', 'HER', 'HMO', 'HUN', 'ISL', 'IDO', 'IBO', 'IND', 'INA', 'ILE', 'IKU', 'IPK', 'GLE', 'JAV', 'KAL', 'KAN', 'KAU', 'KAS', 'KAZ', 'KIK', 'KIN', 'KIR', 'KOM', 'KON', 'KUA', 'KUR', 'LAO', 'LAT', 'LAV', 'LIM', 'LIN', 'LIT', 'LUB', 'LTZ', 'MKD', 'MLG', 'MSA', 'MAL', 'MLT', 'GLV', 'MRI', 'MAR', 'MAH', 'MON', 'NAU', 'NAV', 'NDE', 'NBL', 'NDO', 'NEP', 'SME', 'NOR', 'NOB', 'NNO', 'OCI', 'OJI', 'ORI', 'ORM', 'OSS', 'PLI', 'FAS', 'POL', 'PUS', 'QUE', 'QAA', 'RON', 'ROH', 'RUN', 'SMO', 'SAG', 'SAN', 'SRD', 'SRB', 'SNA', 'III', 'SND', 'SIN', 'SLK', 'SLV', 'SOM', 'SOT', 'SUN', 'SWA', 'SSW', 'SWE', 'TGL', 'TAH', 'TGK', 'TAM', 'TAT', 'TEL', 'THA', 'BOD', 'TIR', 'TON', 'TSO', 'TSN', 'TUR', 'TUK', 'TWI', 'UIG', 'UKR', 'UZB', 'VEN', 'VOL', 'WLN', 'CYM', 'FRY', 'WOL', 'XHO', 'YID', 'YOR', 'ZHA', 'ZUL', 'ORJ', 'QPC', 'TNG', 'SRP', ], ], 'ListJobTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => '__string', 'locationName' => 'category', 'location' => 'querystring', ], 'ListBy' => [ 'shape' => 'JobTemplateListBy', 'locationName' => 'listBy', 'location' => 'querystring', ], 'MaxResults' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'maxResults', 'location' => 'querystring', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'location' => 'querystring', ], 'Order' => [ 'shape' => 'Order', 'locationName' => 'order', 'location' => 'querystring', ], ], ], 'ListJobTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'JobTemplates' => [ 'shape' => '__listOfJobTemplate', 'locationName' => 'jobTemplates', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListJobsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'maxResults', 'location' => 'querystring', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'location' => 'querystring', ], 'Order' => [ 'shape' => 'Order', 'locationName' => 'order', 'location' => 'querystring', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', 'location' => 'querystring', ], 'Status' => [ 'shape' => 'JobStatus', 'locationName' => 'status', 'location' => 'querystring', ], ], ], 'ListJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => '__listOfJob', 'locationName' => 'jobs', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListPresetsRequest' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => '__string', 'locationName' => 'category', 'location' => 'querystring', ], 'ListBy' => [ 'shape' => 'PresetListBy', 'locationName' => 'listBy', 'location' => 'querystring', ], 'MaxResults' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'maxResults', 'location' => 'querystring', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'location' => 'querystring', ], 'Order' => [ 'shape' => 'Order', 'locationName' => 'order', 'location' => 'querystring', ], ], ], 'ListPresetsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Presets' => [ 'shape' => '__listOfPreset', 'locationName' => 'presets', ], ], ], 'ListQueuesRequest' => [ 'type' => 'structure', 'members' => [ 'ListBy' => [ 'shape' => 'QueueListBy', 'locationName' => 'listBy', 'location' => 'querystring', ], 'MaxResults' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'maxResults', 'location' => 'querystring', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'location' => 'querystring', ], 'Order' => [ 'shape' => 'Order', 'locationName' => 'order', 'location' => 'querystring', ], ], ], 'ListQueuesResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Queues' => [ 'shape' => '__listOfQueue', 'locationName' => 'queues', ], 'TotalConcurrentJobs' => [ 'shape' => '__integer', 'locationName' => 'totalConcurrentJobs', ], 'UnallocatedConcurrentJobs' => [ 'shape' => '__integer', 'locationName' => 'unallocatedConcurrentJobs', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', 'location' => 'uri', ], ], 'required' => [ 'Arn', ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceTags' => [ 'shape' => 'ResourceTags', 'locationName' => 'resourceTags', ], ], ], 'ListVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'maxResults', 'location' => 'querystring', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'location' => 'querystring', ], ], ], 'ListVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Versions' => [ 'shape' => '__listOfJobEngineVersion', 'locationName' => 'versions', ], ], ], 'M2tsAudioBufferModel' => [ 'type' => 'string', 'enum' => [ 'DVB', 'ATSC', ], ], 'M2tsAudioDuration' => [ 'type' => 'string', 'enum' => [ 'DEFAULT_CODEC_DURATION', 'MATCH_VIDEO_DURATION', ], ], 'M2tsBufferModel' => [ 'type' => 'string', 'enum' => [ 'MULTIPLEX', 'NONE', ], ], 'M2tsDataPtsControl' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'ALIGN_TO_VIDEO', ], ], 'M2tsEbpAudioInterval' => [ 'type' => 'string', 'enum' => [ 'VIDEO_AND_FIXED_INTERVALS', 'VIDEO_INTERVAL', ], ], 'M2tsEbpPlacement' => [ 'type' => 'string', 'enum' => [ 'VIDEO_AND_AUDIO_PIDS', 'VIDEO_PID', ], ], 'M2tsEsRateInPes' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'M2tsForceTsVideoEbpOrder' => [ 'type' => 'string', 'enum' => [ 'FORCE', 'DEFAULT', ], ], 'M2tsKlvMetadata' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'M2tsNielsenId3' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'NONE', ], ], 'M2tsPcrControl' => [ 'type' => 'string', 'enum' => [ 'PCR_EVERY_PES_PACKET', 'CONFIGURED_PCR_PERIOD', ], ], 'M2tsPreventBufferUnderflow' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'M2tsRateMode' => [ 'type' => 'string', 'enum' => [ 'VBR', 'CBR', ], ], 'M2tsScte35Esam' => [ 'type' => 'structure', 'members' => [ 'Scte35EsamPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'scte35EsamPid', ], ], ], 'M2tsScte35Source' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'M2tsSegmentationMarkers' => [ 'type' => 'string', 'enum' => [ 'NONE', 'RAI_SEGSTART', 'RAI_ADAPT', 'PSI_SEGSTART', 'EBP', 'EBP_LEGACY', ], ], 'M2tsSegmentationStyle' => [ 'type' => 'string', 'enum' => [ 'MAINTAIN_CADENCE', 'RESET_CADENCE', ], ], 'M2tsSettings' => [ 'type' => 'structure', 'members' => [ 'AudioBufferModel' => [ 'shape' => 'M2tsAudioBufferModel', 'locationName' => 'audioBufferModel', ], 'AudioDuration' => [ 'shape' => 'M2tsAudioDuration', 'locationName' => 'audioDuration', ], 'AudioFramesPerPes' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'audioFramesPerPes', ], 'AudioPids' => [ 'shape' => '__listOf__integerMin32Max8182', 'locationName' => 'audioPids', ], 'Bitrate' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'bitrate', ], 'BufferModel' => [ 'shape' => 'M2tsBufferModel', 'locationName' => 'bufferModel', ], 'DataPTSControl' => [ 'shape' => 'M2tsDataPtsControl', 'locationName' => 'dataPTSControl', ], 'DvbNitSettings' => [ 'shape' => 'DvbNitSettings', 'locationName' => 'dvbNitSettings', ], 'DvbSdtSettings' => [ 'shape' => 'DvbSdtSettings', 'locationName' => 'dvbSdtSettings', ], 'DvbSubPids' => [ 'shape' => '__listOf__integerMin32Max8182', 'locationName' => 'dvbSubPids', ], 'DvbTdtSettings' => [ 'shape' => 'DvbTdtSettings', 'locationName' => 'dvbTdtSettings', ], 'DvbTeletextPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'dvbTeletextPid', ], 'EbpAudioInterval' => [ 'shape' => 'M2tsEbpAudioInterval', 'locationName' => 'ebpAudioInterval', ], 'EbpPlacement' => [ 'shape' => 'M2tsEbpPlacement', 'locationName' => 'ebpPlacement', ], 'EsRateInPes' => [ 'shape' => 'M2tsEsRateInPes', 'locationName' => 'esRateInPes', ], 'ForceTsVideoEbpOrder' => [ 'shape' => 'M2tsForceTsVideoEbpOrder', 'locationName' => 'forceTsVideoEbpOrder', ], 'FragmentTime' => [ 'shape' => '__doubleMin0', 'locationName' => 'fragmentTime', ], 'KlvMetadata' => [ 'shape' => 'M2tsKlvMetadata', 'locationName' => 'klvMetadata', ], 'MaxPcrInterval' => [ 'shape' => '__integerMin0Max500', 'locationName' => 'maxPcrInterval', ], 'MinEbpInterval' => [ 'shape' => '__integerMin0Max10000', 'locationName' => 'minEbpInterval', ], 'NielsenId3' => [ 'shape' => 'M2tsNielsenId3', 'locationName' => 'nielsenId3', ], 'NullPacketBitrate' => [ 'shape' => '__doubleMin0', 'locationName' => 'nullPacketBitrate', ], 'PatInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'patInterval', ], 'PcrControl' => [ 'shape' => 'M2tsPcrControl', 'locationName' => 'pcrControl', ], 'PcrPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'pcrPid', ], 'PmtInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'pmtInterval', ], 'PmtPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'pmtPid', ], 'PreventBufferUnderflow' => [ 'shape' => 'M2tsPreventBufferUnderflow', 'locationName' => 'preventBufferUnderflow', ], 'PrivateMetadataPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'privateMetadataPid', ], 'ProgramNumber' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'programNumber', ], 'PtsOffset' => [ 'shape' => '__integerMin0Max3600', 'locationName' => 'ptsOffset', ], 'PtsOffsetMode' => [ 'shape' => 'TsPtsOffset', 'locationName' => 'ptsOffsetMode', ], 'RateMode' => [ 'shape' => 'M2tsRateMode', 'locationName' => 'rateMode', ], 'Scte35Esam' => [ 'shape' => 'M2tsScte35Esam', 'locationName' => 'scte35Esam', ], 'Scte35Pid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'scte35Pid', ], 'Scte35Source' => [ 'shape' => 'M2tsScte35Source', 'locationName' => 'scte35Source', ], 'SegmentationMarkers' => [ 'shape' => 'M2tsSegmentationMarkers', 'locationName' => 'segmentationMarkers', ], 'SegmentationStyle' => [ 'shape' => 'M2tsSegmentationStyle', 'locationName' => 'segmentationStyle', ], 'SegmentationTime' => [ 'shape' => '__doubleMin0', 'locationName' => 'segmentationTime', ], 'TimedMetadataPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'timedMetadataPid', ], 'TransportStreamId' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'transportStreamId', ], 'VideoPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'videoPid', ], ], ], 'M3u8AudioDuration' => [ 'type' => 'string', 'enum' => [ 'DEFAULT_CODEC_DURATION', 'MATCH_VIDEO_DURATION', ], ], 'M3u8DataPtsControl' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'ALIGN_TO_VIDEO', ], ], 'M3u8NielsenId3' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'NONE', ], ], 'M3u8PcrControl' => [ 'type' => 'string', 'enum' => [ 'PCR_EVERY_PES_PACKET', 'CONFIGURED_PCR_PERIOD', ], ], 'M3u8Scte35Source' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'M3u8Settings' => [ 'type' => 'structure', 'members' => [ 'AudioDuration' => [ 'shape' => 'M3u8AudioDuration', 'locationName' => 'audioDuration', ], 'AudioFramesPerPes' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'audioFramesPerPes', ], 'AudioPids' => [ 'shape' => '__listOf__integerMin32Max8182', 'locationName' => 'audioPids', ], 'DataPTSControl' => [ 'shape' => 'M3u8DataPtsControl', 'locationName' => 'dataPTSControl', ], 'MaxPcrInterval' => [ 'shape' => '__integerMin0Max500', 'locationName' => 'maxPcrInterval', ], 'NielsenId3' => [ 'shape' => 'M3u8NielsenId3', 'locationName' => 'nielsenId3', ], 'PatInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'patInterval', ], 'PcrControl' => [ 'shape' => 'M3u8PcrControl', 'locationName' => 'pcrControl', ], 'PcrPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'pcrPid', ], 'PmtInterval' => [ 'shape' => '__integerMin0Max1000', 'locationName' => 'pmtInterval', ], 'PmtPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'pmtPid', ], 'PrivateMetadataPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'privateMetadataPid', ], 'ProgramNumber' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'programNumber', ], 'PtsOffset' => [ 'shape' => '__integerMin0Max3600', 'locationName' => 'ptsOffset', ], 'PtsOffsetMode' => [ 'shape' => 'TsPtsOffset', 'locationName' => 'ptsOffsetMode', ], 'Scte35Pid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'scte35Pid', ], 'Scte35Source' => [ 'shape' => 'M3u8Scte35Source', 'locationName' => 'scte35Source', ], 'TimedMetadata' => [ 'shape' => 'TimedMetadata', 'locationName' => 'timedMetadata', ], 'TimedMetadataPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'timedMetadataPid', ], 'TransportStreamId' => [ 'shape' => '__integerMin0Max65535', 'locationName' => 'transportStreamId', ], 'VideoPid' => [ 'shape' => '__integerMin32Max8182', 'locationName' => 'videoPid', ], ], ], 'MatrixCoefficients' => [ 'type' => 'string', 'enum' => [ 'RGB', 'ITU_709', 'UNSPECIFIED', 'RESERVED', 'FCC', 'ITU_470BG', 'SMPTE_170M', 'SMPTE_240M', 'YCgCo', 'ITU_2020_NCL', 'ITU_2020_CL', 'SMPTE_2085', 'CD_NCL', 'CD_CL', 'ITU_2100ICtCp', 'IPT', 'EBU3213', 'LAST', ], ], 'Metadata' => [ 'type' => 'structure', 'members' => [ 'ETag' => [ 'shape' => '__string', 'locationName' => 'eTag', ], 'FileSize' => [ 'shape' => '__long', 'locationName' => 'fileSize', ], 'LastModified' => [ 'shape' => '__timestampUnix', 'locationName' => 'lastModified', ], 'MimeType' => [ 'shape' => '__string', 'locationName' => 'mimeType', ], ], ], 'MinBottomRenditionSize' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'height', ], 'Width' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'width', ], ], ], 'MinTopRenditionSize' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'height', ], 'Width' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'width', ], ], ], 'MotionImageInserter' => [ 'type' => 'structure', 'members' => [ 'Framerate' => [ 'shape' => 'MotionImageInsertionFramerate', 'locationName' => 'framerate', ], 'Input' => [ 'shape' => '__stringMin14PatternS3Mov09PngHttpsMov09Png', 'locationName' => 'input', ], 'InsertionMode' => [ 'shape' => 'MotionImageInsertionMode', 'locationName' => 'insertionMode', ], 'Offset' => [ 'shape' => 'MotionImageInsertionOffset', 'locationName' => 'offset', ], 'Playback' => [ 'shape' => 'MotionImagePlayback', 'locationName' => 'playback', ], 'StartTime' => [ 'shape' => '__stringMin11Max11Pattern01D20305D205D', 'locationName' => 'startTime', ], ], ], 'MotionImageInsertionFramerate' => [ 'type' => 'structure', 'members' => [ 'FramerateDenominator' => [ 'shape' => '__integerMin1Max17895697', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483640', 'locationName' => 'framerateNumerator', ], ], ], 'MotionImageInsertionMode' => [ 'type' => 'string', 'enum' => [ 'MOV', 'PNG', ], ], 'MotionImageInsertionOffset' => [ 'type' => 'structure', 'members' => [ 'ImageX' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'imageX', ], 'ImageY' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'imageY', ], ], ], 'MotionImagePlayback' => [ 'type' => 'string', 'enum' => [ 'ONCE', 'REPEAT', ], ], 'MovClapAtom' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'MovCslgAtom' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'MovMpeg2FourCCControl' => [ 'type' => 'string', 'enum' => [ 'XDCAM', 'MPEG', ], ], 'MovPaddingControl' => [ 'type' => 'string', 'enum' => [ 'OMNEON', 'NONE', ], ], 'MovReference' => [ 'type' => 'string', 'enum' => [ 'SELF_CONTAINED', 'EXTERNAL', ], ], 'MovSettings' => [ 'type' => 'structure', 'members' => [ 'ClapAtom' => [ 'shape' => 'MovClapAtom', 'locationName' => 'clapAtom', ], 'CslgAtom' => [ 'shape' => 'MovCslgAtom', 'locationName' => 'cslgAtom', ], 'Mpeg2FourCCControl' => [ 'shape' => 'MovMpeg2FourCCControl', 'locationName' => 'mpeg2FourCCControl', ], 'PaddingControl' => [ 'shape' => 'MovPaddingControl', 'locationName' => 'paddingControl', ], 'Reference' => [ 'shape' => 'MovReference', 'locationName' => 'reference', ], ], ], 'Mp2Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin32000Max384000', 'locationName' => 'bitrate', ], 'Channels' => [ 'shape' => '__integerMin1Max2', 'locationName' => 'channels', ], 'SampleRate' => [ 'shape' => '__integerMin32000Max48000', 'locationName' => 'sampleRate', ], ], ], 'Mp3RateControlMode' => [ 'type' => 'string', 'enum' => [ 'CBR', 'VBR', ], ], 'Mp3Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin16000Max320000', 'locationName' => 'bitrate', ], 'Channels' => [ 'shape' => '__integerMin1Max2', 'locationName' => 'channels', ], 'RateControlMode' => [ 'shape' => 'Mp3RateControlMode', 'locationName' => 'rateControlMode', ], 'SampleRate' => [ 'shape' => '__integerMin22050Max48000', 'locationName' => 'sampleRate', ], 'VbrQuality' => [ 'shape' => '__integerMin0Max9', 'locationName' => 'vbrQuality', ], ], ], 'Mp4CslgAtom' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'Mp4FreeSpaceBox' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'Mp4MoovPlacement' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE_DOWNLOAD', 'NORMAL', ], ], 'Mp4Settings' => [ 'type' => 'structure', 'members' => [ 'AudioDuration' => [ 'shape' => 'CmfcAudioDuration', 'locationName' => 'audioDuration', ], 'CslgAtom' => [ 'shape' => 'Mp4CslgAtom', 'locationName' => 'cslgAtom', ], 'CttsVersion' => [ 'shape' => '__integerMin0Max1', 'locationName' => 'cttsVersion', ], 'FreeSpaceBox' => [ 'shape' => 'Mp4FreeSpaceBox', 'locationName' => 'freeSpaceBox', ], 'MoovPlacement' => [ 'shape' => 'Mp4MoovPlacement', 'locationName' => 'moovPlacement', ], 'Mp4MajorBrand' => [ 'shape' => '__string', 'locationName' => 'mp4MajorBrand', ], ], ], 'MpdAccessibilityCaptionHints' => [ 'type' => 'string', 'enum' => [ 'INCLUDE', 'EXCLUDE', ], ], 'MpdAudioDuration' => [ 'type' => 'string', 'enum' => [ 'DEFAULT_CODEC_DURATION', 'MATCH_VIDEO_DURATION', ], ], 'MpdCaptionContainerType' => [ 'type' => 'string', 'enum' => [ 'RAW', 'FRAGMENTED_MP4', ], ], 'MpdKlvMetadata' => [ 'type' => 'string', 'enum' => [ 'NONE', 'PASSTHROUGH', ], ], 'MpdManifestMetadataSignaling' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'MpdScte35Esam' => [ 'type' => 'string', 'enum' => [ 'INSERT', 'NONE', ], ], 'MpdScte35Source' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'MpdSettings' => [ 'type' => 'structure', 'members' => [ 'AccessibilityCaptionHints' => [ 'shape' => 'MpdAccessibilityCaptionHints', 'locationName' => 'accessibilityCaptionHints', ], 'AudioDuration' => [ 'shape' => 'MpdAudioDuration', 'locationName' => 'audioDuration', ], 'CaptionContainerType' => [ 'shape' => 'MpdCaptionContainerType', 'locationName' => 'captionContainerType', ], 'KlvMetadata' => [ 'shape' => 'MpdKlvMetadata', 'locationName' => 'klvMetadata', ], 'ManifestMetadataSignaling' => [ 'shape' => 'MpdManifestMetadataSignaling', 'locationName' => 'manifestMetadataSignaling', ], 'Scte35Esam' => [ 'shape' => 'MpdScte35Esam', 'locationName' => 'scte35Esam', ], 'Scte35Source' => [ 'shape' => 'MpdScte35Source', 'locationName' => 'scte35Source', ], 'TimedMetadata' => [ 'shape' => 'MpdTimedMetadata', 'locationName' => 'timedMetadata', ], 'TimedMetadataBoxVersion' => [ 'shape' => 'MpdTimedMetadataBoxVersion', 'locationName' => 'timedMetadataBoxVersion', ], 'TimedMetadataSchemeIdUri' => [ 'shape' => '__stringMax1000', 'locationName' => 'timedMetadataSchemeIdUri', ], 'TimedMetadataValue' => [ 'shape' => '__stringMax1000', 'locationName' => 'timedMetadataValue', ], ], ], 'MpdTimedMetadata' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'MpdTimedMetadataBoxVersion' => [ 'type' => 'string', 'enum' => [ 'VERSION_0', 'VERSION_1', ], ], 'Mpeg2AdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'OFF', 'LOW', 'MEDIUM', 'HIGH', ], ], 'Mpeg2CodecLevel' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'LOW', 'MAIN', 'HIGH1440', 'HIGH', ], ], 'Mpeg2CodecProfile' => [ 'type' => 'string', 'enum' => [ 'MAIN', 'PROFILE_422', ], ], 'Mpeg2DynamicSubGop' => [ 'type' => 'string', 'enum' => [ 'ADAPTIVE', 'STATIC', ], ], 'Mpeg2FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Mpeg2FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'Mpeg2GopSizeUnits' => [ 'type' => 'string', 'enum' => [ 'FRAMES', 'SECONDS', ], ], 'Mpeg2InterlaceMode' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE', 'TOP_FIELD', 'BOTTOM_FIELD', 'FOLLOW_TOP_FIELD', 'FOLLOW_BOTTOM_FIELD', ], ], 'Mpeg2IntraDcPrecision' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'INTRA_DC_PRECISION_8', 'INTRA_DC_PRECISION_9', 'INTRA_DC_PRECISION_10', 'INTRA_DC_PRECISION_11', ], ], 'Mpeg2ParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Mpeg2QualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PASS', 'MULTI_PASS', ], ], 'Mpeg2RateControlMode' => [ 'type' => 'string', 'enum' => [ 'VBR', 'CBR', ], ], 'Mpeg2ScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'Mpeg2SceneChangeDetect' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Mpeg2Settings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'Mpeg2AdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'Bitrate' => [ 'shape' => '__integerMin1000Max288000000', 'locationName' => 'bitrate', ], 'CodecLevel' => [ 'shape' => 'Mpeg2CodecLevel', 'locationName' => 'codecLevel', ], 'CodecProfile' => [ 'shape' => 'Mpeg2CodecProfile', 'locationName' => 'codecProfile', ], 'DynamicSubGop' => [ 'shape' => 'Mpeg2DynamicSubGop', 'locationName' => 'dynamicSubGop', ], 'FramerateControl' => [ 'shape' => 'Mpeg2FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'Mpeg2FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max1001', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin24Max60000', 'locationName' => 'framerateNumerator', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'gopClosedCadence', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'GopSizeUnits' => [ 'shape' => 'Mpeg2GopSizeUnits', 'locationName' => 'gopSizeUnits', ], 'HrdBufferFinalFillPercentage' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'hrdBufferFinalFillPercentage', ], 'HrdBufferInitialFillPercentage' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'hrdBufferInitialFillPercentage', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max47185920', 'locationName' => 'hrdBufferSize', ], 'InterlaceMode' => [ 'shape' => 'Mpeg2InterlaceMode', 'locationName' => 'interlaceMode', ], 'IntraDcPrecision' => [ 'shape' => 'Mpeg2IntraDcPrecision', 'locationName' => 'intraDcPrecision', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000Max300000000', 'locationName' => 'maxBitrate', ], 'MinIInterval' => [ 'shape' => '__integerMin0Max30', 'locationName' => 'minIInterval', ], 'NumberBFramesBetweenReferenceFrames' => [ 'shape' => '__integerMin0Max7', 'locationName' => 'numberBFramesBetweenReferenceFrames', ], 'ParControl' => [ 'shape' => 'Mpeg2ParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parNumerator', ], 'QualityTuningLevel' => [ 'shape' => 'Mpeg2QualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'RateControlMode' => [ 'shape' => 'Mpeg2RateControlMode', 'locationName' => 'rateControlMode', ], 'ScanTypeConversionMode' => [ 'shape' => 'Mpeg2ScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SceneChangeDetect' => [ 'shape' => 'Mpeg2SceneChangeDetect', 'locationName' => 'sceneChangeDetect', ], 'SlowPal' => [ 'shape' => 'Mpeg2SlowPal', 'locationName' => 'slowPal', ], 'Softness' => [ 'shape' => '__integerMin0Max128', 'locationName' => 'softness', ], 'SpatialAdaptiveQuantization' => [ 'shape' => 'Mpeg2SpatialAdaptiveQuantization', 'locationName' => 'spatialAdaptiveQuantization', ], 'Syntax' => [ 'shape' => 'Mpeg2Syntax', 'locationName' => 'syntax', ], 'Telecine' => [ 'shape' => 'Mpeg2Telecine', 'locationName' => 'telecine', ], 'TemporalAdaptiveQuantization' => [ 'shape' => 'Mpeg2TemporalAdaptiveQuantization', 'locationName' => 'temporalAdaptiveQuantization', ], ], ], 'Mpeg2SlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Mpeg2SpatialAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Mpeg2Syntax' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'D_10', ], ], 'Mpeg2Telecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'SOFT', 'HARD', ], ], 'Mpeg2TemporalAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'MsSmoothAdditionalManifest' => [ 'type' => 'structure', 'members' => [ 'ManifestNameModifier' => [ 'shape' => '__stringMin1', 'locationName' => 'manifestNameModifier', ], 'SelectedOutputs' => [ 'shape' => '__listOf__stringMin1', 'locationName' => 'selectedOutputs', ], ], ], 'MsSmoothAudioDeduplication' => [ 'type' => 'string', 'enum' => [ 'COMBINE_DUPLICATE_STREAMS', 'NONE', ], ], 'MsSmoothEncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'SpekeKeyProvider' => [ 'shape' => 'SpekeKeyProvider', 'locationName' => 'spekeKeyProvider', ], ], ], 'MsSmoothFragmentLengthControl' => [ 'type' => 'string', 'enum' => [ 'EXACT', 'GOP_MULTIPLE', ], ], 'MsSmoothGroupSettings' => [ 'type' => 'structure', 'members' => [ 'AdditionalManifests' => [ 'shape' => '__listOfMsSmoothAdditionalManifest', 'locationName' => 'additionalManifests', ], 'AudioDeduplication' => [ 'shape' => 'MsSmoothAudioDeduplication', 'locationName' => 'audioDeduplication', ], 'Destination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'destination', ], 'DestinationSettings' => [ 'shape' => 'DestinationSettings', 'locationName' => 'destinationSettings', ], 'Encryption' => [ 'shape' => 'MsSmoothEncryptionSettings', 'locationName' => 'encryption', ], 'FragmentLength' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'fragmentLength', ], 'FragmentLengthControl' => [ 'shape' => 'MsSmoothFragmentLengthControl', 'locationName' => 'fragmentLengthControl', ], 'ManifestEncoding' => [ 'shape' => 'MsSmoothManifestEncoding', 'locationName' => 'manifestEncoding', ], ], ], 'MsSmoothManifestEncoding' => [ 'type' => 'string', 'enum' => [ 'UTF8', 'UTF16', ], ], 'MxfAfdSignaling' => [ 'type' => 'string', 'enum' => [ 'NO_COPY', 'COPY_FROM_VIDEO', ], ], 'MxfProfile' => [ 'type' => 'string', 'enum' => [ 'D_10', 'XDCAM', 'OP1A', 'XAVC', 'XDCAM_RDD9', ], ], 'MxfSettings' => [ 'type' => 'structure', 'members' => [ 'AfdSignaling' => [ 'shape' => 'MxfAfdSignaling', 'locationName' => 'afdSignaling', ], 'Profile' => [ 'shape' => 'MxfProfile', 'locationName' => 'profile', ], 'XavcProfileSettings' => [ 'shape' => 'MxfXavcProfileSettings', 'locationName' => 'xavcProfileSettings', ], ], ], 'MxfXavcDurationMode' => [ 'type' => 'string', 'enum' => [ 'ALLOW_ANY_DURATION', 'DROP_FRAMES_FOR_COMPLIANCE', ], ], 'MxfXavcProfileSettings' => [ 'type' => 'structure', 'members' => [ 'DurationMode' => [ 'shape' => 'MxfXavcDurationMode', 'locationName' => 'durationMode', ], 'MaxAncDataSize' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'maxAncDataSize', ], ], ], 'NexGuardFileMarkerSettings' => [ 'type' => 'structure', 'members' => [ 'License' => [ 'shape' => '__stringMin1Max100000', 'locationName' => 'license', ], 'Payload' => [ 'shape' => '__integerMin0Max4194303', 'locationName' => 'payload', ], 'Preset' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'preset', ], 'Strength' => [ 'shape' => 'WatermarkingStrength', 'locationName' => 'strength', ], ], ], 'NielsenActiveWatermarkProcessType' => [ 'type' => 'string', 'enum' => [ 'NAES2_AND_NW', 'CBET', 'NAES2_AND_NW_AND_CBET', ], ], 'NielsenConfiguration' => [ 'type' => 'structure', 'members' => [ 'BreakoutCode' => [ 'shape' => '__integerMin0Max0', 'locationName' => 'breakoutCode', ], 'DistributorId' => [ 'shape' => '__string', 'locationName' => 'distributorId', ], ], ], 'NielsenNonLinearWatermarkSettings' => [ 'type' => 'structure', 'members' => [ 'ActiveWatermarkProcess' => [ 'shape' => 'NielsenActiveWatermarkProcessType', 'locationName' => 'activeWatermarkProcess', ], 'AdiFilename' => [ 'shape' => '__stringPatternS3', 'locationName' => 'adiFilename', ], 'AssetId' => [ 'shape' => '__stringMin1Max20', 'locationName' => 'assetId', ], 'AssetName' => [ 'shape' => '__stringMin1Max50', 'locationName' => 'assetName', ], 'CbetSourceId' => [ 'shape' => '__stringPattern0xAFaF0908190908', 'locationName' => 'cbetSourceId', ], 'EpisodeId' => [ 'shape' => '__stringMin1Max20', 'locationName' => 'episodeId', ], 'MetadataDestination' => [ 'shape' => '__stringPatternS3', 'locationName' => 'metadataDestination', ], 'SourceId' => [ 'shape' => '__integerMin0Max65534', 'locationName' => 'sourceId', ], 'SourceWatermarkStatus' => [ 'shape' => 'NielsenSourceWatermarkStatusType', 'locationName' => 'sourceWatermarkStatus', ], 'TicServerUrl' => [ 'shape' => '__stringPatternHttps', 'locationName' => 'ticServerUrl', ], 'UniqueTicPerAudioTrack' => [ 'shape' => 'NielsenUniqueTicPerAudioTrackType', 'locationName' => 'uniqueTicPerAudioTrack', ], ], ], 'NielsenSourceWatermarkStatusType' => [ 'type' => 'string', 'enum' => [ 'CLEAN', 'WATERMARKED', ], ], 'NielsenUniqueTicPerAudioTrackType' => [ 'type' => 'string', 'enum' => [ 'RESERVE_UNIQUE_TICS_PER_TRACK', 'SAME_TICS_PER_TRACK', ], ], 'NoiseFilterPostTemporalSharpening' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', 'AUTO', ], ], 'NoiseFilterPostTemporalSharpeningStrength' => [ 'type' => 'string', 'enum' => [ 'LOW', 'MEDIUM', 'HIGH', ], ], 'NoiseReducer' => [ 'type' => 'structure', 'members' => [ 'Filter' => [ 'shape' => 'NoiseReducerFilter', 'locationName' => 'filter', ], 'FilterSettings' => [ 'shape' => 'NoiseReducerFilterSettings', 'locationName' => 'filterSettings', ], 'SpatialFilterSettings' => [ 'shape' => 'NoiseReducerSpatialFilterSettings', 'locationName' => 'spatialFilterSettings', ], 'TemporalFilterSettings' => [ 'shape' => 'NoiseReducerTemporalFilterSettings', 'locationName' => 'temporalFilterSettings', ], ], ], 'NoiseReducerFilter' => [ 'type' => 'string', 'enum' => [ 'BILATERAL', 'MEAN', 'GAUSSIAN', 'LANCZOS', 'SHARPEN', 'CONSERVE', 'SPATIAL', 'TEMPORAL', ], ], 'NoiseReducerFilterSettings' => [ 'type' => 'structure', 'members' => [ 'Strength' => [ 'shape' => '__integerMin0Max3', 'locationName' => 'strength', ], ], ], 'NoiseReducerSpatialFilterSettings' => [ 'type' => 'structure', 'members' => [ 'PostFilterSharpenStrength' => [ 'shape' => '__integerMin0Max3', 'locationName' => 'postFilterSharpenStrength', ], 'Speed' => [ 'shape' => '__integerMinNegative2Max3', 'locationName' => 'speed', ], 'Strength' => [ 'shape' => '__integerMin0Max16', 'locationName' => 'strength', ], ], ], 'NoiseReducerTemporalFilterSettings' => [ 'type' => 'structure', 'members' => [ 'AggressiveMode' => [ 'shape' => '__integerMin0Max4', 'locationName' => 'aggressiveMode', ], 'PostTemporalSharpening' => [ 'shape' => 'NoiseFilterPostTemporalSharpening', 'locationName' => 'postTemporalSharpening', ], 'PostTemporalSharpeningStrength' => [ 'shape' => 'NoiseFilterPostTemporalSharpeningStrength', 'locationName' => 'postTemporalSharpeningStrength', ], 'Speed' => [ 'shape' => '__integerMinNegative1Max3', 'locationName' => 'speed', ], 'Strength' => [ 'shape' => '__integerMin0Max16', 'locationName' => 'strength', ], ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 404, ], ], 'OpusSettings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin32000Max192000', 'locationName' => 'bitrate', ], 'Channels' => [ 'shape' => '__integerMin1Max2', 'locationName' => 'channels', ], 'SampleRate' => [ 'shape' => '__integerMin16000Max48000', 'locationName' => 'sampleRate', ], ], ], 'Order' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'Output' => [ 'type' => 'structure', 'members' => [ 'AudioDescriptions' => [ 'shape' => '__listOfAudioDescription', 'locationName' => 'audioDescriptions', ], 'CaptionDescriptions' => [ 'shape' => '__listOfCaptionDescription', 'locationName' => 'captionDescriptions', ], 'ContainerSettings' => [ 'shape' => 'ContainerSettings', 'locationName' => 'containerSettings', ], 'Extension' => [ 'shape' => '__stringMax256', 'locationName' => 'extension', ], 'NameModifier' => [ 'shape' => '__stringMin1Max256', 'locationName' => 'nameModifier', ], 'OutputSettings' => [ 'shape' => 'OutputSettings', 'locationName' => 'outputSettings', ], 'Preset' => [ 'shape' => '__stringMin0', 'locationName' => 'preset', ], 'VideoDescription' => [ 'shape' => 'VideoDescription', 'locationName' => 'videoDescription', ], ], ], 'OutputChannelMapping' => [ 'type' => 'structure', 'members' => [ 'InputChannels' => [ 'shape' => '__listOf__integerMinNegative60Max6', 'locationName' => 'inputChannels', ], 'InputChannelsFineTune' => [ 'shape' => '__listOf__doubleMinNegative60Max6', 'locationName' => 'inputChannelsFineTune', ], ], ], 'OutputDetail' => [ 'type' => 'structure', 'members' => [ 'DurationInMs' => [ 'shape' => '__integer', 'locationName' => 'durationInMs', ], 'VideoDetails' => [ 'shape' => 'VideoDetail', 'locationName' => 'videoDetails', ], ], ], 'OutputGroup' => [ 'type' => 'structure', 'members' => [ 'AutomatedEncodingSettings' => [ 'shape' => 'AutomatedEncodingSettings', 'locationName' => 'automatedEncodingSettings', ], 'CustomName' => [ 'shape' => '__string', 'locationName' => 'customName', ], 'Name' => [ 'shape' => '__stringMax2048', 'locationName' => 'name', ], 'OutputGroupSettings' => [ 'shape' => 'OutputGroupSettings', 'locationName' => 'outputGroupSettings', ], 'Outputs' => [ 'shape' => '__listOfOutput', 'locationName' => 'outputs', ], ], ], 'OutputGroupDetail' => [ 'type' => 'structure', 'members' => [ 'OutputDetails' => [ 'shape' => '__listOfOutputDetail', 'locationName' => 'outputDetails', ], ], ], 'OutputGroupSettings' => [ 'type' => 'structure', 'members' => [ 'CmafGroupSettings' => [ 'shape' => 'CmafGroupSettings', 'locationName' => 'cmafGroupSettings', ], 'DashIsoGroupSettings' => [ 'shape' => 'DashIsoGroupSettings', 'locationName' => 'dashIsoGroupSettings', ], 'FileGroupSettings' => [ 'shape' => 'FileGroupSettings', 'locationName' => 'fileGroupSettings', ], 'HlsGroupSettings' => [ 'shape' => 'HlsGroupSettings', 'locationName' => 'hlsGroupSettings', ], 'MsSmoothGroupSettings' => [ 'shape' => 'MsSmoothGroupSettings', 'locationName' => 'msSmoothGroupSettings', ], 'Type' => [ 'shape' => 'OutputGroupType', 'locationName' => 'type', ], ], ], 'OutputGroupType' => [ 'type' => 'string', 'enum' => [ 'HLS_GROUP_SETTINGS', 'DASH_ISO_GROUP_SETTINGS', 'FILE_GROUP_SETTINGS', 'MS_SMOOTH_GROUP_SETTINGS', 'CMAF_GROUP_SETTINGS', ], ], 'OutputSdt' => [ 'type' => 'string', 'enum' => [ 'SDT_FOLLOW', 'SDT_FOLLOW_IF_PRESENT', 'SDT_MANUAL', 'SDT_NONE', ], ], 'OutputSettings' => [ 'type' => 'structure', 'members' => [ 'HlsSettings' => [ 'shape' => 'HlsSettings', 'locationName' => 'hlsSettings', ], ], ], 'PadVideo' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'BLACK', ], ], 'PartnerWatermarking' => [ 'type' => 'structure', 'members' => [ 'NexguardFileMarkerSettings' => [ 'shape' => 'NexGuardFileMarkerSettings', 'locationName' => 'nexguardFileMarkerSettings', ], ], ], 'Policy' => [ 'type' => 'structure', 'members' => [ 'HttpInputs' => [ 'shape' => 'InputPolicy', 'locationName' => 'httpInputs', ], 'HttpsInputs' => [ 'shape' => 'InputPolicy', 'locationName' => 'httpsInputs', ], 'S3Inputs' => [ 'shape' => 'InputPolicy', 'locationName' => 's3Inputs', ], ], ], 'Preset' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Category' => [ 'shape' => '__string', 'locationName' => 'category', ], 'CreatedAt' => [ 'shape' => '__timestampUnix', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'LastUpdated' => [ 'shape' => '__timestampUnix', 'locationName' => 'lastUpdated', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'Settings' => [ 'shape' => 'PresetSettings', 'locationName' => 'settings', ], 'Type' => [ 'shape' => 'Type', 'locationName' => 'type', ], ], 'required' => [ 'Settings', 'Name', ], ], 'PresetListBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATION_DATE', 'SYSTEM', ], ], 'PresetSettings' => [ 'type' => 'structure', 'members' => [ 'AudioDescriptions' => [ 'shape' => '__listOfAudioDescription', 'locationName' => 'audioDescriptions', ], 'CaptionDescriptions' => [ 'shape' => '__listOfCaptionDescriptionPreset', 'locationName' => 'captionDescriptions', ], 'ContainerSettings' => [ 'shape' => 'ContainerSettings', 'locationName' => 'containerSettings', ], 'VideoDescription' => [ 'shape' => 'VideoDescription', 'locationName' => 'videoDescription', ], ], ], 'PresetSpeke20Audio' => [ 'type' => 'string', 'enum' => [ 'PRESET_AUDIO_1', 'PRESET_AUDIO_2', 'PRESET_AUDIO_3', 'SHARED', 'UNENCRYPTED', ], ], 'PresetSpeke20Video' => [ 'type' => 'string', 'enum' => [ 'PRESET_VIDEO_1', 'PRESET_VIDEO_2', 'PRESET_VIDEO_3', 'PRESET_VIDEO_4', 'PRESET_VIDEO_5', 'PRESET_VIDEO_6', 'PRESET_VIDEO_7', 'PRESET_VIDEO_8', 'SHARED', 'UNENCRYPTED', ], ], 'PricingPlan' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'RESERVED', ], ], 'ProbeInputFile' => [ 'type' => 'structure', 'members' => [ 'FileUrl' => [ 'shape' => '__string', 'locationName' => 'fileUrl', ], ], ], 'ProbeRequest' => [ 'type' => 'structure', 'members' => [ 'InputFiles' => [ 'shape' => '__listOfProbeInputFile', 'locationName' => 'inputFiles', ], ], ], 'ProbeResponse' => [ 'type' => 'structure', 'members' => [ 'ProbeResults' => [ 'shape' => '__listOfProbeResult', 'locationName' => 'probeResults', ], ], ], 'ProbeResult' => [ 'type' => 'structure', 'members' => [ 'Container' => [ 'shape' => 'Container', 'locationName' => 'container', ], 'Metadata' => [ 'shape' => 'Metadata', 'locationName' => 'metadata', ], 'TrackMappings' => [ 'shape' => '__listOfTrackMapping', 'locationName' => 'trackMappings', ], ], ], 'ProresChromaSampling' => [ 'type' => 'string', 'enum' => [ 'PRESERVE_444_SAMPLING', 'SUBSAMPLE_TO_422', ], ], 'ProresCodecProfile' => [ 'type' => 'string', 'enum' => [ 'APPLE_PRORES_422', 'APPLE_PRORES_422_HQ', 'APPLE_PRORES_422_LT', 'APPLE_PRORES_422_PROXY', 'APPLE_PRORES_4444', 'APPLE_PRORES_4444_XQ', ], ], 'ProresFramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'ProresFramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'ProresInterlaceMode' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE', 'TOP_FIELD', 'BOTTOM_FIELD', 'FOLLOW_TOP_FIELD', 'FOLLOW_BOTTOM_FIELD', ], ], 'ProresParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'ProresScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'ProresSettings' => [ 'type' => 'structure', 'members' => [ 'ChromaSampling' => [ 'shape' => 'ProresChromaSampling', 'locationName' => 'chromaSampling', ], 'CodecProfile' => [ 'shape' => 'ProresCodecProfile', 'locationName' => 'codecProfile', ], 'FramerateControl' => [ 'shape' => 'ProresFramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'ProresFramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'InterlaceMode' => [ 'shape' => 'ProresInterlaceMode', 'locationName' => 'interlaceMode', ], 'ParControl' => [ 'shape' => 'ProresParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parNumerator', ], 'ScanTypeConversionMode' => [ 'shape' => 'ProresScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SlowPal' => [ 'shape' => 'ProresSlowPal', 'locationName' => 'slowPal', ], 'Telecine' => [ 'shape' => 'ProresTelecine', 'locationName' => 'telecine', ], ], ], 'ProresSlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'ProresTelecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'HARD', ], ], 'PutPolicyRequest' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', 'locationName' => 'policy', ], ], 'required' => [ 'Policy', ], ], 'PutPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'Policy' => [ 'shape' => 'Policy', 'locationName' => 'policy', ], ], ], 'Queue' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'ConcurrentJobs' => [ 'shape' => '__integer', 'locationName' => 'concurrentJobs', ], 'CreatedAt' => [ 'shape' => '__timestampUnix', 'locationName' => 'createdAt', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'LastUpdated' => [ 'shape' => '__timestampUnix', 'locationName' => 'lastUpdated', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'PricingPlan' => [ 'shape' => 'PricingPlan', 'locationName' => 'pricingPlan', ], 'ProgressingJobsCount' => [ 'shape' => '__integer', 'locationName' => 'progressingJobsCount', ], 'ReservationPlan' => [ 'shape' => 'ReservationPlan', 'locationName' => 'reservationPlan', ], 'ServiceOverrides' => [ 'shape' => '__listOfServiceOverride', 'locationName' => 'serviceOverrides', ], 'Status' => [ 'shape' => 'QueueStatus', 'locationName' => 'status', ], 'SubmittedJobsCount' => [ 'shape' => '__integer', 'locationName' => 'submittedJobsCount', ], 'Type' => [ 'shape' => 'Type', 'locationName' => 'type', ], ], 'required' => [ 'Name', ], ], 'QueueListBy' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATION_DATE', ], ], 'QueueStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'PAUSED', ], ], 'QueueTransition' => [ 'type' => 'structure', 'members' => [ 'DestinationQueue' => [ 'shape' => '__string', 'locationName' => 'destinationQueue', ], 'SourceQueue' => [ 'shape' => '__string', 'locationName' => 'sourceQueue', ], 'Timestamp' => [ 'shape' => '__timestampUnix', 'locationName' => 'timestamp', ], ], ], 'Rectangle' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__integerMin2Max2147483647', 'locationName' => 'height', ], 'Width' => [ 'shape' => '__integerMin2Max2147483647', 'locationName' => 'width', ], 'X' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'x', ], 'Y' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'y', ], ], ], 'RemixSettings' => [ 'type' => 'structure', 'members' => [ 'AudioDescriptionAudioChannel' => [ 'shape' => '__integerMin1Max64', 'locationName' => 'audioDescriptionAudioChannel', ], 'AudioDescriptionDataChannel' => [ 'shape' => '__integerMin1Max64', 'locationName' => 'audioDescriptionDataChannel', ], 'ChannelMapping' => [ 'shape' => 'ChannelMapping', 'locationName' => 'channelMapping', ], 'ChannelsIn' => [ 'shape' => '__integerMin1Max64', 'locationName' => 'channelsIn', ], 'ChannelsOut' => [ 'shape' => '__integerMin1Max64', 'locationName' => 'channelsOut', ], ], ], 'RemoveRubyReserveAttributes' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'RenewalType' => [ 'type' => 'string', 'enum' => [ 'AUTO_RENEW', 'EXPIRE', ], ], 'RequiredFlag' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ReservationPlan' => [ 'type' => 'structure', 'members' => [ 'Commitment' => [ 'shape' => 'Commitment', 'locationName' => 'commitment', ], 'ExpiresAt' => [ 'shape' => '__timestampUnix', 'locationName' => 'expiresAt', ], 'PurchasedAt' => [ 'shape' => '__timestampUnix', 'locationName' => 'purchasedAt', ], 'RenewalType' => [ 'shape' => 'RenewalType', 'locationName' => 'renewalType', ], 'ReservedSlots' => [ 'shape' => '__integer', 'locationName' => 'reservedSlots', ], 'Status' => [ 'shape' => 'ReservationPlanStatus', 'locationName' => 'status', ], ], ], 'ReservationPlanSettings' => [ 'type' => 'structure', 'members' => [ 'Commitment' => [ 'shape' => 'Commitment', 'locationName' => 'commitment', ], 'RenewalType' => [ 'shape' => 'RenewalType', 'locationName' => 'renewalType', ], 'ReservedSlots' => [ 'shape' => '__integer', 'locationName' => 'reservedSlots', ], ], 'required' => [ 'Commitment', 'ReservedSlots', 'RenewalType', ], ], 'ReservationPlanStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'EXPIRED', ], ], 'ResourceTags' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], ], 'RespondToAfd' => [ 'type' => 'string', 'enum' => [ 'NONE', 'RESPOND', 'PASSTHROUGH', ], ], 'RuleType' => [ 'type' => 'string', 'enum' => [ 'MIN_TOP_RENDITION_SIZE', 'MIN_BOTTOM_RENDITION_SIZE', 'FORCE_INCLUDE_RENDITIONS', 'ALLOWED_RENDITIONS', ], ], 'S3DestinationAccessControl' => [ 'type' => 'structure', 'members' => [ 'CannedAcl' => [ 'shape' => 'S3ObjectCannedAcl', 'locationName' => 'cannedAcl', ], ], ], 'S3DestinationSettings' => [ 'type' => 'structure', 'members' => [ 'AccessControl' => [ 'shape' => 'S3DestinationAccessControl', 'locationName' => 'accessControl', ], 'Encryption' => [ 'shape' => 'S3EncryptionSettings', 'locationName' => 'encryption', ], 'StorageClass' => [ 'shape' => 'S3StorageClass', 'locationName' => 'storageClass', ], ], ], 'S3EncryptionSettings' => [ 'type' => 'structure', 'members' => [ 'EncryptionType' => [ 'shape' => 'S3ServerSideEncryptionType', 'locationName' => 'encryptionType', ], 'KmsEncryptionContext' => [ 'shape' => '__stringPatternAZaZ0902', 'locationName' => 'kmsEncryptionContext', ], 'KmsKeyArn' => [ 'shape' => '__stringPatternArnAwsUsGovCnKmsAZ26EastWestCentralNorthSouthEastWest1912D12KeyAFAF098AFAF094AFAF094AFAF094AFAF0912MrkAFAF0932', 'locationName' => 'kmsKeyArn', ], ], ], 'S3ObjectCannedAcl' => [ 'type' => 'string', 'enum' => [ 'PUBLIC_READ', 'AUTHENTICATED_READ', 'BUCKET_OWNER_READ', 'BUCKET_OWNER_FULL_CONTROL', ], ], 'S3ServerSideEncryptionType' => [ 'type' => 'string', 'enum' => [ 'SERVER_SIDE_ENCRYPTION_S3', 'SERVER_SIDE_ENCRYPTION_KMS', ], ], 'S3StorageClass' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'REDUCED_REDUNDANCY', 'STANDARD_IA', 'ONEZONE_IA', 'INTELLIGENT_TIERING', 'GLACIER', 'DEEP_ARCHIVE', ], ], 'SampleRangeConversion' => [ 'type' => 'string', 'enum' => [ 'LIMITED_RANGE_SQUEEZE', 'NONE', 'LIMITED_RANGE_CLIP', ], ], 'ScalingBehavior' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'STRETCH_TO_OUTPUT', 'FIT', 'FIT_NO_UPSCALE', 'FILL', ], ], 'SccDestinationFramerate' => [ 'type' => 'string', 'enum' => [ 'FRAMERATE_23_97', 'FRAMERATE_24', 'FRAMERATE_25', 'FRAMERATE_29_97_DROPFRAME', 'FRAMERATE_29_97_NON_DROPFRAME', ], ], 'SccDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Framerate' => [ 'shape' => 'SccDestinationFramerate', 'locationName' => 'framerate', ], ], ], 'SearchJobsRequest' => [ 'type' => 'structure', 'members' => [ 'InputFile' => [ 'shape' => '__string', 'locationName' => 'inputFile', 'location' => 'querystring', ], 'MaxResults' => [ 'shape' => '__integerMin1Max20', 'locationName' => 'maxResults', 'location' => 'querystring', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', 'location' => 'querystring', ], 'Order' => [ 'shape' => 'Order', 'locationName' => 'order', 'location' => 'querystring', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', 'location' => 'querystring', ], 'Status' => [ 'shape' => 'JobStatus', 'locationName' => 'status', 'location' => 'querystring', ], ], ], 'SearchJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => '__listOfJob', 'locationName' => 'jobs', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ServiceOverride' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], 'OverrideValue' => [ 'shape' => '__string', 'locationName' => 'overrideValue', ], 'Value' => [ 'shape' => '__string', 'locationName' => 'value', ], ], ], 'SimulateReservedQueue' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'SpekeKeyProvider' => [ 'type' => 'structure', 'members' => [ 'CertificateArn' => [ 'shape' => '__stringPatternArnAwsUsGovAcm', 'locationName' => 'certificateArn', ], 'EncryptionContractConfiguration' => [ 'shape' => 'EncryptionContractConfiguration', 'locationName' => 'encryptionContractConfiguration', ], 'ResourceId' => [ 'shape' => '__string', 'locationName' => 'resourceId', ], 'SystemIds' => [ 'shape' => '__listOf__stringPattern09aFAF809aFAF409aFAF409aFAF409aFAF12', 'locationName' => 'systemIds', ], 'Url' => [ 'shape' => '__stringPatternHttpsD', 'locationName' => 'url', ], ], ], 'SpekeKeyProviderCmaf' => [ 'type' => 'structure', 'members' => [ 'CertificateArn' => [ 'shape' => '__stringPatternArnAwsUsGovAcm', 'locationName' => 'certificateArn', ], 'DashSignaledSystemIds' => [ 'shape' => '__listOf__stringMin36Max36Pattern09aFAF809aFAF409aFAF409aFAF409aFAF12', 'locationName' => 'dashSignaledSystemIds', ], 'EncryptionContractConfiguration' => [ 'shape' => 'EncryptionContractConfiguration', 'locationName' => 'encryptionContractConfiguration', ], 'HlsSignaledSystemIds' => [ 'shape' => '__listOf__stringMin36Max36Pattern09aFAF809aFAF409aFAF409aFAF409aFAF12', 'locationName' => 'hlsSignaledSystemIds', ], 'ResourceId' => [ 'shape' => '__stringPatternW', 'locationName' => 'resourceId', ], 'Url' => [ 'shape' => '__stringPatternHttpsD', 'locationName' => 'url', ], ], ], 'SrtDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'StylePassthrough' => [ 'shape' => 'SrtStylePassthrough', 'locationName' => 'stylePassthrough', ], ], ], 'SrtStylePassthrough' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'StaticKeyProvider' => [ 'type' => 'structure', 'members' => [ 'KeyFormat' => [ 'shape' => '__stringPatternIdentityAZaZ26AZaZ09163', 'locationName' => 'keyFormat', ], 'KeyFormatVersions' => [ 'shape' => '__stringPatternDD', 'locationName' => 'keyFormatVersions', ], 'StaticKeyValue' => [ 'shape' => '__stringPatternAZaZ0932', 'locationName' => 'staticKeyValue', ], 'Url' => [ 'shape' => '__string', 'locationName' => 'url', ], ], ], 'StatusUpdateInterval' => [ 'type' => 'string', 'enum' => [ 'SECONDS_10', 'SECONDS_12', 'SECONDS_15', 'SECONDS_20', 'SECONDS_30', 'SECONDS_60', 'SECONDS_120', 'SECONDS_180', 'SECONDS_240', 'SECONDS_300', 'SECONDS_360', 'SECONDS_420', 'SECONDS_480', 'SECONDS_540', 'SECONDS_600', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', ], 'Tags' => [ 'shape' => '__mapOf__string', 'locationName' => 'tags', ], ], 'required' => [ 'Arn', 'Tags', ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TeletextDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'PageNumber' => [ 'shape' => '__stringMin3Max3Pattern1809aFAF09aEAE', 'locationName' => 'pageNumber', ], 'PageTypes' => [ 'shape' => '__listOfTeletextPageType', 'locationName' => 'pageTypes', ], ], ], 'TeletextPageType' => [ 'type' => 'string', 'enum' => [ 'PAGE_TYPE_INITIAL', 'PAGE_TYPE_SUBTITLE', 'PAGE_TYPE_ADDL_INFO', 'PAGE_TYPE_PROGRAM_SCHEDULE', 'PAGE_TYPE_HEARING_IMPAIRED_SUBTITLE', ], ], 'TeletextSourceSettings' => [ 'type' => 'structure', 'members' => [ 'PageNumber' => [ 'shape' => '__stringMin3Max3Pattern1809aFAF09aEAE', 'locationName' => 'pageNumber', ], ], ], 'TimecodeBurnin' => [ 'type' => 'structure', 'members' => [ 'FontSize' => [ 'shape' => '__integerMin10Max48', 'locationName' => 'fontSize', ], 'Position' => [ 'shape' => 'TimecodeBurninPosition', 'locationName' => 'position', ], 'Prefix' => [ 'shape' => '__stringPattern', 'locationName' => 'prefix', ], ], ], 'TimecodeBurninPosition' => [ 'type' => 'string', 'enum' => [ 'TOP_CENTER', 'TOP_LEFT', 'TOP_RIGHT', 'MIDDLE_LEFT', 'MIDDLE_CENTER', 'MIDDLE_RIGHT', 'BOTTOM_LEFT', 'BOTTOM_CENTER', 'BOTTOM_RIGHT', ], ], 'TimecodeConfig' => [ 'type' => 'structure', 'members' => [ 'Anchor' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'anchor', ], 'Source' => [ 'shape' => 'TimecodeSource', 'locationName' => 'source', ], 'Start' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'start', ], 'TimestampOffset' => [ 'shape' => '__stringPattern0940191020191209301', 'locationName' => 'timestampOffset', ], ], ], 'TimecodeSource' => [ 'type' => 'string', 'enum' => [ 'EMBEDDED', 'ZEROBASED', 'SPECIFIEDSTART', ], ], 'TimecodeTrack' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'TimedMetadata' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'NONE', ], ], 'TimedMetadataInsertion' => [ 'type' => 'structure', 'members' => [ 'Id3Insertions' => [ 'shape' => '__listOfId3Insertion', 'locationName' => 'id3Insertions', ], ], ], 'Timing' => [ 'type' => 'structure', 'members' => [ 'FinishTime' => [ 'shape' => '__timestampUnix', 'locationName' => 'finishTime', ], 'StartTime' => [ 'shape' => '__timestampUnix', 'locationName' => 'startTime', ], 'SubmitTime' => [ 'shape' => '__timestampUnix', 'locationName' => 'submitTime', ], ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 429, ], ], 'Track' => [ 'type' => 'structure', 'members' => [ 'AudioProperties' => [ 'shape' => 'AudioProperties', 'locationName' => 'audioProperties', ], 'Codec' => [ 'shape' => 'Codec', 'locationName' => 'codec', ], 'DataProperties' => [ 'shape' => 'DataProperties', 'locationName' => 'dataProperties', ], 'Duration' => [ 'shape' => '__double', 'locationName' => 'duration', ], 'Index' => [ 'shape' => '__integer', 'locationName' => 'index', ], 'TrackType' => [ 'shape' => 'TrackType', 'locationName' => 'trackType', ], 'VideoProperties' => [ 'shape' => 'VideoProperties', 'locationName' => 'videoProperties', ], ], ], 'TrackMapping' => [ 'type' => 'structure', 'members' => [ 'AudioTrackIndexes' => [ 'shape' => '__listOf__integer', 'locationName' => 'audioTrackIndexes', ], 'DataTrackIndexes' => [ 'shape' => '__listOf__integer', 'locationName' => 'dataTrackIndexes', ], 'VideoTrackIndexes' => [ 'shape' => '__listOf__integer', 'locationName' => 'videoTrackIndexes', ], ], ], 'TrackSourceSettings' => [ 'type' => 'structure', 'members' => [ 'TrackNumber' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'trackNumber', ], ], ], 'TrackType' => [ 'type' => 'string', 'enum' => [ 'video', 'audio', 'data', ], ], 'TransferCharacteristics' => [ 'type' => 'string', 'enum' => [ 'ITU_709', 'UNSPECIFIED', 'RESERVED', 'ITU_470M', 'ITU_470BG', 'SMPTE_170M', 'SMPTE_240M', 'LINEAR', 'LOG10_2', 'LOC10_2_5', 'IEC_61966_2_4', 'ITU_1361', 'IEC_61966_2_1', 'ITU_2020_10bit', 'ITU_2020_12bit', 'SMPTE_2084', 'SMPTE_428_1', 'ARIB_B67', 'LAST', ], ], 'TsPtsOffset' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'SECONDS', ], ], 'TtmlDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'StylePassthrough' => [ 'shape' => 'TtmlStylePassthrough', 'locationName' => 'stylePassthrough', ], ], ], 'TtmlStylePassthrough' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Type' => [ 'type' => 'string', 'enum' => [ 'SYSTEM', 'CUSTOM', ], ], 'UncompressedFourcc' => [ 'type' => 'string', 'enum' => [ 'I420', 'I422', 'I444', ], ], 'UncompressedFramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'UncompressedFramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'UncompressedInterlaceMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'PROGRESSIVE', ], ], 'UncompressedScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'UncompressedSettings' => [ 'type' => 'structure', 'members' => [ 'Fourcc' => [ 'shape' => 'UncompressedFourcc', 'locationName' => 'fourcc', ], 'FramerateControl' => [ 'shape' => 'UncompressedFramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'UncompressedFramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'InterlaceMode' => [ 'shape' => 'UncompressedInterlaceMode', 'locationName' => 'interlaceMode', ], 'ScanTypeConversionMode' => [ 'shape' => 'UncompressedScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SlowPal' => [ 'shape' => 'UncompressedSlowPal', 'locationName' => 'slowPal', ], 'Telecine' => [ 'shape' => 'UncompressedTelecine', 'locationName' => 'telecine', ], ], ], 'UncompressedSlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'UncompressedTelecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'HARD', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => '__string', 'locationName' => 'arn', 'location' => 'uri', ], 'TagKeys' => [ 'shape' => '__listOf__string', 'locationName' => 'tagKeys', ], ], 'required' => [ 'Arn', ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateJobTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'AccelerationSettings' => [ 'shape' => 'AccelerationSettings', 'locationName' => 'accelerationSettings', ], 'Category' => [ 'shape' => '__string', 'locationName' => 'category', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'HopDestinations' => [ 'shape' => '__listOfHopDestination', 'locationName' => 'hopDestinations', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], 'Priority' => [ 'shape' => '__integerMinNegative50Max50', 'locationName' => 'priority', ], 'Queue' => [ 'shape' => '__string', 'locationName' => 'queue', ], 'Settings' => [ 'shape' => 'JobTemplateSettings', 'locationName' => 'settings', ], 'StatusUpdateInterval' => [ 'shape' => 'StatusUpdateInterval', 'locationName' => 'statusUpdateInterval', ], ], 'required' => [ 'Name', ], ], 'UpdateJobTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'JobTemplate' => [ 'shape' => 'JobTemplate', 'locationName' => 'jobTemplate', ], ], ], 'UpdatePresetRequest' => [ 'type' => 'structure', 'members' => [ 'Category' => [ 'shape' => '__string', 'locationName' => 'category', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], 'Settings' => [ 'shape' => 'PresetSettings', 'locationName' => 'settings', ], ], 'required' => [ 'Name', ], ], 'UpdatePresetResponse' => [ 'type' => 'structure', 'members' => [ 'Preset' => [ 'shape' => 'Preset', 'locationName' => 'preset', ], ], ], 'UpdateQueueRequest' => [ 'type' => 'structure', 'members' => [ 'ConcurrentJobs' => [ 'shape' => '__integer', 'locationName' => 'concurrentJobs', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', 'location' => 'uri', ], 'ReservationPlanSettings' => [ 'shape' => 'ReservationPlanSettings', 'locationName' => 'reservationPlanSettings', ], 'Status' => [ 'shape' => 'QueueStatus', 'locationName' => 'status', ], ], 'required' => [ 'Name', ], ], 'UpdateQueueResponse' => [ 'type' => 'structure', 'members' => [ 'Queue' => [ 'shape' => 'Queue', 'locationName' => 'queue', ], ], ], 'Vc3Class' => [ 'type' => 'string', 'enum' => [ 'CLASS_145_8BIT', 'CLASS_220_8BIT', 'CLASS_220_10BIT', ], ], 'Vc3FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Vc3FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'Vc3InterlaceMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'PROGRESSIVE', ], ], 'Vc3ScanTypeConversionMode' => [ 'type' => 'string', 'enum' => [ 'INTERLACED', 'INTERLACED_OPTIMIZE', ], ], 'Vc3Settings' => [ 'type' => 'structure', 'members' => [ 'FramerateControl' => [ 'shape' => 'Vc3FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'Vc3FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max1001', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin24Max60000', 'locationName' => 'framerateNumerator', ], 'InterlaceMode' => [ 'shape' => 'Vc3InterlaceMode', 'locationName' => 'interlaceMode', ], 'ScanTypeConversionMode' => [ 'shape' => 'Vc3ScanTypeConversionMode', 'locationName' => 'scanTypeConversionMode', ], 'SlowPal' => [ 'shape' => 'Vc3SlowPal', 'locationName' => 'slowPal', ], 'Telecine' => [ 'shape' => 'Vc3Telecine', 'locationName' => 'telecine', ], 'Vc3Class' => [ 'shape' => 'Vc3Class', 'locationName' => 'vc3Class', ], ], ], 'Vc3SlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'Vc3Telecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'HARD', ], ], 'VchipAction' => [ 'type' => 'string', 'enum' => [ 'PASSTHROUGH', 'STRIP', ], ], 'VideoCodec' => [ 'type' => 'string', 'enum' => [ 'AV1', 'AVC_INTRA', 'FRAME_CAPTURE', 'GIF', 'H_264', 'H_265', 'MPEG2', 'PASSTHROUGH', 'PRORES', 'UNCOMPRESSED', 'VC3', 'VP8', 'VP9', 'XAVC', ], ], 'VideoCodecSettings' => [ 'type' => 'structure', 'members' => [ 'Av1Settings' => [ 'shape' => 'Av1Settings', 'locationName' => 'av1Settings', ], 'AvcIntraSettings' => [ 'shape' => 'AvcIntraSettings', 'locationName' => 'avcIntraSettings', ], 'Codec' => [ 'shape' => 'VideoCodec', 'locationName' => 'codec', ], 'FrameCaptureSettings' => [ 'shape' => 'FrameCaptureSettings', 'locationName' => 'frameCaptureSettings', ], 'GifSettings' => [ 'shape' => 'GifSettings', 'locationName' => 'gifSettings', ], 'H264Settings' => [ 'shape' => 'H264Settings', 'locationName' => 'h264Settings', ], 'H265Settings' => [ 'shape' => 'H265Settings', 'locationName' => 'h265Settings', ], 'Mpeg2Settings' => [ 'shape' => 'Mpeg2Settings', 'locationName' => 'mpeg2Settings', ], 'ProresSettings' => [ 'shape' => 'ProresSettings', 'locationName' => 'proresSettings', ], 'UncompressedSettings' => [ 'shape' => 'UncompressedSettings', 'locationName' => 'uncompressedSettings', ], 'Vc3Settings' => [ 'shape' => 'Vc3Settings', 'locationName' => 'vc3Settings', ], 'Vp8Settings' => [ 'shape' => 'Vp8Settings', 'locationName' => 'vp8Settings', ], 'Vp9Settings' => [ 'shape' => 'Vp9Settings', 'locationName' => 'vp9Settings', ], 'XavcSettings' => [ 'shape' => 'XavcSettings', 'locationName' => 'xavcSettings', ], ], ], 'VideoDescription' => [ 'type' => 'structure', 'members' => [ 'AfdSignaling' => [ 'shape' => 'AfdSignaling', 'locationName' => 'afdSignaling', ], 'AntiAlias' => [ 'shape' => 'AntiAlias', 'locationName' => 'antiAlias', ], 'ChromaPositionMode' => [ 'shape' => 'ChromaPositionMode', 'locationName' => 'chromaPositionMode', ], 'CodecSettings' => [ 'shape' => 'VideoCodecSettings', 'locationName' => 'codecSettings', ], 'ColorMetadata' => [ 'shape' => 'ColorMetadata', 'locationName' => 'colorMetadata', ], 'Crop' => [ 'shape' => 'Rectangle', 'locationName' => 'crop', ], 'DropFrameTimecode' => [ 'shape' => 'DropFrameTimecode', 'locationName' => 'dropFrameTimecode', ], 'FixedAfd' => [ 'shape' => '__integerMin0Max15', 'locationName' => 'fixedAfd', ], 'Height' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'height', ], 'Position' => [ 'shape' => 'Rectangle', 'locationName' => 'position', ], 'RespondToAfd' => [ 'shape' => 'RespondToAfd', 'locationName' => 'respondToAfd', ], 'ScalingBehavior' => [ 'shape' => 'ScalingBehavior', 'locationName' => 'scalingBehavior', ], 'Sharpness' => [ 'shape' => '__integerMin0Max100', 'locationName' => 'sharpness', ], 'TimecodeInsertion' => [ 'shape' => 'VideoTimecodeInsertion', 'locationName' => 'timecodeInsertion', ], 'TimecodeTrack' => [ 'shape' => 'TimecodeTrack', 'locationName' => 'timecodeTrack', ], 'VideoPreprocessors' => [ 'shape' => 'VideoPreprocessor', 'locationName' => 'videoPreprocessors', ], 'Width' => [ 'shape' => '__integerMin32Max8192', 'locationName' => 'width', ], ], ], 'VideoDetail' => [ 'type' => 'structure', 'members' => [ 'HeightInPx' => [ 'shape' => '__integer', 'locationName' => 'heightInPx', ], 'WidthInPx' => [ 'shape' => '__integer', 'locationName' => 'widthInPx', ], ], ], 'VideoOverlay' => [ 'type' => 'structure', 'members' => [ 'EndTimecode' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'endTimecode', ], 'InitialPosition' => [ 'shape' => 'VideoOverlayPosition', 'locationName' => 'initialPosition', ], 'Input' => [ 'shape' => 'VideoOverlayInput', 'locationName' => 'input', ], 'Playback' => [ 'shape' => 'VideoOverlayPlayBackMode', 'locationName' => 'playback', ], 'StartTimecode' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'startTimecode', ], 'Transitions' => [ 'shape' => '__listOfVideoOverlayTransition', 'locationName' => 'transitions', ], ], ], 'VideoOverlayInput' => [ 'type' => 'structure', 'members' => [ 'FileInput' => [ 'shape' => '__stringPatternS3Https', 'locationName' => 'fileInput', ], 'InputClippings' => [ 'shape' => '__listOfVideoOverlayInputClipping', 'locationName' => 'inputClippings', ], 'TimecodeSource' => [ 'shape' => 'InputTimecodeSource', 'locationName' => 'timecodeSource', ], 'TimecodeStart' => [ 'shape' => '__stringMin11Max11Pattern01D20305D205D', 'locationName' => 'timecodeStart', ], ], ], 'VideoOverlayInputClipping' => [ 'type' => 'structure', 'members' => [ 'EndTimecode' => [ 'shape' => '__stringPattern010920405090509092090909', 'locationName' => 'endTimecode', ], 'StartTimecode' => [ 'shape' => '__stringPattern010920405090509092090909', 'locationName' => 'startTimecode', ], ], ], 'VideoOverlayPlayBackMode' => [ 'type' => 'string', 'enum' => [ 'ONCE', 'REPEAT', ], ], 'VideoOverlayPosition' => [ 'type' => 'structure', 'members' => [ 'Height' => [ 'shape' => '__integerMinNegative1Max2147483647', 'locationName' => 'height', ], 'Unit' => [ 'shape' => 'VideoOverlayUnit', 'locationName' => 'unit', ], 'Width' => [ 'shape' => '__integerMinNegative1Max2147483647', 'locationName' => 'width', ], 'XPosition' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'xPosition', ], 'YPosition' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'yPosition', ], ], ], 'VideoOverlayTransition' => [ 'type' => 'structure', 'members' => [ 'EndPosition' => [ 'shape' => 'VideoOverlayPosition', 'locationName' => 'endPosition', ], 'EndTimecode' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'endTimecode', ], 'StartTimecode' => [ 'shape' => '__stringPattern010920405090509092', 'locationName' => 'startTimecode', ], ], ], 'VideoOverlayUnit' => [ 'type' => 'string', 'enum' => [ 'PIXELS', 'PERCENTAGE', ], ], 'VideoPreprocessor' => [ 'type' => 'structure', 'members' => [ 'ColorCorrector' => [ 'shape' => 'ColorCorrector', 'locationName' => 'colorCorrector', ], 'Deinterlacer' => [ 'shape' => 'Deinterlacer', 'locationName' => 'deinterlacer', ], 'DolbyVision' => [ 'shape' => 'DolbyVision', 'locationName' => 'dolbyVision', ], 'Hdr10Plus' => [ 'shape' => 'Hdr10Plus', 'locationName' => 'hdr10Plus', ], 'ImageInserter' => [ 'shape' => 'ImageInserter', 'locationName' => 'imageInserter', ], 'NoiseReducer' => [ 'shape' => 'NoiseReducer', 'locationName' => 'noiseReducer', ], 'PartnerWatermarking' => [ 'shape' => 'PartnerWatermarking', 'locationName' => 'partnerWatermarking', ], 'TimecodeBurnin' => [ 'shape' => 'TimecodeBurnin', 'locationName' => 'timecodeBurnin', ], ], ], 'VideoProperties' => [ 'type' => 'structure', 'members' => [ 'BitDepth' => [ 'shape' => '__integer', 'locationName' => 'bitDepth', ], 'BitRate' => [ 'shape' => '__integer', 'locationName' => 'bitRate', ], 'ColorPrimaries' => [ 'shape' => 'ColorPrimaries', 'locationName' => 'colorPrimaries', ], 'FrameRate' => [ 'shape' => 'FrameRate', 'locationName' => 'frameRate', ], 'Height' => [ 'shape' => '__integer', 'locationName' => 'height', ], 'MatrixCoefficients' => [ 'shape' => 'MatrixCoefficients', 'locationName' => 'matrixCoefficients', ], 'TransferCharacteristics' => [ 'shape' => 'TransferCharacteristics', 'locationName' => 'transferCharacteristics', ], 'Width' => [ 'shape' => '__integer', 'locationName' => 'width', ], ], ], 'VideoSelector' => [ 'type' => 'structure', 'members' => [ 'AlphaBehavior' => [ 'shape' => 'AlphaBehavior', 'locationName' => 'alphaBehavior', ], 'ColorSpace' => [ 'shape' => 'ColorSpace', 'locationName' => 'colorSpace', ], 'ColorSpaceUsage' => [ 'shape' => 'ColorSpaceUsage', 'locationName' => 'colorSpaceUsage', ], 'EmbeddedTimecodeOverride' => [ 'shape' => 'EmbeddedTimecodeOverride', 'locationName' => 'embeddedTimecodeOverride', ], 'Hdr10Metadata' => [ 'shape' => 'Hdr10Metadata', 'locationName' => 'hdr10Metadata', ], 'MaxLuminance' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'maxLuminance', ], 'PadVideo' => [ 'shape' => 'PadVideo', 'locationName' => 'padVideo', ], 'Pid' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'pid', ], 'ProgramNumber' => [ 'shape' => '__integerMinNegative2147483648Max2147483647', 'locationName' => 'programNumber', ], 'Rotate' => [ 'shape' => 'InputRotate', 'locationName' => 'rotate', ], 'SampleRange' => [ 'shape' => 'InputSampleRange', 'locationName' => 'sampleRange', ], ], ], 'VideoTimecodeInsertion' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'PIC_TIMING_SEI', ], ], 'VorbisSettings' => [ 'type' => 'structure', 'members' => [ 'Channels' => [ 'shape' => '__integerMin1Max2', 'locationName' => 'channels', ], 'SampleRate' => [ 'shape' => '__integerMin22050Max48000', 'locationName' => 'sampleRate', ], 'VbrQuality' => [ 'shape' => '__integerMinNegative1Max10', 'locationName' => 'vbrQuality', ], ], ], 'Vp8FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Vp8FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'Vp8ParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Vp8QualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'MULTI_PASS', 'MULTI_PASS_HQ', ], ], 'Vp8RateControlMode' => [ 'type' => 'string', 'enum' => [ 'VBR', ], ], 'Vp8Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin1000Max1152000000', 'locationName' => 'bitrate', ], 'FramerateControl' => [ 'shape' => 'Vp8FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'Vp8FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max47185920', 'locationName' => 'hrdBufferSize', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000Max1152000000', 'locationName' => 'maxBitrate', ], 'ParControl' => [ 'shape' => 'Vp8ParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parNumerator', ], 'QualityTuningLevel' => [ 'shape' => 'Vp8QualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'RateControlMode' => [ 'shape' => 'Vp8RateControlMode', 'locationName' => 'rateControlMode', ], ], ], 'Vp9FramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Vp9FramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'Vp9ParControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'Vp9QualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'MULTI_PASS', 'MULTI_PASS_HQ', ], ], 'Vp9RateControlMode' => [ 'type' => 'string', 'enum' => [ 'VBR', ], ], 'Vp9Settings' => [ 'type' => 'structure', 'members' => [ 'Bitrate' => [ 'shape' => '__integerMin1000Max480000000', 'locationName' => 'bitrate', ], 'FramerateControl' => [ 'shape' => 'Vp9FramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'Vp9FramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'framerateNumerator', ], 'GopSize' => [ 'shape' => '__doubleMin0', 'locationName' => 'gopSize', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max47185920', 'locationName' => 'hrdBufferSize', ], 'MaxBitrate' => [ 'shape' => '__integerMin1000Max480000000', 'locationName' => 'maxBitrate', ], 'ParControl' => [ 'shape' => 'Vp9ParControl', 'locationName' => 'parControl', ], 'ParDenominator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parDenominator', ], 'ParNumerator' => [ 'shape' => '__integerMin1Max2147483647', 'locationName' => 'parNumerator', ], 'QualityTuningLevel' => [ 'shape' => 'Vp9QualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'RateControlMode' => [ 'shape' => 'Vp9RateControlMode', 'locationName' => 'rateControlMode', ], ], ], 'WarningGroup' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => '__integer', 'locationName' => 'code', ], 'Count' => [ 'shape' => '__integer', 'locationName' => 'count', ], ], 'required' => [ 'Count', 'Code', ], ], 'WatermarkingStrength' => [ 'type' => 'string', 'enum' => [ 'LIGHTEST', 'LIGHTER', 'DEFAULT', 'STRONGER', 'STRONGEST', ], ], 'WavFormat' => [ 'type' => 'string', 'enum' => [ 'RIFF', 'RF64', 'EXTENSIBLE', ], ], 'WavSettings' => [ 'type' => 'structure', 'members' => [ 'BitDepth' => [ 'shape' => '__integerMin16Max24', 'locationName' => 'bitDepth', ], 'Channels' => [ 'shape' => '__integerMin1Max64', 'locationName' => 'channels', ], 'Format' => [ 'shape' => 'WavFormat', 'locationName' => 'format', ], 'SampleRate' => [ 'shape' => '__integerMin8000Max192000', 'locationName' => 'sampleRate', ], ], ], 'WebvttAccessibilitySubs' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'WebvttDestinationSettings' => [ 'type' => 'structure', 'members' => [ 'Accessibility' => [ 'shape' => 'WebvttAccessibilitySubs', 'locationName' => 'accessibility', ], 'StylePassthrough' => [ 'shape' => 'WebvttStylePassthrough', 'locationName' => 'stylePassthrough', ], ], ], 'WebvttHlsSourceSettings' => [ 'type' => 'structure', 'members' => [ 'RenditionGroupId' => [ 'shape' => '__string', 'locationName' => 'renditionGroupId', ], 'RenditionLanguageCode' => [ 'shape' => 'LanguageCode', 'locationName' => 'renditionLanguageCode', ], 'RenditionName' => [ 'shape' => '__string', 'locationName' => 'renditionName', ], ], ], 'WebvttStylePassthrough' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', 'STRICT', 'MERGE', ], ], 'Xavc4kIntraCbgProfileClass' => [ 'type' => 'string', 'enum' => [ 'CLASS_100', 'CLASS_300', 'CLASS_480', ], ], 'Xavc4kIntraCbgProfileSettings' => [ 'type' => 'structure', 'members' => [ 'XavcClass' => [ 'shape' => 'Xavc4kIntraCbgProfileClass', 'locationName' => 'xavcClass', ], ], ], 'Xavc4kIntraVbrProfileClass' => [ 'type' => 'string', 'enum' => [ 'CLASS_100', 'CLASS_300', 'CLASS_480', ], ], 'Xavc4kIntraVbrProfileSettings' => [ 'type' => 'structure', 'members' => [ 'XavcClass' => [ 'shape' => 'Xavc4kIntraVbrProfileClass', 'locationName' => 'xavcClass', ], ], ], 'Xavc4kProfileBitrateClass' => [ 'type' => 'string', 'enum' => [ 'BITRATE_CLASS_100', 'BITRATE_CLASS_140', 'BITRATE_CLASS_200', ], ], 'Xavc4kProfileCodecProfile' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'HIGH_422', ], ], 'Xavc4kProfileQualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PASS', 'SINGLE_PASS_HQ', 'MULTI_PASS_HQ', ], ], 'Xavc4kProfileSettings' => [ 'type' => 'structure', 'members' => [ 'BitrateClass' => [ 'shape' => 'Xavc4kProfileBitrateClass', 'locationName' => 'bitrateClass', ], 'CodecProfile' => [ 'shape' => 'Xavc4kProfileCodecProfile', 'locationName' => 'codecProfile', ], 'FlickerAdaptiveQuantization' => [ 'shape' => 'XavcFlickerAdaptiveQuantization', 'locationName' => 'flickerAdaptiveQuantization', ], 'GopBReference' => [ 'shape' => 'XavcGopBReference', 'locationName' => 'gopBReference', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'gopClosedCadence', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max1152000000', 'locationName' => 'hrdBufferSize', ], 'QualityTuningLevel' => [ 'shape' => 'Xavc4kProfileQualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'Slices' => [ 'shape' => '__integerMin8Max12', 'locationName' => 'slices', ], ], ], 'XavcAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'OFF', 'AUTO', 'LOW', 'MEDIUM', 'HIGH', 'HIGHER', 'MAX', ], ], 'XavcEntropyEncoding' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'CABAC', 'CAVLC', ], ], 'XavcFlickerAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'XavcFramerateControl' => [ 'type' => 'string', 'enum' => [ 'INITIALIZE_FROM_SOURCE', 'SPECIFIED', ], ], 'XavcFramerateConversionAlgorithm' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_DROP', 'INTERPOLATE', 'FRAMEFORMER', 'MAINTAIN_FRAME_COUNT', ], ], 'XavcGopBReference' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'XavcHdIntraCbgProfileClass' => [ 'type' => 'string', 'enum' => [ 'CLASS_50', 'CLASS_100', 'CLASS_200', ], ], 'XavcHdIntraCbgProfileSettings' => [ 'type' => 'structure', 'members' => [ 'XavcClass' => [ 'shape' => 'XavcHdIntraCbgProfileClass', 'locationName' => 'xavcClass', ], ], ], 'XavcHdProfileBitrateClass' => [ 'type' => 'string', 'enum' => [ 'BITRATE_CLASS_25', 'BITRATE_CLASS_35', 'BITRATE_CLASS_50', ], ], 'XavcHdProfileQualityTuningLevel' => [ 'type' => 'string', 'enum' => [ 'SINGLE_PASS', 'SINGLE_PASS_HQ', 'MULTI_PASS_HQ', ], ], 'XavcHdProfileSettings' => [ 'type' => 'structure', 'members' => [ 'BitrateClass' => [ 'shape' => 'XavcHdProfileBitrateClass', 'locationName' => 'bitrateClass', ], 'FlickerAdaptiveQuantization' => [ 'shape' => 'XavcFlickerAdaptiveQuantization', 'locationName' => 'flickerAdaptiveQuantization', ], 'GopBReference' => [ 'shape' => 'XavcGopBReference', 'locationName' => 'gopBReference', ], 'GopClosedCadence' => [ 'shape' => '__integerMin0Max2147483647', 'locationName' => 'gopClosedCadence', ], 'HrdBufferSize' => [ 'shape' => '__integerMin0Max1152000000', 'locationName' => 'hrdBufferSize', ], 'InterlaceMode' => [ 'shape' => 'XavcInterlaceMode', 'locationName' => 'interlaceMode', ], 'QualityTuningLevel' => [ 'shape' => 'XavcHdProfileQualityTuningLevel', 'locationName' => 'qualityTuningLevel', ], 'Slices' => [ 'shape' => '__integerMin4Max12', 'locationName' => 'slices', ], 'Telecine' => [ 'shape' => 'XavcHdProfileTelecine', 'locationName' => 'telecine', ], ], ], 'XavcHdProfileTelecine' => [ 'type' => 'string', 'enum' => [ 'NONE', 'HARD', ], ], 'XavcInterlaceMode' => [ 'type' => 'string', 'enum' => [ 'PROGRESSIVE', 'TOP_FIELD', 'BOTTOM_FIELD', 'FOLLOW_TOP_FIELD', 'FOLLOW_BOTTOM_FIELD', ], ], 'XavcProfile' => [ 'type' => 'string', 'enum' => [ 'XAVC_HD_INTRA_CBG', 'XAVC_4K_INTRA_CBG', 'XAVC_4K_INTRA_VBR', 'XAVC_HD', 'XAVC_4K', ], ], 'XavcSettings' => [ 'type' => 'structure', 'members' => [ 'AdaptiveQuantization' => [ 'shape' => 'XavcAdaptiveQuantization', 'locationName' => 'adaptiveQuantization', ], 'EntropyEncoding' => [ 'shape' => 'XavcEntropyEncoding', 'locationName' => 'entropyEncoding', ], 'FramerateControl' => [ 'shape' => 'XavcFramerateControl', 'locationName' => 'framerateControl', ], 'FramerateConversionAlgorithm' => [ 'shape' => 'XavcFramerateConversionAlgorithm', 'locationName' => 'framerateConversionAlgorithm', ], 'FramerateDenominator' => [ 'shape' => '__integerMin1Max1001', 'locationName' => 'framerateDenominator', ], 'FramerateNumerator' => [ 'shape' => '__integerMin24Max60000', 'locationName' => 'framerateNumerator', ], 'Profile' => [ 'shape' => 'XavcProfile', 'locationName' => 'profile', ], 'SlowPal' => [ 'shape' => 'XavcSlowPal', 'locationName' => 'slowPal', ], 'Softness' => [ 'shape' => '__integerMin0Max128', 'locationName' => 'softness', ], 'SpatialAdaptiveQuantization' => [ 'shape' => 'XavcSpatialAdaptiveQuantization', 'locationName' => 'spatialAdaptiveQuantization', ], 'TemporalAdaptiveQuantization' => [ 'shape' => 'XavcTemporalAdaptiveQuantization', 'locationName' => 'temporalAdaptiveQuantization', ], 'Xavc4kIntraCbgProfileSettings' => [ 'shape' => 'Xavc4kIntraCbgProfileSettings', 'locationName' => 'xavc4kIntraCbgProfileSettings', ], 'Xavc4kIntraVbrProfileSettings' => [ 'shape' => 'Xavc4kIntraVbrProfileSettings', 'locationName' => 'xavc4kIntraVbrProfileSettings', ], 'Xavc4kProfileSettings' => [ 'shape' => 'Xavc4kProfileSettings', 'locationName' => 'xavc4kProfileSettings', ], 'XavcHdIntraCbgProfileSettings' => [ 'shape' => 'XavcHdIntraCbgProfileSettings', 'locationName' => 'xavcHdIntraCbgProfileSettings', ], 'XavcHdProfileSettings' => [ 'shape' => 'XavcHdProfileSettings', 'locationName' => 'xavcHdProfileSettings', ], ], ], 'XavcSlowPal' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'XavcSpatialAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], 'XavcTemporalAdaptiveQuantization' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'ENABLED', ], ], '__boolean' => [ 'type' => 'boolean', ], '__double' => [ 'type' => 'double', ], '__doubleMin0' => [ 'type' => 'double', ], '__doubleMin0Max1' => [ 'type' => 'double', ], '__doubleMin0Max2147483647' => [ 'type' => 'double', ], '__doubleMinNegative59Max0' => [ 'type' => 'double', ], '__doubleMinNegative60Max3' => [ 'type' => 'double', ], '__doubleMinNegative60Max6' => [ 'type' => 'double', ], '__doubleMinNegative60MaxNegative1' => [ 'type' => 'double', ], '__doubleMinNegative6Max3' => [ 'type' => 'double', ], '__doubleMinNegative8Max0' => [ 'type' => 'double', ], '__integer' => [ 'type' => 'integer', ], '__integerMin0Max0' => [ 'type' => 'integer', 'min' => 0, 'max' => 0, ], '__integerMin0Max1' => [ 'type' => 'integer', 'min' => 0, 'max' => 1, ], '__integerMin0Max10' => [ 'type' => 'integer', 'min' => 0, 'max' => 10, ], '__integerMin0Max100' => [ 'type' => 'integer', 'min' => 0, 'max' => 100, ], '__integerMin0Max1000' => [ 'type' => 'integer', 'min' => 0, 'max' => 1000, ], '__integerMin0Max10000' => [ 'type' => 'integer', 'min' => 0, 'max' => 10000, ], '__integerMin0Max1152000000' => [ 'type' => 'integer', 'min' => 0, 'max' => 1152000000, ], '__integerMin0Max128' => [ 'type' => 'integer', 'min' => 0, 'max' => 128, ], '__integerMin0Max1466400000' => [ 'type' => 'integer', 'min' => 0, 'max' => 1466400000, ], '__integerMin0Max15' => [ 'type' => 'integer', 'min' => 0, 'max' => 15, ], '__integerMin0Max16' => [ 'type' => 'integer', 'min' => 0, 'max' => 16, ], '__integerMin0Max2147483647' => [ 'type' => 'integer', 'min' => 0, 'max' => 2147483647, ], '__integerMin0Max255' => [ 'type' => 'integer', 'min' => 0, 'max' => 255, ], '__integerMin0Max3' => [ 'type' => 'integer', 'min' => 0, 'max' => 3, ], '__integerMin0Max30' => [ 'type' => 'integer', 'min' => 0, 'max' => 30, ], '__integerMin0Max30000' => [ 'type' => 'integer', 'min' => 0, 'max' => 30000, ], '__integerMin0Max3600' => [ 'type' => 'integer', 'min' => 0, 'max' => 3600, ], '__integerMin0Max4' => [ 'type' => 'integer', 'min' => 0, 'max' => 4, ], '__integerMin0Max4000' => [ 'type' => 'integer', 'min' => 0, 'max' => 4000, ], '__integerMin0Max4194303' => [ 'type' => 'integer', 'min' => 0, 'max' => 4194303, ], '__integerMin0Max47185920' => [ 'type' => 'integer', 'min' => 0, 'max' => 47185920, ], '__integerMin0Max5' => [ 'type' => 'integer', 'min' => 0, 'max' => 5, ], '__integerMin0Max500' => [ 'type' => 'integer', 'min' => 0, 'max' => 500, ], '__integerMin0Max50000' => [ 'type' => 'integer', 'min' => 0, 'max' => 50000, ], '__integerMin0Max65534' => [ 'type' => 'integer', 'min' => 0, 'max' => 65534, ], '__integerMin0Max65535' => [ 'type' => 'integer', 'min' => 0, 'max' => 65535, ], '__integerMin0Max7' => [ 'type' => 'integer', 'min' => 0, 'max' => 7, ], '__integerMin0Max8' => [ 'type' => 'integer', 'min' => 0, 'max' => 8, ], '__integerMin0Max9' => [ 'type' => 'integer', 'min' => 0, 'max' => 9, ], '__integerMin0Max96' => [ 'type' => 'integer', 'min' => 0, 'max' => 96, ], '__integerMin0Max99' => [ 'type' => 'integer', 'min' => 0, 'max' => 99, ], '__integerMin100000Max100000000' => [ 'type' => 'integer', 'min' => 100000, 'max' => 100000000, ], '__integerMin1000Max1152000000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 1152000000, ], '__integerMin1000Max1466400000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 1466400000, ], '__integerMin1000Max288000000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 288000000, ], '__integerMin1000Max30000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 30000, ], '__integerMin1000Max300000000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 300000000, ], '__integerMin1000Max480000000' => [ 'type' => 'integer', 'min' => 1000, 'max' => 480000000, ], '__integerMin100Max1000' => [ 'type' => 'integer', 'min' => 100, 'max' => 1000, ], '__integerMin10Max48' => [ 'type' => 'integer', 'min' => 10, 'max' => 48, ], '__integerMin16000Max320000' => [ 'type' => 'integer', 'min' => 16000, 'max' => 320000, ], '__integerMin16000Max48000' => [ 'type' => 'integer', 'min' => 16000, 'max' => 48000, ], '__integerMin16Max24' => [ 'type' => 'integer', 'min' => 16, 'max' => 24, ], '__integerMin1Max1' => [ 'type' => 'integer', 'min' => 1, 'max' => 1, ], '__integerMin1Max10' => [ 'type' => 'integer', 'min' => 1, 'max' => 10, ], '__integerMin1Max100' => [ 'type' => 'integer', 'min' => 1, 'max' => 100, ], '__integerMin1Max10000000' => [ 'type' => 'integer', 'min' => 1, 'max' => 10000000, ], '__integerMin1Max1001' => [ 'type' => 'integer', 'min' => 1, 'max' => 1001, ], '__integerMin1Max150' => [ 'type' => 'integer', 'min' => 1, 'max' => 150, ], '__integerMin1Max17895697' => [ 'type' => 'integer', 'min' => 1, 'max' => 17895697, ], '__integerMin1Max2' => [ 'type' => 'integer', 'min' => 1, 'max' => 2, ], '__integerMin1Max20' => [ 'type' => 'integer', 'min' => 1, 'max' => 20, ], '__integerMin1Max2048' => [ 'type' => 'integer', 'min' => 1, 'max' => 2048, ], '__integerMin1Max2147483640' => [ 'type' => 'integer', 'min' => 1, 'max' => 2147483640, ], '__integerMin1Max2147483647' => [ 'type' => 'integer', 'min' => 1, 'max' => 2147483647, ], '__integerMin1Max31' => [ 'type' => 'integer', 'min' => 1, 'max' => 31, ], '__integerMin1Max32' => [ 'type' => 'integer', 'min' => 1, 'max' => 32, ], '__integerMin1Max4' => [ 'type' => 'integer', 'min' => 1, 'max' => 4, ], '__integerMin1Max4096' => [ 'type' => 'integer', 'min' => 1, 'max' => 4096, ], '__integerMin1Max512' => [ 'type' => 'integer', 'min' => 1, 'max' => 512, ], '__integerMin1Max6' => [ 'type' => 'integer', 'min' => 1, 'max' => 6, ], '__integerMin1Max60000' => [ 'type' => 'integer', 'min' => 1, 'max' => 60000, ], '__integerMin1Max64' => [ 'type' => 'integer', 'min' => 1, 'max' => 64, ], '__integerMin1Max8' => [ 'type' => 'integer', 'min' => 1, 'max' => 8, ], '__integerMin22050Max48000' => [ 'type' => 'integer', 'min' => 22050, 'max' => 48000, ], '__integerMin24Max60000' => [ 'type' => 'integer', 'min' => 24, 'max' => 60000, ], '__integerMin25Max10000' => [ 'type' => 'integer', 'min' => 25, 'max' => 10000, ], '__integerMin25Max2000' => [ 'type' => 'integer', 'min' => 25, 'max' => 2000, ], '__integerMin2Max2147483647' => [ 'type' => 'integer', 'min' => 2, 'max' => 2147483647, ], '__integerMin2Max4096' => [ 'type' => 'integer', 'min' => 2, 'max' => 4096, ], '__integerMin32000Max192000' => [ 'type' => 'integer', 'min' => 32000, 'max' => 192000, ], '__integerMin32000Max3024000' => [ 'type' => 'integer', 'min' => 32000, 'max' => 3024000, ], '__integerMin32000Max384000' => [ 'type' => 'integer', 'min' => 32000, 'max' => 384000, ], '__integerMin32000Max48000' => [ 'type' => 'integer', 'min' => 32000, 'max' => 48000, ], '__integerMin32Max8182' => [ 'type' => 'integer', 'min' => 32, 'max' => 8182, ], '__integerMin32Max8192' => [ 'type' => 'integer', 'min' => 32, 'max' => 8192, ], '__integerMin384000Max1024000' => [ 'type' => 'integer', 'min' => 384000, 'max' => 1024000, ], '__integerMin3Max15' => [ 'type' => 'integer', 'min' => 3, 'max' => 15, ], '__integerMin48000Max48000' => [ 'type' => 'integer', 'min' => 48000, 'max' => 48000, ], '__integerMin4Max12' => [ 'type' => 'integer', 'min' => 4, 'max' => 12, ], '__integerMin50Max86400000' => [ 'type' => 'integer', 'min' => 50, 'max' => 86400000, ], '__integerMin6000Max1024000' => [ 'type' => 'integer', 'min' => 6000, 'max' => 1024000, ], '__integerMin64000Max640000' => [ 'type' => 'integer', 'min' => 64000, 'max' => 640000, ], '__integerMin8000Max192000' => [ 'type' => 'integer', 'min' => 8000, 'max' => 192000, ], '__integerMin8000Max96000' => [ 'type' => 'integer', 'min' => 8000, 'max' => 96000, ], '__integerMin8Max12' => [ 'type' => 'integer', 'min' => 8, 'max' => 12, ], '__integerMin8Max4096' => [ 'type' => 'integer', 'min' => 8, 'max' => 4096, ], '__integerMin90Max105' => [ 'type' => 'integer', 'min' => 90, 'max' => 105, ], '__integerMin920Max1023' => [ 'type' => 'integer', 'min' => 920, 'max' => 1023, ], '__integerMin96Max600' => [ 'type' => 'integer', 'min' => 96, 'max' => 600, ], '__integerMinNegative1000Max1000' => [ 'type' => 'integer', 'min' => -1000, 'max' => 1000, ], '__integerMinNegative180Max180' => [ 'type' => 'integer', 'min' => -180, 'max' => 180, ], '__integerMinNegative1Max10' => [ 'type' => 'integer', 'min' => -1, 'max' => 10, ], '__integerMinNegative1Max2147483647' => [ 'type' => 'integer', 'min' => -1, 'max' => 2147483647, ], '__integerMinNegative1Max3' => [ 'type' => 'integer', 'min' => -1, 'max' => 3, ], '__integerMinNegative2147483648Max2147483647' => [ 'type' => 'integer', 'min' => -2147483648, 'max' => 2147483647, ], '__integerMinNegative2Max3' => [ 'type' => 'integer', 'min' => -2, 'max' => 3, ], '__integerMinNegative50Max50' => [ 'type' => 'integer', 'min' => -50, 'max' => 50, ], '__integerMinNegative5Max10' => [ 'type' => 'integer', 'min' => -5, 'max' => 10, ], '__integerMinNegative60Max6' => [ 'type' => 'integer', 'min' => -60, 'max' => 6, ], '__integerMinNegative70Max0' => [ 'type' => 'integer', 'min' => -70, 'max' => 0, ], '__listOfAllowedRenditionSize' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedRenditionSize', ], ], '__listOfAudioChannelTag' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioChannelTag', ], ], '__listOfAudioDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioDescription', ], ], '__listOfAutomatedAbrRule' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutomatedAbrRule', ], ], '__listOfCaptionDescription' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaptionDescription', ], ], '__listOfCaptionDescriptionPreset' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaptionDescriptionPreset', ], ], '__listOfCmafAdditionalManifest' => [ 'type' => 'list', 'member' => [ 'shape' => 'CmafAdditionalManifest', ], ], '__listOfColorConversion3DLUTSetting' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColorConversion3DLUTSetting', ], ], '__listOfDashAdditionalManifest' => [ 'type' => 'list', 'member' => [ 'shape' => 'DashAdditionalManifest', ], ], '__listOfEndpoint' => [ 'type' => 'list', 'member' => [ 'shape' => 'Endpoint', ], ], '__listOfForceIncludeRenditionSize' => [ 'type' => 'list', 'member' => [ 'shape' => 'ForceIncludeRenditionSize', ], ], '__listOfHlsAdMarkers' => [ 'type' => 'list', 'member' => [ 'shape' => 'HlsAdMarkers', ], ], '__listOfHlsAdditionalManifest' => [ 'type' => 'list', 'member' => [ 'shape' => 'HlsAdditionalManifest', ], ], '__listOfHlsCaptionLanguageMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'HlsCaptionLanguageMapping', ], ], '__listOfHopDestination' => [ 'type' => 'list', 'member' => [ 'shape' => 'HopDestination', ], ], '__listOfId3Insertion' => [ 'type' => 'list', 'member' => [ 'shape' => 'Id3Insertion', ], ], '__listOfInput' => [ 'type' => 'list', 'member' => [ 'shape' => 'Input', ], ], '__listOfInputClipping' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputClipping', ], ], '__listOfInputTemplate' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputTemplate', ], ], '__listOfInsertableImage' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsertableImage', ], ], '__listOfJob' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], '__listOfJobEngineVersion' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobEngineVersion', ], ], '__listOfJobTemplate' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobTemplate', ], ], '__listOfMsSmoothAdditionalManifest' => [ 'type' => 'list', 'member' => [ 'shape' => 'MsSmoothAdditionalManifest', ], ], '__listOfOutput' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], ], '__listOfOutputChannelMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputChannelMapping', ], ], '__listOfOutputDetail' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputDetail', ], ], '__listOfOutputGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputGroup', ], ], '__listOfOutputGroupDetail' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputGroupDetail', ], ], '__listOfPreset' => [ 'type' => 'list', 'member' => [ 'shape' => 'Preset', ], ], '__listOfProbeInputFile' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProbeInputFile', ], ], '__listOfProbeResult' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProbeResult', ], ], '__listOfQueue' => [ 'type' => 'list', 'member' => [ 'shape' => 'Queue', ], ], '__listOfQueueTransition' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueueTransition', ], ], '__listOfServiceOverride' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceOverride', ], ], '__listOfTeletextPageType' => [ 'type' => 'list', 'member' => [ 'shape' => 'TeletextPageType', ], ], '__listOfTrack' => [ 'type' => 'list', 'member' => [ 'shape' => 'Track', ], ], '__listOfTrackMapping' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrackMapping', ], ], '__listOfVideoOverlay' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoOverlay', ], ], '__listOfVideoOverlayInputClipping' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoOverlayInputClipping', ], ], '__listOfVideoOverlayTransition' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoOverlayTransition', ], ], '__listOfWarningGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'WarningGroup', ], ], '__listOf__doubleMinNegative60Max6' => [ 'type' => 'list', 'member' => [ 'shape' => '__doubleMinNegative60Max6', ], ], '__listOf__integer' => [ 'type' => 'list', 'member' => [ 'shape' => '__integer', ], ], '__listOf__integerMin1Max2147483647' => [ 'type' => 'list', 'member' => [ 'shape' => '__integerMin1Max2147483647', ], ], '__listOf__integerMin32Max8182' => [ 'type' => 'list', 'member' => [ 'shape' => '__integerMin32Max8182', ], ], '__listOf__integerMinNegative60Max6' => [ 'type' => 'list', 'member' => [ 'shape' => '__integerMinNegative60Max6', ], ], '__listOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__listOf__stringMin1' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringMin1', ], ], '__listOf__stringMin36Max36Pattern09aFAF809aFAF409aFAF409aFAF409aFAF12' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringMin36Max36Pattern09aFAF809aFAF409aFAF409aFAF409aFAF12', ], ], '__listOf__stringPattern09aFAF809aFAF409aFAF409aFAF409aFAF12' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringPattern09aFAF809aFAF409aFAF409aFAF409aFAF12', ], ], '__listOf__stringPatternS3ASSETMAPXml' => [ 'type' => 'list', 'member' => [ 'shape' => '__stringPatternS3ASSETMAPXml', ], ], '__long' => [ 'type' => 'long', ], '__mapOfAudioSelector' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'AudioSelector', ], ], '__mapOfAudioSelectorGroup' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'AudioSelectorGroup', ], ], '__mapOfCaptionSelector' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'CaptionSelector', ], ], '__mapOfDynamicAudioSelector' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => 'DynamicAudioSelector', ], ], '__mapOf__string' => [ 'type' => 'map', 'key' => [ 'shape' => '__string', ], 'value' => [ 'shape' => '__string', ], ], '__string' => [ 'type' => 'string', ], '__stringMax1000' => [ 'type' => 'string', 'max' => 1000, ], '__stringMax2048' => [ 'type' => 'string', 'max' => 2048, ], '__stringMax2048PatternS3Https' => [ 'type' => 'string', 'max' => 2048, 'pattern' => '^s3://([^\\/]+\\/+)+((([^\\/]*)))|^https?://[^\\/].*[^&]$', ], '__stringMax256' => [ 'type' => 'string', 'max' => 256, ], '__stringMin0' => [ 'type' => 'string', 'min' => 0, ], '__stringMin1' => [ 'type' => 'string', 'min' => 1, ], '__stringMin11Max11Pattern01D20305D205D' => [ 'type' => 'string', 'min' => 11, 'max' => 11, 'pattern' => '^((([0-1]\\d)|(2[0-3]))(:[0-5]\\d){2}([:;][0-5]\\d))$', ], '__stringMin14PatternS3BmpBMPPngPNGHttpsBmpBMPPngPNG' => [ 'type' => 'string', 'min' => 14, 'pattern' => '^((s3://(.*?)\\.(bmp|BMP|png|PNG))|(https?://(.*?)\\.(bmp|BMP|png|PNG)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringMin14PatternS3BmpBMPPngPNGTgaTGAHttpsBmpBMPPngPNGTgaTGA' => [ 'type' => 'string', 'min' => 14, 'pattern' => '^((s3://(.*?)\\.(bmp|BMP|png|PNG|tga|TGA))|(https?://(.*?)\\.(bmp|BMP|png|PNG|tga|TGA)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringMin14PatternS3CubeCUBEHttpsCubeCUBE' => [ 'type' => 'string', 'min' => 14, 'pattern' => '^((s3://(.*?)\\.(cube|CUBE))|(https?://(.*?)\\.(cube|CUBE)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringMin14PatternS3Mov09PngHttpsMov09Png' => [ 'type' => 'string', 'min' => 14, 'pattern' => '^((s3://(.*)(\\.mov|[0-9]+\\.png))|(https?://(.*)(\\.mov|[0-9]+\\.png)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringMin14PatternS3SccSCCTtmlTTMLDfxpDFXPStlSTLSrtSRTXmlXMLSmiSMIVttVTTWebvttWEBVTTHttpsSccSCCTtmlTTMLDfxpDFXPStlSTLSrtSRTXmlXMLSmiSMIVttVTTWebvttWEBVTT' => [ 'type' => 'string', 'min' => 14, 'pattern' => '^((s3://(.*?)\\.(scc|SCC|ttml|TTML|dfxp|DFXP|stl|STL|srt|SRT|xml|XML|smi|SMI|vtt|VTT|webvtt|WEBVTT))|(https?://(.*?)\\.(scc|SCC|ttml|TTML|dfxp|DFXP|stl|STL|srt|SRT|xml|XML|smi|SMI|vtt|VTT|webvtt|WEBVTT)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringMin14PatternS3XmlXMLHttpsXmlXML' => [ 'type' => 'string', 'min' => 14, 'pattern' => '^((s3://(.*?)\\.(xml|XML))|(https?://(.*?)\\.(xml|XML)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringMin16Max24PatternAZaZ0922AZaZ0916' => [ 'type' => 'string', 'min' => 16, 'max' => 24, 'pattern' => '^[A-Za-z0-9+\\/]{22}==$|^[A-Za-z0-9+\\/]{16}$', ], '__stringMin1Max100000' => [ 'type' => 'string', 'min' => 1, 'max' => 100000, ], '__stringMin1Max20' => [ 'type' => 'string', 'min' => 1, 'max' => 20, ], '__stringMin1Max2048PatternArnAZSecretsmanagerWD12SecretAZAZ09' => [ 'type' => 'string', 'min' => 1, 'max' => 2048, 'pattern' => '^(arn:[a-z-]+:secretsmanager:[\\w-]+:\\d{12}:secret:)?[a-zA-Z0-9_\\/_+=.@-]*$', ], '__stringMin1Max256' => [ 'type' => 'string', 'min' => 1, 'max' => 256, ], '__stringMin1Max50' => [ 'type' => 'string', 'min' => 1, 'max' => 50, ], '__stringMin1Max50PatternAZAZ09' => [ 'type' => 'string', 'min' => 1, 'max' => 50, 'pattern' => '^[a-zA-Z0-9_\\/_+=.@-]*$', ], '__stringMin24Max512PatternAZaZ0902' => [ 'type' => 'string', 'min' => 24, 'max' => 512, 'pattern' => '^[A-Za-z0-9+\\/]+={0,2}$', ], '__stringMin32Max32Pattern09aFAF32' => [ 'type' => 'string', 'min' => 32, 'max' => 32, 'pattern' => '^[0-9a-fA-F]{32}$', ], '__stringMin36Max36Pattern09aFAF809aFAF409aFAF409aFAF409aFAF12' => [ 'type' => 'string', 'min' => 36, 'max' => 36, 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], '__stringMin3Max3Pattern1809aFAF09aEAE' => [ 'type' => 'string', 'min' => 3, 'max' => 3, 'pattern' => '^[1-8][0-9a-fA-F][0-9a-eA-E]$', ], '__stringMin3Max3PatternAZaZ3' => [ 'type' => 'string', 'min' => 3, 'max' => 3, 'pattern' => '^[A-Za-z]{3}$', ], '__stringMin6Max8Pattern09aFAF609aFAF2' => [ 'type' => 'string', 'min' => 6, 'max' => 8, 'pattern' => '^[0-9a-fA-F]{6}([0-9a-fA-F]{2})?$', ], '__stringMin9Max19PatternAZ26EastWestCentralNorthSouthEastWest1912' => [ 'type' => 'string', 'min' => 9, 'max' => 19, 'pattern' => '^[a-z-]{2,6}-(east|west|central|((north|south)(east|west)?))-[1-9]{1,2}$', ], '__stringPattern' => [ 'type' => 'string', 'pattern' => '^[ -~]+$', ], '__stringPattern010920405090509092' => [ 'type' => 'string', 'pattern' => '^([01][0-9]|2[0-4]):[0-5][0-9]:[0-5][0-9][:;][0-9]{2}$', ], '__stringPattern010920405090509092090909' => [ 'type' => 'string', 'pattern' => '^([01][0-9]|2[0-4]):[0-5][0-9]:[0-5][0-9][:;][0-9]{2}(@[0-9]+(\\.[0-9]+)?(:[0-9]+)?)?$', ], '__stringPattern01D20305D205D' => [ 'type' => 'string', 'pattern' => '^((([0-1]\\d)|(2[0-3]))(:[0-5]\\d){2}([:;][0-5]\\d))$', ], '__stringPattern0940191020191209301' => [ 'type' => 'string', 'pattern' => '^([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$', ], '__stringPattern09aFAF809aFAF409aFAF409aFAF409aFAF12' => [ 'type' => 'string', 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], '__stringPattern0xAFaF0908190908' => [ 'type' => 'string', 'pattern' => '(^0x[A-Fa-f0-9]{0,8}$|^[1-9][0-9]{0,8}$)', ], '__stringPatternAZaZ0902' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9+\\/]+={0,2}$', ], '__stringPatternAZaZ0932' => [ 'type' => 'string', 'pattern' => '^[A-Za-z0-9]{32}$', ], '__stringPatternAZaZ23AZaZ' => [ 'type' => 'string', 'pattern' => '^[A-Za-z]{2,3}(-[A-Za-z-]+)?$', ], '__stringPatternAZaZ23AZaZ09' => [ 'type' => 'string', 'pattern' => '^[A-Za-z]{2,3}(-[A-Za-z0-9-]+)?$', ], '__stringPatternArnAwsUsGovAcm' => [ 'type' => 'string', 'pattern' => '^arn:aws(-us-gov)?:acm:', ], '__stringPatternArnAwsUsGovCnKmsAZ26EastWestCentralNorthSouthEastWest1912D12KeyAFAF098AFAF094AFAF094AFAF094AFAF0912MrkAFAF0932' => [ 'type' => 'string', 'pattern' => '^arn:aws(-us-gov|-cn)?:kms:[a-z-]{2,6}-(east|west|central|((north|south)(east|west)?))-[1-9]{1,2}:\\d{12}:key/([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}|mrk-[a-fA-F0-9]{32})$', ], '__stringPatternDD' => [ 'type' => 'string', 'pattern' => '^(\\d+(\\/\\d+)*)$', ], '__stringPatternHttps' => [ 'type' => 'string', 'pattern' => '^https:\\/\\/', ], '__stringPatternHttpsD' => [ 'type' => 'string', 'pattern' => '^https:\\/\\/[^:@\\/]*(:\\d*)?(\\/.*)?$', ], '__stringPatternHttpsKantarmedia' => [ 'type' => 'string', 'pattern' => '^https:\\/\\/.*.kantarmedia.*$', ], '__stringPatternIdentityAZaZ26AZaZ09163' => [ 'type' => 'string', 'pattern' => '^(identity|[A-Za-z]{2,6}(\\.[A-Za-z0-9-]{1,63})+)$', ], '__stringPatternS3' => [ 'type' => 'string', 'pattern' => '^s3:\\/\\/', ], '__stringPatternS3ASSETMAPXml' => [ 'type' => 'string', 'pattern' => '^s3:\\/\\/.*\\/(ASSETMAP.xml)?$', ], '__stringPatternS3Https' => [ 'type' => 'string', 'pattern' => '^s3://([^\\/]+\\/+)+((([^\\/]*)))|^https?://[^\\/].*[^&]$', ], '__stringPatternS3TtfHttpsTtf' => [ 'type' => 'string', 'pattern' => '^((s3://(.*?)\\.(ttf))|(https?://(.*?)\\.(ttf)(\\?([^&=]+=[^&]+&)*[^&=]+=[^&]+)?))$', ], '__stringPatternSNManifestConfirmConditionNotificationNS' => [ 'type' => 'string', 'pattern' => '^\\s*<(.|\\n)*ManifestConfirmConditionNotification(.|\\n)*>\\s*$', ], '__stringPatternSNSignalProcessingNotificationNS' => [ 'type' => 'string', 'pattern' => '^\\s*<(.|\\n)*SignalProcessingNotification(.|\\n)*>\\s*$', ], '__stringPatternW' => [ 'type' => 'string', 'pattern' => '^[\\w-]+$', ], '__stringPatternWS' => [ 'type' => 'string', 'pattern' => '^[\\w\\s]*$', ], '__timestampIso8601' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], ],];
