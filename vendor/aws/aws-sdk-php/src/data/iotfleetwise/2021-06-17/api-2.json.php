<?php
// This file was auto-generated from sdk-root/src/data/iotfleetwise/2021-06-17/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-06-17', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'iotfleetwise', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS IoT FleetWise', 'serviceId' => 'IoTFleetWise', 'signatureVersion' => 'v4', 'signingName' => 'iotfleetwise', 'targetPrefix' => 'IoTAutobahnControlPlane', 'uid' => 'iotfleetwise-2021-06-17', ], 'operations' => [ 'AssociateVehicleFleet' => [ 'name' => 'AssociateVehicleFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateVehicleFleetRequest', ], 'output' => [ 'shape' => 'AssociateVehicleFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchCreateVehicle' => [ 'name' => 'BatchCreateVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreateVehicleRequest', ], 'output' => [ 'shape' => 'BatchCreateVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'BatchUpdateVehicle' => [ 'name' => 'BatchUpdateVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchUpdateVehicleRequest', ], 'output' => [ 'shape' => 'BatchUpdateVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateCampaign' => [ 'name' => 'CreateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCampaignRequest', ], 'output' => [ 'shape' => 'CreateCampaignResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateDecoderManifest' => [ 'name' => 'CreateDecoderManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDecoderManifestRequest', ], 'output' => [ 'shape' => 'CreateDecoderManifestResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DecoderManifestValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFleetRequest', ], 'output' => [ 'shape' => 'CreateFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateModelManifest' => [ 'name' => 'CreateModelManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateModelManifestRequest', ], 'output' => [ 'shape' => 'CreateModelManifestResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateSignalCatalog' => [ 'name' => 'CreateSignalCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSignalCatalogRequest', ], 'output' => [ 'shape' => 'CreateSignalCatalogResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNodeException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateStateTemplate' => [ 'name' => 'CreateStateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStateTemplateRequest', ], 'output' => [ 'shape' => 'CreateStateTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateVehicle' => [ 'name' => 'CreateVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateVehicleRequest', ], 'output' => [ 'shape' => 'CreateVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteCampaign' => [ 'name' => 'DeleteCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCampaignRequest', ], 'output' => [ 'shape' => 'DeleteCampaignResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteDecoderManifest' => [ 'name' => 'DeleteDecoderManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDecoderManifestRequest', ], 'output' => [ 'shape' => 'DeleteDecoderManifestResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFleetRequest', ], 'output' => [ 'shape' => 'DeleteFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteModelManifest' => [ 'name' => 'DeleteModelManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteModelManifestRequest', ], 'output' => [ 'shape' => 'DeleteModelManifestResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteSignalCatalog' => [ 'name' => 'DeleteSignalCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSignalCatalogRequest', ], 'output' => [ 'shape' => 'DeleteSignalCatalogResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteStateTemplate' => [ 'name' => 'DeleteStateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStateTemplateRequest', ], 'output' => [ 'shape' => 'DeleteStateTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteVehicle' => [ 'name' => 'DeleteVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteVehicleRequest', ], 'output' => [ 'shape' => 'DeleteVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DisassociateVehicleFleet' => [ 'name' => 'DisassociateVehicleFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateVehicleFleetRequest', ], 'output' => [ 'shape' => 'DisassociateVehicleFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCampaign' => [ 'name' => 'GetCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCampaignRequest', ], 'output' => [ 'shape' => 'GetCampaignResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDecoderManifest' => [ 'name' => 'GetDecoderManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDecoderManifestRequest', ], 'output' => [ 'shape' => 'GetDecoderManifestResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetEncryptionConfiguration' => [ 'name' => 'GetEncryptionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'GetEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetFleet' => [ 'name' => 'GetFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetFleetRequest', ], 'output' => [ 'shape' => 'GetFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetLoggingOptions' => [ 'name' => 'GetLoggingOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoggingOptionsRequest', ], 'output' => [ 'shape' => 'GetLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetModelManifest' => [ 'name' => 'GetModelManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetModelManifestRequest', ], 'output' => [ 'shape' => 'GetModelManifestResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetRegisterAccountStatus' => [ 'name' => 'GetRegisterAccountStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegisterAccountStatusRequest', ], 'output' => [ 'shape' => 'GetRegisterAccountStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetSignalCatalog' => [ 'name' => 'GetSignalCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSignalCatalogRequest', ], 'output' => [ 'shape' => 'GetSignalCatalogResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStateTemplate' => [ 'name' => 'GetStateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStateTemplateRequest', ], 'output' => [ 'shape' => 'GetStateTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetVehicle' => [ 'name' => 'GetVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetVehicleRequest', ], 'output' => [ 'shape' => 'GetVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetVehicleStatus' => [ 'name' => 'GetVehicleStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetVehicleStatusRequest', ], 'output' => [ 'shape' => 'GetVehicleStatusResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ImportDecoderManifest' => [ 'name' => 'ImportDecoderManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportDecoderManifestRequest', ], 'output' => [ 'shape' => 'ImportDecoderManifestResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'DecoderManifestValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ImportSignalCatalog' => [ 'name' => 'ImportSignalCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportSignalCatalogRequest', ], 'output' => [ 'shape' => 'ImportSignalCatalogResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'ListCampaigns' => [ 'name' => 'ListCampaigns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCampaignsRequest', ], 'output' => [ 'shape' => 'ListCampaignsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDecoderManifestNetworkInterfaces' => [ 'name' => 'ListDecoderManifestNetworkInterfaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDecoderManifestNetworkInterfacesRequest', ], 'output' => [ 'shape' => 'ListDecoderManifestNetworkInterfacesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDecoderManifestSignals' => [ 'name' => 'ListDecoderManifestSignals', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDecoderManifestSignalsRequest', ], 'output' => [ 'shape' => 'ListDecoderManifestSignalsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDecoderManifests' => [ 'name' => 'ListDecoderManifests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDecoderManifestsRequest', ], 'output' => [ 'shape' => 'ListDecoderManifestsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFleets' => [ 'name' => 'ListFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFleetsRequest', ], 'output' => [ 'shape' => 'ListFleetsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListFleetsForVehicle' => [ 'name' => 'ListFleetsForVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFleetsForVehicleRequest', ], 'output' => [ 'shape' => 'ListFleetsForVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListModelManifestNodes' => [ 'name' => 'ListModelManifestNodes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelManifestNodesRequest', ], 'output' => [ 'shape' => 'ListModelManifestNodesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListModelManifests' => [ 'name' => 'ListModelManifests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListModelManifestsRequest', ], 'output' => [ 'shape' => 'ListModelManifestsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSignalCatalogNodes' => [ 'name' => 'ListSignalCatalogNodes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSignalCatalogNodesRequest', ], 'output' => [ 'shape' => 'ListSignalCatalogNodesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListSignalCatalogs' => [ 'name' => 'ListSignalCatalogs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSignalCatalogsRequest', ], 'output' => [ 'shape' => 'ListSignalCatalogsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListStateTemplates' => [ 'name' => 'ListStateTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStateTemplatesRequest', ], 'output' => [ 'shape' => 'ListStateTemplatesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListVehicles' => [ 'name' => 'ListVehicles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVehiclesRequest', ], 'output' => [ 'shape' => 'ListVehiclesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListVehiclesInFleet' => [ 'name' => 'ListVehiclesInFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListVehiclesInFleetRequest', ], 'output' => [ 'shape' => 'ListVehiclesInFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutEncryptionConfiguration' => [ 'name' => 'PutEncryptionConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutEncryptionConfigurationRequest', ], 'output' => [ 'shape' => 'PutEncryptionConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutLoggingOptions' => [ 'name' => 'PutLoggingOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutLoggingOptionsRequest', ], 'output' => [ 'shape' => 'PutLoggingOptionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'RegisterAccount' => [ 'name' => 'RegisterAccount', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterAccountRequest', ], 'output' => [ 'shape' => 'RegisterAccountResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaign' => [ 'name' => 'UpdateCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCampaignRequest', ], 'output' => [ 'shape' => 'UpdateCampaignResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateDecoderManifest' => [ 'name' => 'UpdateDecoderManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDecoderManifestRequest', ], 'output' => [ 'shape' => 'UpdateDecoderManifestResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DecoderManifestValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateFleet' => [ 'name' => 'UpdateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFleetRequest', ], 'output' => [ 'shape' => 'UpdateFleetResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateModelManifest' => [ 'name' => 'UpdateModelManifest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateModelManifestRequest', ], 'output' => [ 'shape' => 'UpdateModelManifestResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateSignalCatalog' => [ 'name' => 'UpdateSignalCatalog', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSignalCatalogRequest', ], 'output' => [ 'shape' => 'UpdateSignalCatalogResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidNodeException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateStateTemplate' => [ 'name' => 'UpdateStateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStateTemplateRequest', ], 'output' => [ 'shape' => 'UpdateStateTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InvalidSignalsException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateVehicle' => [ 'name' => 'UpdateVehicle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateVehicleRequest', ], 'output' => [ 'shape' => 'UpdateVehicleResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'Actuator' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', 'dataType', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'NodeDataType', ], 'description' => [ 'shape' => 'description', ], 'unit' => [ 'shape' => 'string', ], 'allowedValues' => [ 'shape' => 'listOfStrings', ], 'min' => [ 'shape' => 'double', ], 'max' => [ 'shape' => 'double', ], 'assignedValue' => [ 'shape' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'assignedValue is no longer in use', ], 'deprecationMessage' => [ 'shape' => 'message', ], 'comment' => [ 'shape' => 'message', ], 'structFullyQualifiedName' => [ 'shape' => 'NodePath', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:.*', ], 'AssociateVehicleFleetRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', 'fleetId', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'fleetId' => [ 'shape' => 'fleetId', ], ], ], 'AssociateVehicleFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'Attribute' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', 'dataType', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'NodeDataType', ], 'description' => [ 'shape' => 'description', ], 'unit' => [ 'shape' => 'string', ], 'allowedValues' => [ 'shape' => 'listOfStrings', ], 'min' => [ 'shape' => 'double', ], 'max' => [ 'shape' => 'double', ], 'assignedValue' => [ 'shape' => 'string', 'deprecated' => true, 'deprecatedMessage' => 'assignedValue is no longer in use', ], 'defaultValue' => [ 'shape' => 'string', ], 'deprecationMessage' => [ 'shape' => 'message', ], 'comment' => [ 'shape' => 'message', ], ], ], 'BatchCreateVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicles', ], 'members' => [ 'vehicles' => [ 'shape' => 'createVehicleRequestItems', ], ], ], 'BatchCreateVehicleResponse' => [ 'type' => 'structure', 'members' => [ 'vehicles' => [ 'shape' => 'createVehicleResponses', ], 'errors' => [ 'shape' => 'createVehicleErrors', ], ], ], 'BatchUpdateVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicles', ], 'members' => [ 'vehicles' => [ 'shape' => 'updateVehicleRequestItems', ], ], ], 'BatchUpdateVehicleResponse' => [ 'type' => 'structure', 'members' => [ 'vehicles' => [ 'shape' => 'updateVehicleResponseItems', ], 'errors' => [ 'shape' => 'updateVehicleErrors', ], ], ], 'Branch' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'description', ], 'deprecationMessage' => [ 'shape' => 'message', ], 'comment' => [ 'shape' => 'message', ], ], ], 'CampaignStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'WAITING_FOR_APPROVAL', 'RUNNING', 'SUSPENDED', ], ], 'CampaignSummary' => [ 'type' => 'structure', 'required' => [ 'creationTime', 'lastModificationTime', ], 'members' => [ 'arn' => [ 'shape' => 'campaignArn', ], 'name' => [ 'shape' => 'campaignName', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'targetArn' => [ 'shape' => 'arn', ], 'status' => [ 'shape' => 'CampaignStatus', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'CanDbcDefinition' => [ 'type' => 'structure', 'required' => [ 'networkInterface', 'canDbcFiles', ], 'members' => [ 'networkInterface' => [ 'shape' => 'InterfaceId', ], 'canDbcFiles' => [ 'shape' => 'NetworkFilesList', ], 'signalsMap' => [ 'shape' => 'ModelSignalsMap', ], ], ], 'CanInterface' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'CanInterfaceName', ], 'protocolName' => [ 'shape' => 'ProtocolName', ], 'protocolVersion' => [ 'shape' => 'ProtocolVersion', ], ], ], 'CanInterfaceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CanSignal' => [ 'type' => 'structure', 'required' => [ 'messageId', 'isBigEndian', 'isSigned', 'startBit', 'offset', 'factor', 'length', ], 'members' => [ 'messageId' => [ 'shape' => 'nonNegativeInteger', ], 'isBigEndian' => [ 'shape' => 'PrimitiveBoolean', ], 'isSigned' => [ 'shape' => 'PrimitiveBoolean', ], 'startBit' => [ 'shape' => 'nonNegativeInteger', ], 'offset' => [ 'shape' => 'double', ], 'factor' => [ 'shape' => 'double', ], 'length' => [ 'shape' => 'nonNegativeInteger', ], 'name' => [ 'shape' => 'CanSignalName', ], ], ], 'CanSignalName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CloudWatchLogDeliveryOptions' => [ 'type' => 'structure', 'required' => [ 'logType', ], 'members' => [ 'logType' => [ 'shape' => 'LogType', ], 'logGroupName' => [ 'shape' => 'CloudWatchLogGroupName', ], ], ], 'CloudWatchLogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_\\/#A-Za-z0-9]+', ], 'CollectionScheme' => [ 'type' => 'structure', 'members' => [ 'timeBasedCollectionScheme' => [ 'shape' => 'TimeBasedCollectionScheme', ], 'conditionBasedCollectionScheme' => [ 'shape' => 'ConditionBasedCollectionScheme', ], ], 'union' => true, ], 'Compression' => [ 'type' => 'string', 'enum' => [ 'OFF', 'SNAPPY', ], ], 'ConditionBasedCollectionScheme' => [ 'type' => 'structure', 'required' => [ 'expression', ], 'members' => [ 'expression' => [ 'shape' => 'eventExpression', ], 'minimumTriggerIntervalMs' => [ 'shape' => 'uint32', ], 'triggerMode' => [ 'shape' => 'TriggerMode', ], 'conditionLanguageVersion' => [ 'shape' => 'languageVersion', ], ], ], 'ConditionBasedSignalFetchConfig' => [ 'type' => 'structure', 'required' => [ 'conditionExpression', 'triggerMode', ], 'members' => [ 'conditionExpression' => [ 'shape' => 'fetchConfigEventExpression', ], 'triggerMode' => [ 'shape' => 'TriggerMode', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resource', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'string', ], 'resource' => [ 'shape' => 'string', ], 'resourceType' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'CreateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'signalCatalogArn', 'targetArn', 'collectionScheme', ], 'members' => [ 'name' => [ 'shape' => 'campaignName', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'targetArn' => [ 'shape' => 'arn', ], 'startTime' => [ 'shape' => 'timestamp', ], 'expiryTime' => [ 'shape' => 'timestamp', ], 'postTriggerCollectionDuration' => [ 'shape' => 'uint32', ], 'diagnosticsMode' => [ 'shape' => 'DiagnosticsMode', ], 'spoolingMode' => [ 'shape' => 'SpoolingMode', ], 'compression' => [ 'shape' => 'Compression', ], 'priority' => [ 'shape' => 'priority', 'deprecated' => true, 'deprecatedMessage' => 'priority is no longer used or needed as input', ], 'signalsToCollect' => [ 'shape' => 'SignalInformationList', ], 'collectionScheme' => [ 'shape' => 'CollectionScheme', ], 'dataExtraDimensions' => [ 'shape' => 'DataExtraDimensionNodePathList', ], 'tags' => [ 'shape' => 'TagList', ], 'dataDestinationConfigs' => [ 'shape' => 'DataDestinationConfigs', ], 'dataPartitions' => [ 'shape' => 'DataPartitions', ], 'signalsToFetch' => [ 'shape' => 'SignalFetchInformationList', ], ], ], 'CreateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'campaignName', ], 'arn' => [ 'shape' => 'campaignArn', ], ], ], 'CreateDecoderManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'modelManifestArn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'signalDecoders' => [ 'shape' => 'SignalDecoders', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'defaultForUnmappedSignals' => [ 'shape' => 'DefaultForUnmappedSignalsType', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDecoderManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'CreateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleetId', 'signalCatalogArn', ], 'members' => [ 'fleetId' => [ 'shape' => 'fleetId', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFleetResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', ], 'members' => [ 'id' => [ 'shape' => 'fleetId', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'CreateModelManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'nodes', 'signalCatalogArn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'nodes' => [ 'shape' => 'listOfStrings', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateModelManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'CreateSignalCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'nodes' => [ 'shape' => 'Nodes', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateSignalCatalogResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'CreateStateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'signalCatalogArn', 'stateTemplateProperties', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'stateTemplateProperties' => [ 'shape' => 'StateTemplateProperties', ], 'dataExtraDimensions' => [ 'shape' => 'StateTemplateDataExtraDimensionNodePathList', ], 'metadataExtraDimensions' => [ 'shape' => 'StateTemplateMetadataExtraDimensionNodePathList', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateStateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'id' => [ 'shape' => 'ResourceUniqueId', ], ], ], 'CreateVehicleError' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'code' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], ], ], 'CreateVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', 'modelManifestArn', 'decoderManifestArn', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'decoderManifestArn' => [ 'shape' => 'arn', ], 'attributes' => [ 'shape' => 'attributesMap', ], 'associationBehavior' => [ 'shape' => 'VehicleAssociationBehavior', ], 'tags' => [ 'shape' => 'TagList', ], 'stateTemplates' => [ 'shape' => 'StateTemplateAssociations', ], ], ], 'CreateVehicleRequestItem' => [ 'type' => 'structure', 'required' => [ 'vehicleName', 'modelManifestArn', 'decoderManifestArn', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'decoderManifestArn' => [ 'shape' => 'arn', ], 'attributes' => [ 'shape' => 'attributesMap', ], 'associationBehavior' => [ 'shape' => 'VehicleAssociationBehavior', ], 'tags' => [ 'shape' => 'TagList', ], 'stateTemplates' => [ 'shape' => 'StateTemplateAssociations', ], ], ], 'CreateVehicleResponse' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], 'thingArn' => [ 'shape' => 'arn', ], ], ], 'CreateVehicleResponseItem' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], 'thingArn' => [ 'shape' => 'arn', ], ], ], 'CustomDecodingId' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '(?!.*\\.\\.)[a-zA-Z0-9_\\-#:.]+', ], 'CustomDecodingInterface' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'CustomDecodingSignalInterfaceName', ], ], ], 'CustomDecodingSignal' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CustomDecodingId', ], ], ], 'CustomDecodingSignalInterfaceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z\\d\\-_:]+', ], 'CustomProperty' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', 'dataType', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'NodeDataType', ], 'dataEncoding' => [ 'shape' => 'NodeDataEncoding', ], 'description' => [ 'shape' => 'description', ], 'deprecationMessage' => [ 'shape' => 'message', ], 'comment' => [ 'shape' => 'message', ], 'structFullyQualifiedName' => [ 'shape' => 'NodePath', ], ], ], 'CustomStruct' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'description', ], 'deprecationMessage' => [ 'shape' => 'message', ], 'comment' => [ 'shape' => 'message', ], ], ], 'DataDestinationConfig' => [ 'type' => 'structure', 'members' => [ 's3Config' => [ 'shape' => 'S3Config', ], 'timestreamConfig' => [ 'shape' => 'TimestreamConfig', ], 'mqttTopicConfig' => [ 'shape' => 'MqttTopicConfig', ], ], 'union' => true, ], 'DataDestinationConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataDestinationConfig', ], 'max' => 3, 'min' => 1, ], 'DataExtraDimensionNodePathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodePath', ], 'max' => 5, 'min' => 0, 'sensitive' => true, ], 'DataFormat' => [ 'type' => 'string', 'enum' => [ 'JSON', 'PARQUET', ], ], 'DataPartition' => [ 'type' => 'structure', 'required' => [ 'id', 'storageOptions', ], 'members' => [ 'id' => [ 'shape' => 'DataPartitionId', ], 'storageOptions' => [ 'shape' => 'DataPartitionStorageOptions', ], 'uploadOptions' => [ 'shape' => 'DataPartitionUploadOptions', ], ], ], 'DataPartitionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9]+', ], 'DataPartitionStorageOptions' => [ 'type' => 'structure', 'required' => [ 'maximumSize', 'storageLocation', 'minimumTimeToLive', ], 'members' => [ 'maximumSize' => [ 'shape' => 'StorageMaximumSize', ], 'storageLocation' => [ 'shape' => 'StorageLocation', ], 'minimumTimeToLive' => [ 'shape' => 'StorageMinimumTimeToLive', ], ], ], 'DataPartitionUploadOptions' => [ 'type' => 'structure', 'required' => [ 'expression', ], 'members' => [ 'expression' => [ 'shape' => 'eventExpression', ], 'conditionLanguageVersion' => [ 'shape' => 'languageVersion', ], ], ], 'DataPartitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataPartition', ], 'max' => 20, 'min' => 1, ], 'DecoderManifestSummary' => [ 'type' => 'structure', 'required' => [ 'creationTime', 'lastModificationTime', ], 'members' => [ 'name' => [ 'shape' => 'string', ], 'arn' => [ 'shape' => 'arn', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'status' => [ 'shape' => 'ManifestStatus', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], 'message' => [ 'shape' => 'message', ], ], ], 'DecoderManifestValidationException' => [ 'type' => 'structure', 'members' => [ 'invalidSignals' => [ 'shape' => 'InvalidSignalDecoders', ], 'invalidNetworkInterfaces' => [ 'shape' => 'InvalidNetworkInterfaces', ], 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'DefaultForUnmappedSignalsType' => [ 'type' => 'string', 'enum' => [ 'CUSTOM_DECODING', ], ], 'DeleteCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'campaignName', ], ], ], 'DeleteCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'campaignName', ], 'arn' => [ 'shape' => 'campaignArn', ], ], ], 'DeleteDecoderManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], ], ], 'DeleteDecoderManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'DeleteFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleetId', ], 'members' => [ 'fleetId' => [ 'shape' => 'fleetId', ], ], ], 'DeleteFleetResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'fleetId', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'DeleteModelManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], ], ], 'DeleteModelManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'DeleteSignalCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], ], ], 'DeleteSignalCatalogResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'DeleteStateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'DeleteStateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'id' => [ 'shape' => 'ResourceUniqueId', ], ], ], 'DeleteVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], ], ], 'DeleteVehicleResponse' => [ 'type' => 'structure', 'required' => [ 'vehicleName', 'arn', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'DiagnosticsMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'SEND_ACTIVE_DTCS', ], ], 'DisassociateVehicleFleetRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', 'fleetId', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'fleetId' => [ 'shape' => 'fleetId', ], ], ], 'DisassociateVehicleFleetResponse' => [ 'type' => 'structure', 'members' => [], ], 'EncryptionStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'SUCCESS', 'FAILURE', ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'KMS_BASED_ENCRYPTION', 'FLEETWISE_DEFAULT_ENCRYPTION', ], ], 'EventExpressionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'actionEventExpression', ], 'max' => 2, 'min' => 1, ], 'FleetSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'signalCatalogArn', 'creationTime', ], 'members' => [ 'id' => [ 'shape' => 'fleetId', ], 'arn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'FormattedVss' => [ 'type' => 'structure', 'members' => [ 'vssJson' => [ 'shape' => 'String', ], ], 'union' => true, ], 'Fqns' => [ 'type' => 'list', 'member' => [ 'shape' => 'FullyQualifiedName', ], 'max' => 500, 'min' => 1, ], 'FullyQualifiedName' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'GetCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'campaignName', ], ], ], 'GetCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'campaignName', ], 'arn' => [ 'shape' => 'campaignArn', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'targetArn' => [ 'shape' => 'arn', ], 'status' => [ 'shape' => 'CampaignStatus', ], 'startTime' => [ 'shape' => 'timestamp', ], 'expiryTime' => [ 'shape' => 'timestamp', ], 'postTriggerCollectionDuration' => [ 'shape' => 'uint32', ], 'diagnosticsMode' => [ 'shape' => 'DiagnosticsMode', ], 'spoolingMode' => [ 'shape' => 'SpoolingMode', ], 'compression' => [ 'shape' => 'Compression', ], 'priority' => [ 'shape' => 'priority', ], 'signalsToCollect' => [ 'shape' => 'SignalInformationList', ], 'collectionScheme' => [ 'shape' => 'CollectionScheme', ], 'dataExtraDimensions' => [ 'shape' => 'DataExtraDimensionNodePathList', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], 'dataDestinationConfigs' => [ 'shape' => 'DataDestinationConfigs', ], 'dataPartitions' => [ 'shape' => 'DataPartitions', ], 'signalsToFetch' => [ 'shape' => 'SignalFetchInformationList', ], ], ], 'GetDecoderManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], ], ], 'GetDecoderManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', 'creationTime', 'lastModificationTime', ], 'members' => [ 'name' => [ 'shape' => 'string', ], 'arn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'status' => [ 'shape' => 'ManifestStatus', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], 'message' => [ 'shape' => 'message', ], ], ], 'GetEncryptionConfigurationRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionStatus', 'encryptionType', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'String', ], 'encryptionStatus' => [ 'shape' => 'EncryptionStatus', ], 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'errorMessage' => [ 'shape' => 'errorMessage', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'GetFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleetId', ], 'members' => [ 'fleetId' => [ 'shape' => 'fleetId', ], ], ], 'GetFleetResponse' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'signalCatalogArn', 'creationTime', 'lastModificationTime', ], 'members' => [ 'id' => [ 'shape' => 'fleetId', ], 'arn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'GetLoggingOptionsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetLoggingOptionsResponse' => [ 'type' => 'structure', 'required' => [ 'cloudWatchLogDelivery', ], 'members' => [ 'cloudWatchLogDelivery' => [ 'shape' => 'CloudWatchLogDeliveryOptions', ], ], ], 'GetModelManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], ], ], 'GetModelManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', 'creationTime', 'lastModificationTime', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'status' => [ 'shape' => 'ManifestStatus', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'GetRegisterAccountStatusRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetRegisterAccountStatusResponse' => [ 'type' => 'structure', 'required' => [ 'customerAccountId', 'accountStatus', 'iamRegistrationResponse', 'creationTime', 'lastModificationTime', ], 'members' => [ 'customerAccountId' => [ 'shape' => 'customerAccountId', ], 'accountStatus' => [ 'shape' => 'RegistrationStatus', ], 'timestreamRegistrationResponse' => [ 'shape' => 'TimestreamRegistrationResponse', ], 'iamRegistrationResponse' => [ 'shape' => 'IamRegistrationResponse', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'GetSignalCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], ], ], 'GetSignalCatalogResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', 'creationTime', 'lastModificationTime', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'nodeCounts' => [ 'shape' => 'NodeCounts', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'GetStateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceIdentifier', ], ], ], 'GetStateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'stateTemplateProperties' => [ 'shape' => 'StateTemplateProperties', ], 'dataExtraDimensions' => [ 'shape' => 'StateTemplateDataExtraDimensionNodePathList', ], 'metadataExtraDimensions' => [ 'shape' => 'StateTemplateMetadataExtraDimensionNodePathList', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], 'id' => [ 'shape' => 'ResourceUniqueId', ], ], ], 'GetVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], ], ], 'GetVehicleResponse' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'decoderManifestArn' => [ 'shape' => 'arn', ], 'attributes' => [ 'shape' => 'attributesMap', ], 'stateTemplates' => [ 'shape' => 'StateTemplateAssociations', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'GetVehicleStatusRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', ], 'members' => [ 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'vehicleName' => [ 'shape' => 'vehicleName', ], ], ], 'GetVehicleStatusResponse' => [ 'type' => 'structure', 'members' => [ 'campaigns' => [ 'shape' => 'VehicleStatusList', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'IAMRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws[a-zA-Z0-9-]*):iam::(\\d{12})?:(role((\\u002F)|(\\u002F[\\u0021-\\u007F]+\\u002F))[\\w+=,.@-]+)', ], 'IamRegistrationResponse' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'registrationStatus', ], 'members' => [ 'roleArn' => [ 'shape' => 'arn', ], 'registrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'errorMessage' => [ 'shape' => 'errorMessage', ], ], ], 'IamResources' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'roleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'ImportDecoderManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'networkFileDefinitions', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'networkFileDefinitions' => [ 'shape' => 'NetworkFileDefinitions', ], ], ], 'ImportDecoderManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'ImportSignalCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'vss' => [ 'shape' => 'FormattedVss', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'ImportSignalCatalogResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'InterfaceId' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'InterfaceIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'InterfaceId', ], 'max' => 500, 'min' => 1, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', ], ], 'exception' => true, 'fault' => true, ], 'InvalidNetworkInterface' => [ 'type' => 'structure', 'members' => [ 'interfaceId' => [ 'shape' => 'InterfaceId', ], 'reason' => [ 'shape' => 'NetworkInterfaceFailureReason', ], ], ], 'InvalidNetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvalidNetworkInterface', ], ], 'InvalidNodeException' => [ 'type' => 'structure', 'members' => [ 'invalidNodes' => [ 'shape' => 'Nodes', ], 'reason' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'InvalidSignal' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FullyQualifiedName', ], 'reason' => [ 'shape' => 'string', ], ], ], 'InvalidSignalDecoder' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FullyQualifiedName', ], 'reason' => [ 'shape' => 'SignalDecoderFailureReason', ], 'hint' => [ 'shape' => 'message', ], ], ], 'InvalidSignalDecoders' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvalidSignalDecoder', ], ], 'InvalidSignals' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvalidSignal', ], ], 'InvalidSignalsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'string', ], 'invalidSignals' => [ 'shape' => 'InvalidSignals', ], ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'string', ], 'resourceId' => [ 'shape' => 'string', ], 'resourceType' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ListCampaignsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'status' => [ 'shape' => 'statusStr', ], 'listResponseScope' => [ 'shape' => 'ListResponseScope', ], ], ], 'ListCampaignsResponse' => [ 'type' => 'structure', 'members' => [ 'campaignSummaries' => [ 'shape' => 'campaignSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListDecoderManifestNetworkInterfacesRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], ], ], 'ListDecoderManifestNetworkInterfacesResponse' => [ 'type' => 'structure', 'members' => [ 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListDecoderManifestSignalsRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], ], ], 'ListDecoderManifestSignalsResponse' => [ 'type' => 'structure', 'members' => [ 'signalDecoders' => [ 'shape' => 'SignalDecoders', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListDecoderManifestsRequest' => [ 'type' => 'structure', 'members' => [ 'modelManifestArn' => [ 'shape' => 'arn', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'listResponseScope' => [ 'shape' => 'ListResponseScope', ], ], ], 'ListDecoderManifestsResponse' => [ 'type' => 'structure', 'members' => [ 'summaries' => [ 'shape' => 'decoderManifestSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListFleetsForVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], ], ], 'ListFleetsForVehicleResponse' => [ 'type' => 'structure', 'members' => [ 'fleets' => [ 'shape' => 'fleets', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListFleetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'listResponseScope' => [ 'shape' => 'ListResponseScope', ], ], ], 'ListFleetsResponse' => [ 'type' => 'structure', 'members' => [ 'fleetSummaries' => [ 'shape' => 'fleetSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListModelManifestNodesRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], ], ], 'ListModelManifestNodesResponse' => [ 'type' => 'structure', 'members' => [ 'nodes' => [ 'shape' => 'Nodes', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListModelManifestsRequest' => [ 'type' => 'structure', 'members' => [ 'signalCatalogArn' => [ 'shape' => 'arn', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'listResponseScope' => [ 'shape' => 'ListResponseScope', ], ], ], 'ListModelManifestsResponse' => [ 'type' => 'structure', 'members' => [ 'summaries' => [ 'shape' => 'modelManifestSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListResponseScope' => [ 'type' => 'string', 'enum' => [ 'METADATA_ONLY', ], ], 'ListSignalCatalogNodesRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'signalNodeType' => [ 'shape' => 'SignalNodeType', ], ], ], 'ListSignalCatalogNodesResponse' => [ 'type' => 'structure', 'members' => [ 'nodes' => [ 'shape' => 'Nodes', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListSignalCatalogsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], ], ], 'ListSignalCatalogsResponse' => [ 'type' => 'structure', 'members' => [ 'summaries' => [ 'shape' => 'signalCatalogSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListStateTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], 'listResponseScope' => [ 'shape' => 'ListResponseScope', ], ], ], 'ListStateTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'summaries' => [ 'shape' => 'StateTemplateSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListVehiclesInFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleetId', ], 'members' => [ 'fleetId' => [ 'shape' => 'fleetId', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'maxResults', ], ], ], 'ListVehiclesInFleetResponse' => [ 'type' => 'structure', 'members' => [ 'vehicles' => [ 'shape' => 'vehicles', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'ListVehiclesRequest' => [ 'type' => 'structure', 'members' => [ 'modelManifestArn' => [ 'shape' => 'arn', ], 'attributeNames' => [ 'shape' => 'attributeNamesList', ], 'attributeValues' => [ 'shape' => 'attributeValuesList', ], 'nextToken' => [ 'shape' => 'nextToken', ], 'maxResults' => [ 'shape' => 'listVehiclesMaxResults', ], 'listResponseScope' => [ 'shape' => 'ListResponseScope', ], ], ], 'ListVehiclesResponse' => [ 'type' => 'structure', 'members' => [ 'vehicleSummaries' => [ 'shape' => 'vehicleSummaries', ], 'nextToken' => [ 'shape' => 'nextToken', ], ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ERROR', ], ], 'ManifestStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DRAFT', 'INVALID', 'VALIDATING', ], ], 'MessageSignal' => [ 'type' => 'structure', 'required' => [ 'topicName', 'structuredMessage', ], 'members' => [ 'topicName' => [ 'shape' => 'TopicName', ], 'structuredMessage' => [ 'shape' => 'StructuredMessage', ], ], ], 'ModelManifestSummary' => [ 'type' => 'structure', 'required' => [ 'creationTime', 'lastModificationTime', ], 'members' => [ 'name' => [ 'shape' => 'string', ], 'arn' => [ 'shape' => 'arn', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'status' => [ 'shape' => 'ManifestStatus', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'ModelSignalsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'string', ], 'value' => [ 'shape' => 'string', ], ], 'MqttTopicArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.*', ], 'MqttTopicConfig' => [ 'type' => 'structure', 'required' => [ 'mqttTopicArn', 'executionRoleArn', ], 'members' => [ 'mqttTopicArn' => [ 'shape' => 'MqttTopicArn', ], 'executionRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'NetworkFileBlob' => [ 'type' => 'blob', 'max' => 200000000, 'min' => 0, ], 'NetworkFileDefinition' => [ 'type' => 'structure', 'members' => [ 'canDbc' => [ 'shape' => 'CanDbcDefinition', ], ], 'union' => true, ], 'NetworkFileDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkFileDefinition', ], ], 'NetworkFilesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkFileBlob', ], 'max' => 5, 'min' => 1, ], 'NetworkInterface' => [ 'type' => 'structure', 'required' => [ 'interfaceId', 'type', ], 'members' => [ 'interfaceId' => [ 'shape' => 'InterfaceId', ], 'type' => [ 'shape' => 'NetworkInterfaceType', ], 'canInterface' => [ 'shape' => 'CanInterface', ], 'obdInterface' => [ 'shape' => 'ObdInterface', ], 'vehicleMiddleware' => [ 'shape' => 'VehicleMiddleware', ], 'customDecodingInterface' => [ 'shape' => 'CustomDecodingInterface', ], ], ], 'NetworkInterfaceFailureReason' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_NETWORK_INTERFACE', 'CONFLICTING_NETWORK_INTERFACE', 'NETWORK_INTERFACE_TO_ADD_ALREADY_EXISTS', 'CAN_NETWORK_INTERFACE_INFO_IS_NULL', 'OBD_NETWORK_INTERFACE_INFO_IS_NULL', 'NETWORK_INTERFACE_TO_REMOVE_ASSOCIATED_WITH_SIGNALS', 'VEHICLE_MIDDLEWARE_NETWORK_INTERFACE_INFO_IS_NULL', 'CUSTOM_DECODING_SIGNAL_NETWORK_INTERFACE_INFO_IS_NULL', ], ], 'NetworkInterfaceType' => [ 'type' => 'string', 'enum' => [ 'CAN_INTERFACE', 'OBD_INTERFACE', 'VEHICLE_MIDDLEWARE', 'CUSTOM_DECODING_INTERFACE', ], ], 'NetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], 'max' => 500, 'min' => 1, ], 'Node' => [ 'type' => 'structure', 'members' => [ 'branch' => [ 'shape' => 'Branch', ], 'sensor' => [ 'shape' => 'Sensor', ], 'actuator' => [ 'shape' => 'Actuator', ], 'attribute' => [ 'shape' => 'Attribute', ], 'struct' => [ 'shape' => 'CustomStruct', ], 'property' => [ 'shape' => 'CustomProperty', ], ], 'union' => true, ], 'NodeCounts' => [ 'type' => 'structure', 'members' => [ 'totalNodes' => [ 'shape' => 'number', ], 'totalBranches' => [ 'shape' => 'number', ], 'totalSensors' => [ 'shape' => 'number', ], 'totalAttributes' => [ 'shape' => 'number', ], 'totalActuators' => [ 'shape' => 'number', ], 'totalStructs' => [ 'shape' => 'number', ], 'totalProperties' => [ 'shape' => 'number', ], ], ], 'NodeDataEncoding' => [ 'type' => 'string', 'enum' => [ 'BINARY', 'TYPED', ], ], 'NodeDataType' => [ 'type' => 'string', 'enum' => [ 'INT8', 'UINT8', 'INT16', 'UINT16', 'INT32', 'UINT32', 'INT64', 'UINT64', 'BOOLEAN', 'FLOAT', 'DOUBLE', 'STRING', 'UNIX_TIMESTAMP', 'INT8_ARRAY', 'UINT8_ARRAY', 'INT16_ARRAY', 'UINT16_ARRAY', 'INT32_ARRAY', 'UINT32_ARRAY', 'INT64_ARRAY', 'UINT64_ARRAY', 'BOOLEAN_ARRAY', 'FLOAT_ARRAY', 'DOUBLE_ARRAY', 'STRING_ARRAY', 'UNIX_TIMESTAMP_ARRAY', 'UNKNOWN', 'STRUCT', 'STRUCT_ARRAY', ], ], 'NodePath' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.]+', ], 'NodePaths' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodePath', ], 'max' => 500, 'min' => 1, ], 'Nodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Node', ], 'max' => 500, 'min' => 0, ], 'ObdBitmaskLength' => [ 'type' => 'integer', 'box' => true, 'max' => 8, 'min' => 1, ], 'ObdByteLength' => [ 'type' => 'integer', 'box' => true, 'max' => 8, 'min' => 1, ], 'ObdInterface' => [ 'type' => 'structure', 'required' => [ 'name', 'requestMessageId', ], 'members' => [ 'name' => [ 'shape' => 'ObdInterfaceName', ], 'requestMessageId' => [ 'shape' => 'nonNegativeInteger', ], 'obdStandard' => [ 'shape' => 'ObdStandard', ], 'pidRequestIntervalSeconds' => [ 'shape' => 'nonNegativeInteger', ], 'dtcRequestIntervalSeconds' => [ 'shape' => 'nonNegativeInteger', ], 'useExtendedIds' => [ 'shape' => 'PrimitiveBoolean', ], 'hasTransmissionEcu' => [ 'shape' => 'PrimitiveBoolean', ], ], ], 'ObdInterfaceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'ObdSignal' => [ 'type' => 'structure', 'required' => [ 'pidResponseLength', 'serviceMode', 'pid', 'scaling', 'offset', 'startByte', 'byteLength', ], 'members' => [ 'pidResponseLength' => [ 'shape' => 'positiveInteger', ], 'serviceMode' => [ 'shape' => 'nonNegativeInteger', ], 'pid' => [ 'shape' => 'nonNegativeInteger', ], 'scaling' => [ 'shape' => 'double', ], 'offset' => [ 'shape' => 'double', ], 'startByte' => [ 'shape' => 'nonNegativeInteger', ], 'byteLength' => [ 'shape' => 'ObdByteLength', ], 'bitRightShift' => [ 'shape' => 'nonNegativeInteger', ], 'bitMaskLength' => [ 'shape' => 'ObdBitmaskLength', ], ], ], 'ObdStandard' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'OnChangeStateTemplateUpdateStrategy' => [ 'type' => 'structure', 'members' => [], ], 'PeriodicStateTemplateUpdateStrategy' => [ 'type' => 'structure', 'required' => [ 'stateTemplateUpdateRate', ], 'members' => [ 'stateTemplateUpdateRate' => [ 'shape' => 'TimePeriod', ], ], ], 'Prefix' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_:./!*\'()]+', ], 'PrimitiveBoolean' => [ 'type' => 'boolean', ], 'PrimitiveMessageDefinition' => [ 'type' => 'structure', 'members' => [ 'ros2PrimitiveMessageDefinition' => [ 'shape' => 'ROS2PrimitiveMessageDefinition', ], ], 'union' => true, ], 'ProtocolName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'ProtocolVersion' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'PutEncryptionConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'encryptionType', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'PutEncryptionConfigurationRequestKmsKeyIdString', ], 'encryptionType' => [ 'shape' => 'EncryptionType', ], ], ], 'PutEncryptionConfigurationRequestKmsKeyIdString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'PutEncryptionConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'encryptionStatus', 'encryptionType', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'String', ], 'encryptionStatus' => [ 'shape' => 'EncryptionStatus', ], 'encryptionType' => [ 'shape' => 'EncryptionType', ], ], ], 'PutLoggingOptionsRequest' => [ 'type' => 'structure', 'required' => [ 'cloudWatchLogDelivery', ], 'members' => [ 'cloudWatchLogDelivery' => [ 'shape' => 'CloudWatchLogDeliveryOptions', ], ], ], 'PutLoggingOptionsResponse' => [ 'type' => 'structure', 'members' => [], ], 'ROS2PrimitiveMessageDefinition' => [ 'type' => 'structure', 'required' => [ 'primitiveType', ], 'members' => [ 'primitiveType' => [ 'shape' => 'ROS2PrimitiveType', ], 'offset' => [ 'shape' => 'double', ], 'scaling' => [ 'shape' => 'double', ], 'upperBound' => [ 'shape' => 'ROS2PrimitiveMessageDefinitionUpperBoundLong', ], ], ], 'ROS2PrimitiveMessageDefinitionUpperBoundLong' => [ 'type' => 'long', 'box' => true, 'max' => 2048, 'min' => 0, ], 'ROS2PrimitiveType' => [ 'type' => 'string', 'enum' => [ 'BOOL', 'BYTE', 'CHAR', 'FLOAT32', 'FLOAT64', 'INT8', 'UINT8', 'INT16', 'UINT16', 'INT32', 'UINT32', 'INT64', 'UINT64', 'STRING', 'WSTRING', ], ], 'RegisterAccountRequest' => [ 'type' => 'structure', 'members' => [ 'timestreamResources' => [ 'shape' => 'TimestreamResources', 'deprecated' => true, 'deprecatedMessage' => 'Amazon Timestream metadata is now passed in the CreateCampaign API.', ], 'iamResources' => [ 'shape' => 'IamResources', 'deprecated' => true, 'deprecatedMessage' => 'iamResources is no longer used or needed as input', ], ], ], 'RegisterAccountResponse' => [ 'type' => 'structure', 'required' => [ 'registerAccountStatus', 'iamResources', 'creationTime', 'lastModificationTime', ], 'members' => [ 'registerAccountStatus' => [ 'shape' => 'RegistrationStatus', ], 'timestreamResources' => [ 'shape' => 'TimestreamResources', ], 'iamResources' => [ 'shape' => 'IamResources', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'RegistrationStatus' => [ 'type' => 'string', 'enum' => [ 'REGISTRATION_PENDING', 'REGISTRATION_SUCCESS', 'REGISTRATION_FAILURE', ], ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z\\d\\-_:]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'string', ], 'resourceId' => [ 'shape' => 'string', ], 'resourceType' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'ResourceUniqueId' => [ 'type' => 'string', 'max' => 26, 'min' => 26, 'pattern' => '[A-Z0-9]+', ], 'RetryAfterSeconds' => [ 'type' => 'integer', ], 'S3BucketArn' => [ 'type' => 'string', 'max' => 100, 'min' => 16, 'pattern' => 'arn:(aws[a-zA-Z0-9-]*):s3:::.+', ], 'S3Config' => [ 'type' => 'structure', 'required' => [ 'bucketArn', ], 'members' => [ 'bucketArn' => [ 'shape' => 'S3BucketArn', ], 'dataFormat' => [ 'shape' => 'DataFormat', ], 'storageCompressionFormat' => [ 'shape' => 'StorageCompressionFormat', ], 'prefix' => [ 'shape' => 'Prefix', ], ], ], 'Sensor' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', 'dataType', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'string', ], 'dataType' => [ 'shape' => 'NodeDataType', ], 'description' => [ 'shape' => 'description', ], 'unit' => [ 'shape' => 'string', ], 'allowedValues' => [ 'shape' => 'listOfStrings', ], 'min' => [ 'shape' => 'double', ], 'max' => [ 'shape' => 'double', ], 'deprecationMessage' => [ 'shape' => 'message', ], 'comment' => [ 'shape' => 'message', ], 'structFullyQualifiedName' => [ 'shape' => 'NodePath', ], ], ], 'SignalCatalogSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'string', ], 'arn' => [ 'shape' => 'arn', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], ], ], 'SignalDecoder' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', 'type', 'interfaceId', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'FullyQualifiedName', ], 'type' => [ 'shape' => 'SignalDecoderType', ], 'interfaceId' => [ 'shape' => 'InterfaceId', ], 'canSignal' => [ 'shape' => 'CanSignal', ], 'obdSignal' => [ 'shape' => 'ObdSignal', ], 'messageSignal' => [ 'shape' => 'MessageSignal', ], 'customDecodingSignal' => [ 'shape' => 'CustomDecodingSignal', ], ], ], 'SignalDecoderFailureReason' => [ 'type' => 'string', 'enum' => [ 'DUPLICATE_SIGNAL', 'CONFLICTING_SIGNAL', 'SIGNAL_TO_ADD_ALREADY_EXISTS', 'SIGNAL_NOT_ASSOCIATED_WITH_NETWORK_INTERFACE', 'NETWORK_INTERFACE_TYPE_INCOMPATIBLE_WITH_SIGNAL_DECODER_TYPE', 'SIGNAL_NOT_IN_MODEL', 'CAN_SIGNAL_INFO_IS_NULL', 'OBD_SIGNAL_INFO_IS_NULL', 'NO_DECODER_INFO_FOR_SIGNAL_IN_MODEL', 'MESSAGE_SIGNAL_INFO_IS_NULL', 'SIGNAL_DECODER_TYPE_INCOMPATIBLE_WITH_MESSAGE_SIGNAL_TYPE', 'STRUCT_SIZE_MISMATCH', 'NO_SIGNAL_IN_CATALOG_FOR_DECODER_SIGNAL', 'SIGNAL_DECODER_INCOMPATIBLE_WITH_SIGNAL_CATALOG', 'EMPTY_MESSAGE_SIGNAL', 'CUSTOM_DECODING_SIGNAL_INFO_IS_NULL', ], ], 'SignalDecoderType' => [ 'type' => 'string', 'enum' => [ 'CAN_SIGNAL', 'OBD_SIGNAL', 'MESSAGE_SIGNAL', 'CUSTOM_DECODING_SIGNAL', ], ], 'SignalDecoders' => [ 'type' => 'list', 'member' => [ 'shape' => 'SignalDecoder', ], 'max' => 500, 'min' => 1, ], 'SignalFetchConfig' => [ 'type' => 'structure', 'members' => [ 'timeBased' => [ 'shape' => 'TimeBasedSignalFetchConfig', ], 'conditionBased' => [ 'shape' => 'ConditionBasedSignalFetchConfig', ], ], 'union' => true, ], 'SignalFetchInformation' => [ 'type' => 'structure', 'required' => [ 'fullyQualifiedName', 'signalFetchConfig', 'actions', ], 'members' => [ 'fullyQualifiedName' => [ 'shape' => 'NodePath', ], 'signalFetchConfig' => [ 'shape' => 'SignalFetchConfig', ], 'conditionLanguageVersion' => [ 'shape' => 'languageVersion', ], 'actions' => [ 'shape' => 'EventExpressionList', ], ], ], 'SignalFetchInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SignalFetchInformation', ], 'max' => 2, 'min' => 1, 'sensitive' => true, ], 'SignalInformation' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'wildcardSignalName', ], 'maxSampleCount' => [ 'shape' => 'maxSampleCount', ], 'minimumSamplingIntervalMs' => [ 'shape' => 'uint32', ], 'dataPartitionId' => [ 'shape' => 'DataPartitionId', ], ], ], 'SignalInformationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SignalInformation', ], 'max' => 1000, 'min' => 0, 'sensitive' => true, ], 'SignalNodeType' => [ 'type' => 'string', 'enum' => [ 'SENSOR', 'ACTUATOR', 'ATTRIBUTE', 'BRANCH', 'CUSTOM_STRUCT', 'CUSTOM_PROPERTY', ], ], 'SpoolingMode' => [ 'type' => 'string', 'enum' => [ 'OFF', 'TO_DISK', ], ], 'StateTemplateAssociation' => [ 'type' => 'structure', 'required' => [ 'identifier', 'stateTemplateUpdateStrategy', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceIdentifier', ], 'stateTemplateUpdateStrategy' => [ 'shape' => 'StateTemplateUpdateStrategy', ], ], ], 'StateTemplateAssociationIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceIdentifier', ], 'max' => 20, 'min' => 1, ], 'StateTemplateAssociations' => [ 'type' => 'list', 'member' => [ 'shape' => 'StateTemplateAssociation', ], 'max' => 20, 'min' => 1, ], 'StateTemplateDataExtraDimensionNodePathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodePath', ], 'max' => 5, 'min' => 0, ], 'StateTemplateMetadataExtraDimensionNodePathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodePath', ], 'max' => 5, 'min' => 0, ], 'StateTemplateProperties' => [ 'type' => 'list', 'member' => [ 'shape' => 'NodePath', ], 'max' => 500, 'min' => 1, ], 'StateTemplateSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StateTemplateSummary', ], ], 'StateTemplateSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'signalCatalogArn' => [ 'shape' => 'arn', ], 'description' => [ 'shape' => 'description', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], 'id' => [ 'shape' => 'ResourceUniqueId', ], ], ], 'StateTemplateUpdateStrategy' => [ 'type' => 'structure', 'members' => [ 'periodic' => [ 'shape' => 'PeriodicStateTemplateUpdateStrategy', ], 'onChange' => [ 'shape' => 'OnChangeStateTemplateUpdateStrategy', ], ], 'union' => true, ], 'StorageCompressionFormat' => [ 'type' => 'string', 'enum' => [ 'NONE', 'GZIP', ], ], 'StorageLocation' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'sensitive' => true, ], 'StorageMaximumSize' => [ 'type' => 'structure', 'required' => [ 'unit', 'value', ], 'members' => [ 'unit' => [ 'shape' => 'StorageMaximumSizeUnit', ], 'value' => [ 'shape' => 'StorageMaximumSizeValue', ], ], ], 'StorageMaximumSizeUnit' => [ 'type' => 'string', 'enum' => [ 'MB', 'GB', 'TB', ], ], 'StorageMaximumSizeValue' => [ 'type' => 'integer', 'box' => true, 'max' => 1073741824, 'min' => 1, ], 'StorageMinimumTimeToLive' => [ 'type' => 'structure', 'required' => [ 'unit', 'value', ], 'members' => [ 'unit' => [ 'shape' => 'StorageMinimumTimeToLiveUnit', ], 'value' => [ 'shape' => 'StorageMinimumTimeToLiveValue', ], ], ], 'StorageMinimumTimeToLiveUnit' => [ 'type' => 'string', 'enum' => [ 'HOURS', 'DAYS', 'WEEKS', ], ], 'StorageMinimumTimeToLiveValue' => [ 'type' => 'integer', 'box' => true, 'max' => 876600, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'StructureMessageName' => [ 'type' => 'string', 'max' => 150, 'min' => 1, ], 'StructuredMessage' => [ 'type' => 'structure', 'members' => [ 'primitiveMessageDefinition' => [ 'shape' => 'PrimitiveMessageDefinition', ], 'structuredMessageListDefinition' => [ 'shape' => 'StructuredMessageListDefinition', ], 'structuredMessageDefinition' => [ 'shape' => 'StructuredMessageDefinition', ], ], 'union' => true, ], 'StructuredMessageDefinition' => [ 'type' => 'list', 'member' => [ 'shape' => 'StructuredMessageFieldNameAndDataTypePair', ], 'max' => 500, 'min' => 1, ], 'StructuredMessageFieldNameAndDataTypePair' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'dataType', ], 'members' => [ 'fieldName' => [ 'shape' => 'StructureMessageName', ], 'dataType' => [ 'shape' => 'StructuredMessage', ], ], ], 'StructuredMessageListDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'memberType', 'listType', ], 'members' => [ 'name' => [ 'shape' => 'StructureMessageName', ], 'memberType' => [ 'shape' => 'StructuredMessage', ], 'listType' => [ 'shape' => 'StructuredMessageListType', ], 'capacity' => [ 'shape' => 'nonNegativeInteger', ], ], ], 'StructuredMessageListType' => [ 'type' => 'string', 'enum' => [ 'FIXED_CAPACITY', 'DYNAMIC_UNBOUNDED_CAPACITY', 'DYNAMIC_BOUNDED_CAPACITY', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], 'quotaCode' => [ 'shape' => 'string', ], 'serviceCode' => [ 'shape' => 'string', ], 'retryAfterSeconds' => [ 'shape' => 'RetryAfterSeconds', ], ], 'exception' => true, ], 'TimeBasedCollectionScheme' => [ 'type' => 'structure', 'required' => [ 'periodMs', ], 'members' => [ 'periodMs' => [ 'shape' => 'collectionPeriodMs', ], ], ], 'TimeBasedSignalFetchConfig' => [ 'type' => 'structure', 'required' => [ 'executionFrequencyMs', ], 'members' => [ 'executionFrequencyMs' => [ 'shape' => 'positiveLong', ], ], ], 'TimePeriod' => [ 'type' => 'structure', 'required' => [ 'unit', 'value', ], 'members' => [ 'unit' => [ 'shape' => 'TimeUnit', ], 'value' => [ 'shape' => 'positiveInteger', ], ], ], 'TimeUnit' => [ 'type' => 'string', 'enum' => [ 'MILLISECOND', 'SECOND', 'MINUTE', 'HOUR', ], ], 'TimestreamConfig' => [ 'type' => 'structure', 'required' => [ 'timestreamTableArn', 'executionRoleArn', ], 'members' => [ 'timestreamTableArn' => [ 'shape' => 'TimestreamTableArn', ], 'executionRoleArn' => [ 'shape' => 'IAMRoleArn', ], ], ], 'TimestreamDatabaseName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'TimestreamRegistrationResponse' => [ 'type' => 'structure', 'required' => [ 'timestreamDatabaseName', 'timestreamTableName', 'registrationStatus', ], 'members' => [ 'timestreamDatabaseName' => [ 'shape' => 'TimestreamDatabaseName', ], 'timestreamTableName' => [ 'shape' => 'TimestreamTableName', ], 'timestreamDatabaseArn' => [ 'shape' => 'arn', ], 'timestreamTableArn' => [ 'shape' => 'arn', ], 'registrationStatus' => [ 'shape' => 'RegistrationStatus', ], 'errorMessage' => [ 'shape' => 'errorMessage', ], ], ], 'TimestreamResources' => [ 'type' => 'structure', 'required' => [ 'timestreamDatabaseName', 'timestreamTableName', ], 'members' => [ 'timestreamDatabaseName' => [ 'shape' => 'TimestreamDatabaseName', ], 'timestreamTableName' => [ 'shape' => 'TimestreamTableName', ], ], ], 'TimestreamTableArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:(aws[a-zA-Z0-9-]*):timestream:[a-zA-Z0-9-]+:[0-9]{12}:database/[a-zA-Z0-9_.-]+/table/[a-zA-Z0-9_.-]+', ], 'TimestreamTableName' => [ 'type' => 'string', 'max' => 255, 'min' => 3, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'TopicName' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '[a-zA-Z0-9_\\-#:./]+', ], 'TriggerMode' => [ 'type' => 'string', 'enum' => [ 'ALWAYS', 'RISING_EDGE', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCampaignAction' => [ 'type' => 'string', 'enum' => [ 'APPROVE', 'SUSPEND', 'RESUME', 'UPDATE', ], ], 'UpdateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'action', ], 'members' => [ 'name' => [ 'shape' => 'campaignName', ], 'description' => [ 'shape' => 'description', ], 'dataExtraDimensions' => [ 'shape' => 'DataExtraDimensionNodePathList', ], 'action' => [ 'shape' => 'UpdateCampaignAction', ], ], ], 'UpdateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'campaignArn', ], 'name' => [ 'shape' => 'campaignName', ], 'status' => [ 'shape' => 'CampaignStatus', ], ], ], 'UpdateDecoderManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'signalDecodersToAdd' => [ 'shape' => 'SignalDecoders', ], 'signalDecodersToUpdate' => [ 'shape' => 'SignalDecoders', ], 'signalDecodersToRemove' => [ 'shape' => 'Fqns', ], 'networkInterfacesToAdd' => [ 'shape' => 'NetworkInterfaces', ], 'networkInterfacesToUpdate' => [ 'shape' => 'NetworkInterfaces', ], 'networkInterfacesToRemove' => [ 'shape' => 'InterfaceIds', ], 'status' => [ 'shape' => 'ManifestStatus', ], 'defaultForUnmappedSignals' => [ 'shape' => 'DefaultForUnmappedSignalsType', ], ], ], 'UpdateDecoderManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'UpdateFleetRequest' => [ 'type' => 'structure', 'required' => [ 'fleetId', ], 'members' => [ 'fleetId' => [ 'shape' => 'fleetId', ], 'description' => [ 'shape' => 'description', ], ], ], 'UpdateFleetResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'fleetId', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'UpdateMode' => [ 'type' => 'string', 'enum' => [ 'Overwrite', 'Merge', ], ], 'UpdateModelManifestRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'nodesToAdd' => [ 'shape' => 'NodePaths', ], 'nodesToRemove' => [ 'shape' => 'NodePaths', ], 'status' => [ 'shape' => 'ManifestStatus', ], ], ], 'UpdateModelManifestResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'UpdateSignalCatalogRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'description' => [ 'shape' => 'description', ], 'nodesToAdd' => [ 'shape' => 'Nodes', ], 'nodesToUpdate' => [ 'shape' => 'Nodes', ], 'nodesToRemove' => [ 'shape' => 'NodePaths', ], ], ], 'UpdateSignalCatalogResponse' => [ 'type' => 'structure', 'required' => [ 'name', 'arn', ], 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'UpdateStateTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ResourceIdentifier', ], 'description' => [ 'shape' => 'description', ], 'stateTemplatePropertiesToAdd' => [ 'shape' => 'StateTemplateProperties', ], 'stateTemplatePropertiesToRemove' => [ 'shape' => 'StateTemplateProperties', ], 'dataExtraDimensions' => [ 'shape' => 'StateTemplateDataExtraDimensionNodePathList', ], 'metadataExtraDimensions' => [ 'shape' => 'StateTemplateMetadataExtraDimensionNodePathList', ], ], ], 'UpdateStateTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'resourceName', ], 'arn' => [ 'shape' => 'arn', ], 'id' => [ 'shape' => 'ResourceUniqueId', ], ], ], 'UpdateVehicleError' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'code' => [ 'shape' => 'number', ], 'message' => [ 'shape' => 'string', ], ], ], 'UpdateVehicleRequest' => [ 'type' => 'structure', 'required' => [ 'vehicleName', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'decoderManifestArn' => [ 'shape' => 'arn', ], 'attributes' => [ 'shape' => 'attributesMap', ], 'attributeUpdateMode' => [ 'shape' => 'UpdateMode', ], 'stateTemplatesToAdd' => [ 'shape' => 'StateTemplateAssociations', ], 'stateTemplatesToRemove' => [ 'shape' => 'StateTemplateAssociationIdentifiers', ], ], ], 'UpdateVehicleRequestItem' => [ 'type' => 'structure', 'required' => [ 'vehicleName', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'decoderManifestArn' => [ 'shape' => 'arn', ], 'attributes' => [ 'shape' => 'attributesMap', ], 'attributeUpdateMode' => [ 'shape' => 'UpdateMode', ], 'stateTemplatesToAdd' => [ 'shape' => 'StateTemplateAssociations', ], 'stateTemplatesToRemove' => [ 'shape' => 'StateTemplateAssociationIdentifiers', ], ], ], 'UpdateVehicleResponse' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'UpdateVehicleResponseItem' => [ 'type' => 'structure', 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'string', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VehicleAssociationBehavior' => [ 'type' => 'string', 'enum' => [ 'CreateIotThing', 'ValidateIotThingExists', ], ], 'VehicleMiddleware' => [ 'type' => 'structure', 'required' => [ 'name', 'protocolName', ], 'members' => [ 'name' => [ 'shape' => 'VehicleMiddlewareName', ], 'protocolName' => [ 'shape' => 'VehicleMiddlewareProtocol', ], ], ], 'VehicleMiddlewareName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'VehicleMiddlewareProtocol' => [ 'type' => 'string', 'enum' => [ 'ROS_2', ], ], 'VehicleState' => [ 'type' => 'string', 'enum' => [ 'CREATED', 'READY', 'HEALTHY', 'SUSPENDED', 'DELETING', ], ], 'VehicleStatus' => [ 'type' => 'structure', 'members' => [ 'campaignName' => [ 'shape' => 'campaignName', ], 'vehicleName' => [ 'shape' => 'vehicleName', ], 'status' => [ 'shape' => 'VehicleState', ], ], ], 'VehicleStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VehicleStatus', ], ], 'VehicleSummary' => [ 'type' => 'structure', 'required' => [ 'vehicleName', 'arn', 'modelManifestArn', 'decoderManifestArn', 'creationTime', 'lastModificationTime', ], 'members' => [ 'vehicleName' => [ 'shape' => 'vehicleName', ], 'arn' => [ 'shape' => 'arn', ], 'modelManifestArn' => [ 'shape' => 'arn', ], 'decoderManifestArn' => [ 'shape' => 'arn', ], 'creationTime' => [ 'shape' => 'timestamp', ], 'lastModificationTime' => [ 'shape' => 'timestamp', ], 'attributes' => [ 'shape' => 'attributesMap', ], ], ], 'actionEventExpression' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'sensitive' => true, ], 'arn' => [ 'type' => 'string', ], 'attributeName' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.-]+', ], 'attributeNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'attributeName', ], 'max' => 5, 'min' => 1, ], 'attributeValue' => [ 'type' => 'string', ], 'attributeValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'attributeValue', ], 'max' => 5, 'min' => 1, ], 'attributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'attributeName', ], 'value' => [ 'shape' => 'attributeValue', ], ], 'campaignArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iotfleetwise:[a-z0-9-]+:[0-9]{12}:campaign/[a-zA-Z\\d\\-_:]{1,100}', ], 'campaignName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z\\d\\-_:]+', ], 'campaignSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CampaignSummary', ], ], 'collectionPeriodMs' => [ 'type' => 'long', 'box' => true, 'max' => ********, 'min' => 10000, ], 'createVehicleErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateVehicleError', ], ], 'createVehicleRequestItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateVehicleRequestItem', ], 'max' => 10, 'min' => 1, ], 'createVehicleResponses' => [ 'type' => 'list', 'member' => [ 'shape' => 'CreateVehicleResponseItem', ], ], 'customerAccountId' => [ 'type' => 'string', ], 'decoderManifestSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DecoderManifestSummary', ], ], 'description' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'double' => [ 'type' => 'double', 'box' => true, ], 'errorMessage' => [ 'type' => 'string', ], 'eventExpression' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'sensitive' => true, ], 'fetchConfigEventExpression' => [ 'type' => 'string', 'max' => 400, 'min' => 1, 'sensitive' => true, ], 'fleetId' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9:_-]+', ], 'fleetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetSummary', ], ], 'fleets' => [ 'type' => 'list', 'member' => [ 'shape' => 'fleetId', ], ], 'languageVersion' => [ 'type' => 'integer', 'box' => true, 'max' => 1, 'min' => 1, ], 'listOfStrings' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'listVehiclesMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'maxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'maxSampleCount' => [ 'type' => 'long', 'box' => true, 'max' => 4294967295, 'min' => 1, ], 'message' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[^\\u0000-\\u001F\\u007F]+', ], 'modelManifestSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelManifestSummary', ], ], 'nextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, ], 'nonNegativeInteger' => [ 'type' => 'integer', 'min' => 0, ], 'number' => [ 'type' => 'integer', ], 'positiveInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'positiveLong' => [ 'type' => 'long', 'box' => true, 'min' => 1, ], 'priority' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'resourceName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z\\d\\-_:]+', ], 'signalCatalogSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'SignalCatalogSummary', ], ], 'statusStr' => [ 'type' => 'string', 'max' => 20, 'min' => 7, 'pattern' => '[A-Z_]*', ], 'string' => [ 'type' => 'string', ], 'timestamp' => [ 'type' => 'timestamp', ], 'uint32' => [ 'type' => 'long', 'box' => true, 'max' => 4294967295, 'min' => 0, ], 'updateVehicleErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateVehicleError', ], ], 'updateVehicleRequestItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateVehicleRequestItem', ], 'max' => 10, 'min' => 1, ], 'updateVehicleResponseItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateVehicleResponseItem', ], ], 'vehicleName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z\\d\\-_:]+', ], 'vehicleSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'VehicleSummary', ], ], 'vehicles' => [ 'type' => 'list', 'member' => [ 'shape' => 'vehicleName', ], ], 'wildcardSignalName' => [ 'type' => 'string', 'max' => 150, 'min' => 1, 'pattern' => '[\\w|*|-]+(\\.[\\w|*|-]+)*', ], ],];
