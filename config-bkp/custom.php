<?php
return [
    'custom' => [
        'mainLayoutType' => 'vertical',  
        'theme' => 'light',  
        'sidebarCollapsed' => false,  
        'navbarColor' => '',  
        'horizontalMenuType' => 'floating',  
        'verticalMenuNavbarType' => 'floating', 
        'footerType' => 'static',  
        'layoutWidth' => 'boxed',  
        'showMenu' => true,  
        'bodyClass' => '',  
        'pageHeader' => true,  
        'contentLayout' => 'default',  
        'defaultLanguage' => 'en',     
        'blankPage' => false, 
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'),  
    ],
    'demo-1' => [
        'mainLayoutType' => 'vertical',  
        'theme' => 'light',  
        'sidebarCollapsed' => false,  
        'navbarColor' => '',  
        'horizontalMenuType' => 'static',  
        'verticalMenuNavbarType' => 'floating',  
        'footerType' => 'static',  
        'layoutWidth' => 'full',  
        'showMenu' => true,  
        'bodyClass' => '',  
        'pageHeader' => true, 
        'contentLayout' => 'default',  
        'defaultLanguage' => 'en',    
        'blankPage' => false,  
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'),  
    ],
    'demo-2' => [
        'mainLayoutType' => 'vertical', 
        'theme' => 'light',  
        'sidebarCollapsed' => true,  
        'navbarColor' => '',  
        'horizontalMenuType' => 'static',  
        'verticalMenuNavbarType' => 'floating',  
        'footerType' => 'static', 
        'layoutWidth' => 'full', 
        'showMenu' => true,  
        'bodyClass' => '',  
        'pageHeader' => true, 
        'contentLayout' => 'default',  
        'defaultLanguage' => 'en',     
        'blankPage' => false,  
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'),  
    ],
    'demo-3' => [
        'mainLayoutType' => 'vertical',  
        'theme' => 'bordered',  
        'sidebarCollapsed' => false,  
        'navbarColor' => '', 
        'horizontalMenuType' => 'static',  
        'verticalMenuNavbarType' => 'floating',  
        'footerType' => 'static',  
        'layoutWidth' => 'full',  
        'showMenu' => true, 
        'bodyClass' => '',  
        'pageHeader' => true,  
        'contentLayout' => 'default',  
        'defaultLanguage' => 'en',    
        'blankPage' => false,  
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'),  
    ],
    'demo-4' => [
        'mainLayoutType' => 'vertical',  
        'theme' => 'dark',  
        'sidebarCollapsed' => false, 
        'navbarColor' => '', 
        'horizontalMenuType' => 'static', 
        'verticalMenuNavbarType' => 'floating',  
        'footerType' => 'static',  
        'layoutWidth' => 'full', 
        'showMenu' => true,  
        'bodyClass' => '', 
        'pageHeader' => true,  
        'contentLayout' => 'default',  
        'defaultLanguage' => 'en',    
        'blankPage' => false,  
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'), 
    ],
    'demo-5' => [
        'mainLayoutType' => 'horizontal',  
        'theme' => 'light',  
        'sidebarCollapsed' => false,  
        'navbarColor' => '', 
        'horizontalMenuType' => 'floating',  
        'verticalMenuNavbarType' => 'floating',  
        'footerType' => 'static', 
        'layoutWidth' => 'full', 
        'showMenu' => true,  
        'bodyClass' => '', 
        'pageHeader' => true, 
        'contentLayout' => 'default', 
        'defaultLanguage' => 'en',   
        'blankPage' => false,  
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'),  
    ],
    'demo-6' => [
        'mainLayoutType' => 'horizontal', 
        'theme' => 'dark', 
        'sidebarCollapsed' => false,  
        'navbarColor' => '',  
        'horizontalMenuType' => 'floating',  
        'verticalMenuNavbarType' => 'floating',  
        'footerType' => 'static',  
        'layoutWidth' => 'full',  
        'showMenu' => true, 
        'bodyClass' => '',  
        'pageHeader' => true,  
        'contentLayout' => 'default',  
        'defaultLanguage' => 'en',    
        'blankPage' => false,  
        'direction' => env('MIX_CONTENT_DIRECTION', 'ltr'),  
    ]
];

/* Do changes in this file if you know what it effects to your template. For more infomation refer the <a href="https://pixinvent.com/demo/vuexy-bootstrap-laravel-admin-template//documentation/documentation-laravel.html"> documentation </a> */
