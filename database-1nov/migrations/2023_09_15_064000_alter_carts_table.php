<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterCartsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('carts', function (Blueprint $table) {
            $table->integer('gift_value_id')->nullable()->change();
            $table->tinyInteger('partner_type')->default(1)->comment('1 for static partner, 2 for dynamic partner')->after('partner_id');
            $table->double('dynamic_partner_amount')->default(0)->after('gift_value');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('carts', function($table) {
            $table->dropColumn(['partner_type','dynamic_partner_amount']);
        });
    }
}
