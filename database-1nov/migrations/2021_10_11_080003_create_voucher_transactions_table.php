<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVoucherTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('voucher_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('txn_id',255)->default(null);
            $table->text('response');
            $table->string('txn_status',255)->default(null);
            $table->text('request');
            $table->integer('user_id')->default(null);
            $table->integer('transaction_type')->comment('1 for status check, 2 for redeem, 3 for cancel transaction');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('voucher_transactions');
    }
}
