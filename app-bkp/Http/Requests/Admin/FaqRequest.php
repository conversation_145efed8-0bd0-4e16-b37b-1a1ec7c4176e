<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class FaqRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    public function authorize()
    {
        return true;
    }
    
    public function rules()
    {
        return [
            
            'question'    => [
                'required',
                'max:200'
            ],
            'answer'  => [
                'required',
            ], 
            'status'  => [
                'required',
            ]
            
        ];
    }
    public function messages()
    {
        return [
            'thumbnail_image_tmp.required_without' =>  'The image field is required.',
            'thumbnail_image_tmp.mimes' =>  'The image must be a file of type: jpeg, png, jpg.'
        ];
    }
}
