<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Auth;
use Illuminate\Validation\Rule;

class AccountDetailsRequest extends FormRequest
{
    /**
     * Get the error messages edit user is not authorized.
     *
     * @return array
     */
   
    /**
     * Get the error messages if rules are not valid.
     *
     * @return array
     */
    
    
    public function rules()
    {
        $user_data = Auth::user();
       
        return [
            'first_name'=>['max:60','string','nullable'],
            'last_name'=>['max:60','string','nullable'],
            'email'    => [
                'required',
                'email',
                Rule::unique('users')->ignore($user_data->id),
            ],
           'phone'=>['integer','nullable']
            
        ];
    }
   
}
