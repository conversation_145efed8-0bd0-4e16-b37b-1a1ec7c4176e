<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Models\Faq as FaqModel;
use App\Http\Requests\Admin\FaqRequest;
class FAQ extends BaseController
{
    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = 5;
    }
    // this handles the manage screen of faq
    public function index()
    {
          $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "javascript:void(0)", 'name' => __('faq.faq')], ['name' =>  __('faq.manage_faq')]
          ];
          $faq_data = FaqModel::where('status','!=',FaqModel::SOFTDELETED)->orderBy('id','desc')->get();

          return view('/admin/pages/faq/manage-faq', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'faq_data' => $faq_data
          ]);
    }
    // this handles the add screen of faq
    public function addFaq()
    {
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/faqs-list", 'name' => __('faq.faq')], ['name' =>  __('faq.manage_faq')]
          ];
        
         return view('/admin/pages/faq/add-faq', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
             
          ]);
    }
    // this handles of edit screen of faq
    public function editFaq($id='')
    {
     
      $faq_data = FaqModel::where('id',$id)->first();
      $breadcrumbs = [
        ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/faqs-list", 'name' => __('faq.faq')], ['name' =>  __('faq.edit_faq')]
      ];
     
       return view('/admin/pages/faq/edit-faq', [
          'breadcrumbs' => $breadcrumbs,
          'active_menu' => $this->active_menu,
          'faq' => $faq_data
        ]);
    }
    // this handle the update of faq
    public function updateFaq(FaqRequest $request)
    {
      $request_data = $request->all();
      $id = $request_data['id'];
      $faq_update = FaqModel::find($id);
      $faq_update->update($request->all());

      
      
      $request->session()->flash('created', __('faq.update_success'));
      return redirect('admin/faqs-list');
    }

    // this handles the delete processs of faq
    public function deleteFaq(Request $request)
    {
      $request_data = $request->all();
      $faq_id = $request_data['faq_id'];
      FaqModel::where('id',$faq_id)->update(['status'=>FaqModel::SOFTDELETED]);
      return true;
    }

    // this handles the view screen of faq
    public function viewFaq($id='')
    {
      $faq_data = FaqModel::where('id',$id)->first();
      $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/faqs-list", 'name' => __('faq.faq')], ['name' =>  __('faq.view_faq')]
       ];
     
       return view('/admin/pages/faq/view-faq', [
          'breadcrumbs' => $breadcrumbs,
          'active_menu' => $this->active_menu,
           'faq' => $faq_data
        ]);
    }

    // this handles the save process of faq
    public function saveFaq(FaqRequest $request)
    {
      $FaqModel = new FaqModel;
      $FaqModel->question = $request->input('question');
      $FaqModel->answer = $request->input('answer');
      $FaqModel->status = $request->input('status');
      $FaqModel->save();
      $request->session()->flash('created', __('faq.added_success'));
      return redirect('admin/faqs-list');
    }
}
