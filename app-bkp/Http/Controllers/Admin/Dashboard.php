<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Models\User;
use App\Models\Role;
use App\Models\Partners;
use App\Models\GiftValues;
use App\Models\Transaction;
class Dashboard extends BaseController
{
  // this handles the dashboard screen
    public function index()
    {      
      $breadcrumbs = [
        ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['name' =>  __('partner.dashboard')]
      ];
      $registeredUsers = User::select('id')->whereHas(
        'roles', function($q){
            $q->where('title', 'user');
        }
      )->get()->count();
      $partnerRegistered = Partners::where('status','!=',Partners::SOFTDELETED)->get()->count();     
      $gift_card_redeemed = Transaction::all()->count();   
    
      return view('/admin/pages/dashboard', [
        'breadcrumbs' => $breadcrumbs,
        'registeredUsers' => $registeredUsers,
        'partnerRegistered' => $partnerRegistered,
        'gift_card_redeemed' => $gift_card_redeemed
      ]);
    }
    // this is for 404 page
    public function notFound(){
      return view('admin.pages.errors.404');
    }
}
