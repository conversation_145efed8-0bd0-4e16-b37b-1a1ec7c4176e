<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\GiftValues;
use App\Http\Requests\Admin\AddEGiftCardRequest;
use Illuminate\Support\Facades\Storage;
use DB;
use DateTime;
use App\Models\Transaction;
use App\Models\Partners as PartnerModel;
use Response;
use App\Services\EncryptDecrypt;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class ManageEGiftCardsController extends Controller
{
    protected $active_menu, $unique_cards, $unique_pins;
   
    public function __construct()
    {
        $this->active_menu = 4;
        $this->unique_cards = array();
        $this->unique_pins = array();
    }

    // this returns the display of all gift codes of all partners
    public function checkGiftCode()
    {
        $stock = GiftValues::join('partners as b','b.partner_code','=','gift_values.partner_code');
        $stock = $stock->where('b.status','!=',PartnerModel::SOFTDELETED)->where('gift_values.id','>',0)->get(); 
        $cc = new EncryptDecrypt;
       
            foreach($stock as $gift_data)
            {
                $partner_code = $gift_data->partner_code;
                $gift_value = $gift_data->gift_value;
                try{
          
                 $gift_code = $cc->codeDecrypt($gift_data->unique_card_code);
                }catch(Exception $e)
                {
                    $gift_code = 'error';
                }
                echo $partner_code.' : '.$gift_value.' : '.$gift_code;
                echo '<br>';
            }
       

       
    }

    // this handles the view of manage cards
    public function index()
    { 
         $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('eGiftCard.home') ], ['link' => "javascript:void(0)", 'name' => __('eGiftCard.eGiftCard')], ['name' =>  __('eGiftCard.manage_eGiftCard')]
          ];
          $partner_data = PartnerModel::where('status','!=',PartnerModel::SOFTDELETED)->orderBy('id','desc')->get();
          return view('/admin/pages/e-gift-cards/manage-e-gift-cards', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'partner_data' => $partner_data
          ]);
    }

    // this handles add screen of gift cards
    public function addBulkEGiftCards($id='')
    {
        $partner_data = PartnerModel::where('id',$id)->first();

       $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('eGiftCard.home') ], ['link' => "/admin/manage-e-gift-cards", 'name' => __('eGiftCard.eGiftCard')], ['name' =>  __('eGiftCard.add-eGiftCard')]
       ];
       
         return view('/admin/pages/e-gift-cards/add-bulk-e-gift-cards', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'partner_id'=>$id,
            'partner'=>$partner_data
          ]);
    }

    // this handles the download script of redeemed gift cards
    public function dowloadRedemptionReport(Request $request)
    {
        $request_data = $request->all();
        
        $transactions = $this->transactionsRecords($request_data);
        $fileName = 'redemption_report.csv';
        $headers = array(
            "Content-type"        => "text/csv",
            "Content-Disposition" => "attachment; filename=$fileName",
            "Pragma"              => "no-cache",
            "Cache-Control"       => "must-revalidate, post-check=0, pre-check=0",
            "Expires"             => "0"
        ); 
        
        $columns = array('Redemption Date', 'Partner Name', 'Denomination/Value','Units','Total Sales');
       
        $callback = function() use($transactions, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);
            $gift_value_price = 0;
            $total_sold = 0;
            $total_sale = 0;
            foreach($transactions as $transactions_data)
            {
                fputcsv($file, array(date('d.m.Y',strtotime($transactions_data->transaction_date)), $transactions_data->partner_name, $transactions_data->gift_value_price,$transactions_data->total_sold,$transactions_data->total_value_price));
                $gift_value_price += $transactions_data->gift_value_price;
                $total_sold += $transactions_data->total_sold;
                $total_sale += $transactions_data->gift_value_price * $transactions_data->total_sold;
            }
            
             
            fputcsv($file, array('Total','', $gift_value_price, $total_sold,$total_sale));
           
            fclose($file);
        };
        return response()->stream($callback, 200, $headers);
       

    }
    // this handles the all transaction records 
    public function transactionsRecords($request_data)
    {
        $transactions = Transaction::join('partners as b','b.id','=','transactions.partner_id')
        ->select('transactions.id','transactions.user_id','transactions.gift_value_price','transactions.created_at','b.partner_name','transactions.created_at as transaction_date',
        DB::raw('DATE_FORMAT(transactions.created_at, "%Y") as redeem_year'), DB::raw('DATE_FORMAT(transactions.created_at, "%m") as redeem_month'), DB::raw('DATE_FORMAT(transactions.created_at, "%d") as redeem_date'),
                DB::raw('count(transactions.gift_value_price) as total_sold'), DB::raw('count(transactions.gift_value_price) * transactions.gift_value_price as total_value_price'));

        if(isset($request_data['partner_name']) && $request_data['partner_name']!='')
        {
        $transactions = $transactions->where('b.partner_name','like','%'.$request_data['partner_name'].'%');
        }
        if(isset($request_data['redemption_value']) && $request_data['redemption_value']!='')
        {
        $transactions = $transactions->where('transactions.gift_value_price','=',$request_data['redemption_value']);
        }
        if(isset($request_data['units']) && $request_data['units']!='')
        {
        $transactions = $transactions->having('total_sold','=',$request_data['units']);
        }
        if(isset($request_data['sales']) && $request_data['sales']!='')
        {
        $transactions = $transactions->having('total_value_price','=',$request_data['sales']);
        }

        if(isset($request_data['redeem_date']) && $request_data['redeem_date']!='')
        {
        $date_explode = explode('to',$request_data['redeem_date']);
        $start_date = date('Y-m-d 00:00:00',strtotime($date_explode[0]));
        $end_date_explode = isset($date_explode[1])?$date_explode[1]:date('Y-m-d 23:59:59');
        $end_date = date('Y-m-d 23:59:59',strtotime($end_date_explode));
        $transactions = $transactions->whereBetween('transactions.created_at',[$start_date,$end_date]);
        }
        return $transactions->groupBy('b.id')->groupBy('transactions.gift_value_price')->groupBy(DB::raw("DATE_FORMAT(transactions.created_at,'%Y-%m-%d')"))->get();  

        
    }
    // this manages of all transaction codes
    public function manageCodes(Request $request)
    {
        $request_data = $request->all();
       
        
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('eGiftCard.home') ], ['link' => "javascript:void(0)", 'name' => __('admin.redemption_by_brand')], ['name' =>  __('admin.manage_redemption_by_brand')]
          ];
          
 
       $transactions = $this->transactionsRecords($request_data);
            
           
          return view('/admin/pages/e-gift-cards/manage-e-gift-code', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => 9,
            'transactions' => $transactions
          ]);
    }

    // this handles the partner stocks
    public function getPartnerStock()
    {
       
        $stock = GiftValues::join('partners as b','b.partner_code','=','gift_values.partner_code')
            ->select(DB::raw('FORMAT(gift_values.gift_value,0) as gift_value'),'b.id','b.partner_name',
            DB::raw('count(CASE 
            WHEN gift_values.status=1  THEN 1
            ELSE NULL
        END) as remaining_code')
                    );
            $stock = $stock->where('b.status','!=',PartnerModel::SOFTDELETED)->groupBy('b.id')->groupBy('gift_values.gift_value')->get(); 
        $array = array('data'=>$stock); 
          echo json_encode($array);
          exit;  
    }

    // this handles the all partners gift codes which is redeemed
    public function getRedemptionTransactions(Request $request)
    {
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('eGiftCard.home') ], ['link' => "javascript:void(0)", 'name' => __('admin.redemption_by_brand')], ['name' =>  __('admin.manage_redemption_by_code')]
          ];
        $transactions =  PartnerModel::join('gift_values as b','b.partner_code','=','partners.partner_code')
                        ->join('users as c','c.id','=','b.user_send');
         $request_data = $request->all();
        if(isset($request_data['partner_name']) && $request_data['partner_name']!='')
        {
             $transactions = $transactions->where('partners.partner_name','like','%'.$request_data['partner_name'].'%');
        }
        if(isset($request_data['redemption_value']) && $request_data['redemption_value']!='')
        {
                $transactions = $transactions->where('b.gift_value','=',$request_data['redemption_value']);
        }
        if(isset($request_data['user_name']) && $request_data['user_name']!='')
        {
            $transactions = $transactions->where(function ($query)  use ($request_data) {
                                        $query->where('c.first_name','like','%'.$request_data['user_name'].'%')
                                            ->orWhere('c.last_name','like','%'.$request_data['user_name'].'%');
                                    });
             
        }
     

        if(isset($request_data['redeem_date']) && $request_data['redeem_date']!='')
        {
                $date_explode = explode('to',$request_data['redeem_date']);
                $start_date = date('Y-m-d 00:00:00',strtotime($date_explode[0]));
                $end_date_explode = isset($date_explode[1])?$date_explode[1]:date('Y-m-d 23:59:59');
                $end_date = date('Y-m-d 23:59:59',strtotime($end_date_explode));
            $transactions = $transactions->whereBetween('b.updated_at',[$start_date,$end_date]);
         }
          $transactions = $transactions->where('b.status',GiftValues::USED)->select('b.updated_at as redemption_date','b.*','c.*','partners.*')->orderBy('b.updated_at','desc')->get();
                        
        
        return view('/admin/pages/e-gift-cards/manage-redemption-transactions', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => 11,
            'transactions' => $transactions
          ]);
    }

    // this downloads the redemption codes transactions data
    public function downloadRedemptionTransactions(Request $request)
    {
        $request_data = $request->all();
        $transactions =  PartnerModel::join('gift_values as b','b.partner_code','=','partners.partner_code')
        ->join('users as c','c.id','=','b.user_send');
        $request_data = $request->all();
        if(isset($request_data['partner_name']) && $request_data['partner_name']!='')
        {
            $transactions = $transactions->where('partners.partner_name','like','%'.$request_data['partner_name'].'%');
        }
        if(isset($request_data['redemption_value']) && $request_data['redemption_value']!='')
        {
             $transactions = $transactions->where('b.gift_value','=',$request_data['redemption_value']);
        }
        if(isset($request_data['user_name']) && $request_data['user_name']!='')
        {
             $transactions = $transactions->where(function ($query)  use ($request_data) {
                                $query->where('c.first_name','like','%'.$request_data['user_name'].'%')
                                    ->orWhere('c.last_name','like','%'.$request_data['user_name'].'%');
                            });

        }
       if(isset($request_data['redeem_date']) && $request_data['redeem_date']!='')
        {
            $date_explode = explode('to',$request_data['redeem_date']);
            $start_date = date('Y-m-d 00:00:00',strtotime($date_explode[0]));  
            
            $end_date_explode = isset($date_explode[1])?$date_explode[1]:date('Y-m-d 23:59:59');
            $end_date = date('Y-m-d 23:59:59',strtotime($end_date_explode));
            $transactions = $transactions->whereBetween('b.updated_at',[$start_date,$end_date]);
        }
        $transactions = $transactions->where('b.status',GiftValues::USED)->select('b.updated_at as redemption_date','b.*','c.*','partners.*')->orderBy('b.updated_at','desc')->get();

        $fileName = 'redemption_transaction_codes.csv';
        $headers = array(
            "Content-type"        => "text/csv",
            "Content-Disposition" => "attachment; filename=$fileName",
            "Pragma"              => "no-cache",
            "Cache-Control"       => "must-revalidate, post-check=0, pre-check=0",
            "Expires"             => "0"
        ); 

        $columns = array('Redemption Date','Partner Name','User Name', 'Denomination/Value', 'Gift Code');
        $callback = function() use($transactions, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);
           
            $cc = new EncryptDecrypt;
            foreach($transactions as $transactions_data)
            {
                $gift_code =  $cc->codeDecrypt(trim($transactions_data->unique_card_code));
                $uname =  isset($transactions_data->first_name)?$transactions_data->first_name.' '.$transactions_data->last_name.' - ':'';
                fputcsv($file, array(date('d.m.Y',strtotime($transactions_data->redemption_date)),$transactions_data->partner_name, $uname.$transactions_data->user_send, $transactions_data->gift_value,$gift_code));
                
            }
             
            
           
            fclose($file);
        };
        return response()->stream($callback, 200, $headers);

        
    }

    // this handles all transactions data by gift value and partner 
    public function getTransactions()
    {
        $transactions = Transaction::join('partners as b','b.id','=','transactions.partner_id')
        ->select('transactions.id','transactions.user_id','transactions.gift_value_price','transactions.created_at','b.partner_name','transactions.created_at as transaction_date2',DB::raw("DATE_FORMAT(transactions.created_at,'%m/%d/%Y') as transaction_date"),
        DB::raw('DATE_FORMAT(transactions.created_at, "%Y") as redeem_year'), DB::raw('DATE_FORMAT(transactions.created_at, "%m") as redeem_month'), DB::raw('DATE_FORMAT(transactions.created_at, "%d") as redeem_date'),
                DB::raw('count(transactions.gift_value_price) as total_sold'), DB::raw('count(transactions.gift_value_price) * transactions.gift_value_price as total_value_price'));
        $transactions = $transactions->groupBy('b.id')->groupBy('transactions.gift_value_price')->groupBy(DB::raw("DATE_FORMAT(transactions.created_at,'%Y-%m-%d')"))->get();  
        $array = array('data'=>$transactions); 
        echo json_encode($array);
        exit;  
 
    }
    // this handles the partner stock of gift cards
    public function partnerStock()
    {
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('eGiftCard.home') ], ['link' => "javascript:void(0)", 'name' => __('eGiftCard.partnerStock')], ['name' =>  __('eGiftCard.managePartnerStock')]
          ];
         
         
            $stock = GiftValues::join('partners as b','b.partner_code','=','gift_values.partner_code')
            ->select('b.id','gift_values.gift_value','b.partner_name',
            DB::raw('count(CASE 
            WHEN gift_values.status=1  THEN 1
            ELSE NULL
        END) as remaining_code')
                    );
            $stock = $stock->where('b.status','!=',PartnerModel::SOFTDELETED)->groupBy('b.id')->groupBy('gift_values.gift_value')->get();  
            return view('/admin/pages/e-gift-cards/manage-partner-stock', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => 10,
            'partner_stock' => $stock
            ]);
    }

    // this function downloads all partner stock data
    public function dowloadPartnerStock(Request $request)
    {
        $request_data = $request->all();
        $partner_name = $request_data['partner_name'];
        $codes_remaining = $request_data['codes_remaining'];
        $gift_value = $request_data['gift_value'];

        $fileName = 'partner_stock.csv';
        $headers = array(
            "Content-type"        => "text/csv",
            "Content-Disposition" => "attachment; filename=$fileName",
            "Pragma"              => "no-cache",
            "Cache-Control"       => "must-revalidate, post-check=0, pre-check=0",
            "Expires"             => "0"
        ); 
        
       
        $stock = GiftValues::join('partners as b','b.partner_code','=','gift_values.partner_code')
        ->select('b.id','gift_values.gift_value','b.partner_name',
        DB::raw('count(CASE 
        WHEN gift_values.status=1  THEN 1
        ELSE NULL
    END) as remaining_code')
                );
                
        if($partner_name!='')
        {
            $stock = $stock->where('b.partner_name','like','%'.$partner_name.'%');
        }
        if($codes_remaining!='')
        {
            $stock = $stock->having('remaining_code',$codes_remaining);
        }
        if($gift_value!='')
        {
            $stock = $stock->where('gift_values.gift_value',$gift_value);
        }
        $stock = $stock->where('b.status',PartnerModel::ACTIVE)->groupBy('b.id')->groupBy('gift_values.gift_value')->get();  

        $columns = array('Partner Name', 'Gift Card Value', 'Remaining Gift Codes');
        $callback = function() use($stock, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);
            $gift_sum = 0;
            $remaining_sum = 0;
            foreach($stock as $stock_data)
            {
                fputcsv($file, array($stock_data->partner_name, $stock_data->gift_value, $stock_data->remaining_code));
                $gift_sum += $stock_data->gift_value;
                $remaining_sum += $stock_data->remaining_code;
            }
             
            fputcsv($file, array('Total', $gift_sum, $remaining_sum));
           
            fclose($file);
        };
        return response()->stream($callback, 200, $headers);
        

    }
    // this function helps to download all partner gift codes
    public function dowloadPartnerEgift(Request $request)
    {
        $request_data = $request->all();
          $partner_id = $request_data['partner_id'];
       
        
        $fileName = 'partner_'.$partner_id.'.csv';
        $headers = array(
            "Content-type"        => "text/csv",
            "Content-Disposition" => "attachment; filename=$fileName",
            "Pragma"              => "no-cache",
            "Cache-Control"       => "must-revalidate, post-check=0, pre-check=0",
            "Expires"             => "0"
        ); 
        
        $columns = array('Brand Code', 'Gift Card Value', 'Unique Gift Card Code', 'Exp Date','Status');
            $isDeleted = (isset($request_data['type']) && $request_data['type']=='deleted') ? true : false;

            $partner_data = GiftValues::when($isDeleted,function($q) {
                return $q->where("status",GiftValues::INACTIVE);
            })->where('partner_code','BNC_'.$partner_id)
            ->get();
           
            $callback = function() use($partner_data, $columns) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $columns);
            foreach($partner_data as $partner)
            {
                $status = ($partner->status == GiftValues::ACTIVE) ? 'Active' : (($partner->status == GiftValues::USED) ? 'Sold' :  'Deleted');
                $expiry_date = date('d-m-Y',strtotime($partner->expiry_date));
                if(strtotime($partner->expiry_date) < strtotime(date("Y-m-d")) && ($partner->status == GiftValues::ACTIVE) ){
                    $status = "Expired";
                }
                fputcsv($file, array($partner->partner_code, $partner->gift_value, $partner->unique_card_code, $expiry_date,$status));
            }
             
         fclose($file);
        };

        
       return response()->stream($callback, 200, $headers);
       
    }

   
    // this function validates the updated csv
    public function checkFileError($importData_arr,$i,$partner_id)
    {
        $error = $errorType='';
        
        
        if($importData_arr[$i][0]==''){
            $error = "Brand Name is required";
        } else if(!is_string($importData_arr[$i][0]) || is_numeric($importData_arr[$i][0])){
            $errorType='Brand Name must be a string';
              
        }
        if($importData_arr[$i][5]==''){
            $error = ($error == '') ? "Expiry Date is required" : $error.", "."Expiry Date is required";
        } else {
           
              $date = $importData_arr[$i][5]; 
              $format = 'd/m/Y';
            $d = DateTime::createFromFormat($format, $date);
            
            $result = ( $d && $d->format($format) === $date );


            $format2 = 'd-m-Y';
            $d2 = DateTime::createFromFormat($format2, $date);
            
            $result2 = ( $d2 && $d2->format($format2) === $date );
            
            if(($result) || ($result2) )
            {
                $result = 'pass';
            }else {
             $errorType= ($errorType == '') ? "Expiry Date must be a date" : $errorType.", "."Expiry Date must be a date";
            } 
        }
        if($importData_arr[$i][3]==''){
            $error = ($error == '') ? "Unique Gift Card Code is required" : $error.", "."Unique Gift Card Code is required";
        }

        if(in_array($importData_arr[$i][3],$this->unique_cards))
        {
            $error = ($error == '') ? "Unique Gift Card Code should be unique" : $error.", "."Unique Gift Card Code should be unique";
        }else if($importData_arr[$i][3]!='')
        {
            $this->unique_cards[] = $importData_arr[$i][3];
        }

        if(in_array($importData_arr[$i][4],$this->unique_pins))
        {
            $error = ($error == '') ? "Unique Gift Card Pin should be unique" : $error.", "."Unique Gift Card Pin should be unique";
        }else if($importData_arr[$i][4]!='')
        {
            $this->unique_pins[] = $importData_arr[$i][4];
        }

        if($importData_arr[$i][2]<=0){
             
            $error = $error.", "."Unique Gift Card Value should be more than 0";
        }
        if($importData_arr[$i][2]==''){
            $error = ($error == '') ? "Gift Card Value is required" : $error.", "."Gift Card Value is required";
        } else if(!is_double($importData_arr[$i][2]) && !is_int($importData_arr[$i][2])){
           
                $errorType= ($errorType == '') ? "Gift Value must be a of correct type" : $errorType.", "."Gift Value must be a of correct type";
            
        }
        if(($importData_arr[$i][1] != '') && (!is_string(($importData_arr[$i][1])) || is_numeric(($importData_arr[$i][1])))){
       
              $errorType= ($errorType == '') ? "Currency must be a string with non numeric value" : $errorType.", "."Currency must be a string with non numeric value";
             
        }

        if($importData_arr[$i][6]==''){
            $error = ($error == '') ? "Brand code is required" : $error.", "."Brand code is required";
        }else if($importData_arr[$i][6]!='BNC_'.$partner_id)
        {
            $error = ($error == '') ? "Brand code is invalid" : $error.", "."Brand code is invalid";
        } 
        
       
         
        return array('error'=>$error,'errorType'=>$errorType);
    }

    // this function uploads the bulk e gift cards
    public function createBulkEGiftCards(AddEGiftCardRequest $request){
        
        $partner_data = PartnerModel::find($request['partner_id']);
        $file = $request->file('eGiftCardCsv');
        $cc = new EncryptDecrypt;
        
        if ($file) {
            $filename = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();  
         
            //Check for file extension and size

             $allowed =  array('csv','xlsx', 'xls');
            if(!in_array($extension,$allowed) ) {
                \Session::flash('allowedType', "The E-Gift Card csv must be a file of type: CSV, xlsx, xls.");
                return back();
            }
            
            //Where uploaded file will be stored on the server 
            $location = 'uploads';  
            // Upload file
            $file->move($location, $filename);
            // In case the uploaded file path is to be stored in the database 
            $filepath = public_path($location . "/" . $filename);
            // Reading file
            $file = fopen($filepath, "r");
            $importData_arr = array(); 
            $i = 0;
            
            $errorCount= 0;
            $importData_arr_error = array();

            //Read the contents of the uploaded file            

            while (($filedata = fgetcsv($file, 1000, ",")) !== FALSE) {
                $num = count($filedata);
                $checkColumnValue = 0;
                
                // Skip first row
                if ($i == 0) {
                    $i++;
                    continue;
                }
                // End Skip first row 
                for ($c = 0; $c < $num; $c++) {
                   
                    $converted = \json_decode($filedata[$c]);
                    $importData_arr[$i][] = $converted ?? $filedata[$c];
                   
                    if($filedata[$c] == ''){
                        $checkColumnValue++;
                    }
                }
                //removing row with all column value empty
                if($checkColumnValue == 7){
                    unset($importData_arr[$i]);
                }
                //End removing row with all column value empty
                if(!empty($importData_arr[$i]))
                {
                    $errorCount++;
                    $error='';
                    $rowNo = $i;
                    $errorType='';

                    $error_data = $this->checkFileError($importData_arr,$i,$request['partner_id']);
                    $error = $error_data['error'];
                    $errorType = $error_data['errorType'];
                    
                    if($error !='' || $errorType!='' ){
                        $importData_arr_error[$i]['row_no'] = $rowNo;
                    $importData_arr_error[$i]['error_message_required'] = $error;
                    
                    $importData_arr_error[$i]['error_message_value_type'] = $errorType;
                        unset($importData_arr[$i]);
                    }  
                }
                $i++;
            }
            fclose($file);
            $j = 0;
            foreach ($importData_arr as $importData) {
                $j++;
                    $unique_code = $cc->codeEncrypt(trim($importData[3]));
                    $date = str_replace('/','-',$importData[5]);
                
                    $expiry_date = date('Y-m-d H:i:s',strtotime($date));
                   
                        $unique_code = $cc->codeEncrypt(trim($importData[3]));
                        GiftValues::create([
                            'partner_code' => $partner_data->partner_code,
                            'currency' => trim($importData[1]),
                            'gift_value' => trim($importData[2]),
                            'unique_card_code' => $unique_code,
                            'unique_card_pin' => trim($importData[4]),
                            'units' => 1,
                            'status' => GiftValues::ACTIVE,
                            'expiry_date' =>$expiry_date,
                        ]);
                
            }
            
            $if_error = 0;
            if(count($importData_arr_error)>0 && count($importData_arr)>0){
                $errorCount = count($importData_arr_error);
                $request->session()->flash('partialSuccess', ['success'=> "$j Records successfully uploaded",'failure' => ["$errorCount Records Not uploaded", $importData_arr_error]]);
                $if_error = 1;
                

            } elseif(count($importData_arr_error)>0){
                $errorCount = count($importData_arr_error);
                $request->session()->flash('failed', ['success'=> "No Records uploaded",'failure' => ["All $errorCount Records Not uploaded", $importData_arr_error]]);
                $if_error = 1;
                
            } 
            else {
                $if_error = 1;
                $request->session()->flash('status', "$j Records successfully uploaded");
                
            }
            if($if_error==1)
            {
                return back();
            }
            
        } else {
            $request->session()->flash('failure', "Records not uploaded");
            return back();
            
        }
    }

    public function removeEgiftCodes(Request $request,$partner_id=null){
        $cc = new EncryptDecrypt;
        $breadcrumbs = [
            ['link' => "/admin/remove-e-gift-codes", 'name' =>   __('admin.remove_coupon_code') ], ['link' => "javascript:void(0)", 'name' => __('admin.remove_coupon_code')], ['name' =>  __('admin.remove_coupon_code')]
          ];
          /**
           * Here we are showing Codes Partner wise
           * E.g we selected canabry partner and clicked on change partner than we'll get that particular partner codes listing
           * E Gift Code Shows selected Partner Unique Coupon Code 
           */
          $partners = PartnerModel::whereIn("status",[PartnerModel::ACTIVE])->orderBy("id","desc")->get();
          
          if(isset($request['partner_id'])){
            $partner_id = $request['partner_id']; 
          }else{
              $partner_id = ($partners->count()) ? $partners[0]->id : null;  
          }
        
          $coupon_codes =  GiftValues::whereHas("partner",function(Builder $query) use($partner_id){
            if($partner_id){
                $query->where("id",$partner_id);
            }
          })->with("partner")->whereIn("status",[GiftValues::ACTIVE])->get()->map(function($data) use ($cc){
            // $data->unique_card_code = $cc->codeDecrypt($data->unique_card_code);
            return $data;
          });

        //   [GiftValues::ACTIVE,GiftValues::INACTIVE]
          return view('/admin/pages/e-gift-cards/coupon-codes', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'coupon_codes_data' => $coupon_codes,
            'partners'=>$partners,
            'partner_id'=>$partner_id,
            'decrypt_object'=>$cc 
          ]);
       
       
    }

    public function deleteEgiftCode(Request $request,GiftValues $gift_code){
        
        $gift_code->status = ($gift_code->status == GiftValues::ACTIVE) ? GiftValues::INACTIVE : GiftValues::ACTIVE;
        $gift_code->save();
        $message = ($gift_code->status == GiftValues::ACTIVE) ? trans("admin.gift_code_activated") : trans("admin.gift_code_inactivated") ;
        $request->session()->flash('created', $message);
        return back();
    }

    public function massGiftCodeOperation(Request $request){
        $selected_codes = explode(',',$request->selected_codes);
        // $status = (!$request->operation) ? GiftValues::ACTIVE : GiftValues::INACTIVE;
        GiftValues::whereIn("id",$selected_codes)->update(["status"=>GiftValues::INACTIVE]);
        // $message = trans("admin.gift_code_inactivated") ;
        $request->session()->flash('created', trans("admin.gift_code_inactivated"));
        return back();
    }

    public function inactiveCodesInBulk(Request $request){
        $partner = PartnerModel::where("id",$request->selected_partner)->first();
        
        GiftValues::where([["status",GiftValues::ACTIVE],["partner_code",$partner->partner_code]])
        ->update(["last_inactive_date"=>date("Y-m-d H:i:s"),"status"=>GiftValues::INACTIVE]);
        $request->session()->flash('created', trans("admin.gift_codes_inactivated"));
        return back();
    }
}
