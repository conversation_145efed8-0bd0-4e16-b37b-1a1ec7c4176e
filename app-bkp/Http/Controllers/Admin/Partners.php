<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Http\Requests\Admin\AddPartnerRequest;
use App\Http\Requests\Admin\EditPartnerRequest;
use App\Models\Partners as PartnerModel;
use Illuminate\Support\Facades\Storage;
use App\Models\PartnerCategory as PartnerCategoryModel;
use App\Models\PartnerImage as PartnerImageModel;
use DB;
use Image;
use Redirect;
class Partners extends BaseController
{
    protected $active_menu;
    public function __construct()
    {
        $this->active_menu = 2;
    }
    // this handles the view screen of all partners
    public function index()
    {
         $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "javascript:void(0)", 'name' => __('partner.partner')], ['name' =>  __('partner.manage_partner')]
          ];
          $partner_data = PartnerModel::where('status','!=',PartnerModel::SOFTDELETED)->orderBy('id','desc')->get();

          return view('/admin/pages/partner/manage-partners', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'partner_data' => $partner_data
          ]);
    }
    // this handles the view screen of add partner
    public function addPartner()
    {
       $categories_partner = PartnerCategoryModel::get();
       $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/manage-partners", 'name' => __('partner.partner')], ['name' =>  __('partner.add-partner')]
       ];
       
         return view('/admin/pages/partner/add-partners', [
            'breadcrumbs' => $breadcrumbs,
            'active_menu' => $this->active_menu,
            'categories_partner' =>$categories_partner
          ]);
    }

    // this handles the view screen of partner
    public function viewPartner($id)
    {  
      $partner = PartnerModel::where('id',$id)->first();

      $partnerImage = PartnerImageModel::select('id','partner_id','file_name')->where('partner_id',$id)->get();
      
      if(!$partnerImage->isEmpty())
      {
        $partnerImageData = $partnerImage;
      }
      else
      {
        $partnerImageData = '';
      }
      
      $breadcrumbs = [
         ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/manage-partners", 'name' => __('partner.partner')], ['name' =>  __('partner.view_partner')]
       ];
      
       return view('/admin/pages/partner/view-partners', [
          'breadcrumbs' => $breadcrumbs,
          'active_menu' => $this->active_menu,
           'partner' => $partner,
           'partnerImageData' => $partnerImageData
        ]);
    }

    // this handles the delete of partner
    public function deletePartner(Request $request)
    {
      $request_data = $request->all();
      
      $partner_id = $request_data['partner_id'];
      PartnerModel::where('id',$partner_id)->update(['status'=>PartnerModel::SOFTDELETED]);
      return true;
    }
    // this handles the edit screen of partner
    public function editPartner($id)
    {    
      $categories_partner = PartnerCategoryModel::get();
      $partner = PartnerModel::where('id',$id)->first();
      $partnerImage = PartnerImageModel::select('id','partner_id','file_name')->where('partner_id',$id)->get();
      
      if(!$partnerImage->isEmpty())
      {
        $partnerImageData = $partnerImage;
      }
      else
      {
        $partnerImageData = '';
      }
      
      $breadcrumbs = [
        ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['link' => "/admin/manage-partners", 'name' => __('partner.partner')], ['name' =>  __('partner.edit_partner')]
      ];
      return view('/admin/pages/partner/edit-partners', [
        'breadcrumbs' => $breadcrumbs,
        'active_menu' => $this->active_menu,
        'categories_partner' =>$categories_partner,
        'partner' => $partner,
        'partnerImageData' => $partnerImageData
      ]);
    }
    // this handles the update process of handles
    public function updatePartner(EditPartnerRequest $request)
    {
      $request_data = $request->all();
      $id = $request_data['id'];
     
      $partner_update = PartnerModel::find($id);
      $oldPartnerLogo = $partner_update->logo;        

      // Implementing Partner Logo Uploading Functionality 

      if ($request->hasFile('logo'))
      {
        $imageLogo = $request->file('logo');
        $logoStoringPath = 'twe-egiftred/partner/'.$id. '/logo';                  
        $logoStorePath = Storage::disk('s3')->put($logoStoringPath, $imageLogo, 'public');
        if($logoStorePath)
        {
          $logo_path = $logoStorePath;
          // Deleting old partner logo after new logo is uploaded during edit partner
          Storage::disk('s3')->delete($oldPartnerLogo);
          // End Deleting old partner logo after new logo is uploaded during edit partner
        } 
        else 
        {
          $logo_path = '';
        }
      }
      else{
        $logo_path = $oldPartnerLogo;
      }
      // End Implementing Partner Logo Uploading Functionality
      $partnerData = [
        'partner_name' => $request->input('partner_name'),
        'partner_category_id' => $request->input('partner_category_id'),
        'logo' => $logo_path,
        'partner_description' => $request->input('partner_description'),
        'reason_well' => $request->input('reason_well'),
        'terms_and_conditions' => $request->input('terms_and_conditions'),
        'redemeption_process' => $request->input('redemeption_process'),
        'slug' => strtolower($request->input('slug')),
        'short_desc' => $request->input('short_desc'),
        'status' => $request->input('status'),
        'meta_keywords'=>$request->input('meta_keywords'),
        'meta_description'=>$request->input('meta_description')
      ];
      $partner_update->update($partnerData);

      if($partner_update)
      {
        if($request->upload_image_edit_delete){
          $imgDel = explode(",", $request->upload_image_edit_delete);

          foreach($imgDel as $value){
            $partnerImageId = $value;
            $partnerImage = PartnerImageModel::find($partnerImageId);
            if($partnerImage)
            {
              Storage::disk('s3')->delete($partnerImage->file_name);
              $partnerImage->delete();
            }
          }
          
        }

        $partner_images_count = PartnerImageModel::where('partner_id',$id)->get()->count();
        
        if($request->hasFile('upload_images'))
        {
           $total_images_count = $partner_images_count + count($request->file('upload_images'));
          if($total_images_count > 4)
          {
            $request->session()->flash('max_files_error', __('partner.max_files_error'));
            return redirect('admin/edit-partner/'.$id);
          }
        }
        
        if($request->hasFile('upload_images'))
        {
          $upload_image_directory = "twe-egiftred/partner/".$id."/images";
          foreach($request->file('upload_images') as $imageFile)
          {
            $partnerUploadImage = $imageFile; 
            $partnerUploadImageStorePath = Storage::disk('s3')->put($upload_image_directory, $partnerUploadImage, 'public');
            if($partnerUploadImageStorePath)
            {
              $PartnerImageModel = new PartnerImageModel;
              $PartnerImageModel->partner_id = $id;
              $PartnerImageModel->file_name = $partnerUploadImageStorePath;
              $PartnerImageModel->save();
            }
          }
        }  
      }

      $request->session()->flash('created', __('partner.update_success'));
      return redirect('admin/manage-partners');
    }

    // this handles the create partner process
    public function createPartner(AddPartnerRequest $request)
    {
      $PartnerModel = new PartnerModel;
      $PartnerModel->partner_name = $request->input('partner_name');
      $PartnerModel->partner_category_id = $request->input('partner_category_id');
      if($request->hasFile('upload_images'))
      {
          $total_images_count =  count($request->file('upload_images'));
          if($total_images_count > 4)
          {
            return Redirect::back()->withErrors(['max_files_error' => __('partner.max_files_error')])->withInput();
          }
      }
      $logo_path = '';
      $PartnerModel->logo = $logo_path;
      $PartnerModel->partner_description = $request->input('partner_description');
      $PartnerModel->reason_well = $request->input('reason_well');
      $PartnerModel->terms_and_conditions = $request->input('terms_and_conditions');
      $PartnerModel->redemeption_process = $request->input('redemeption_process');
      $PartnerModel->slug = strtolower($request->input('slug'));
      $PartnerModel->status = $request->input('status');
      $PartnerModel->short_desc = $request->input('short_desc');
      $PartnerModel->meta_keywords = $request->input('meta_keywords');
      $PartnerModel->meta_description = $request->input('meta_description');
      $PartnerModel->partner_code = 'BNC_'.$PartnerModel->id;
      $partnerSaved = $PartnerModel->save();

      // Implementing Partner Logo Uploading Functionality 

      if ($request->hasFile('logo'))
      {
        $imageLogo = $request->file('logo'); 
        $logoStoringPath = 'twe-egiftred/partner/'.$PartnerModel->id. '/logo';

          

         $logoStorePath = Storage::disk('s3')->put($logoStoringPath, $imageLogo,'public');        

        if($logoStorePath)
        {
          $logo_path = $logoStorePath;
          $UpdatePartner = PartnerModel::find($PartnerModel->id);

          $UpdatePartner->logo = $logo_path;
          $UpdatePartner->save();
        } 
        else 
        {
          $logo_path = '';
        }
      }
      // End Implementing Partner Logo Uploading Functionality 

      $PartnerModel->logo = $logo_path;
      $PartnerModel->partner_description = $request->input('partner_description');
      $PartnerModel->reason_well = $request->input('reason_well');
      $PartnerModel->terms_and_conditions = $request->input('terms_and_conditions');
      $PartnerModel->redemeption_process = $request->input('redemeption_process');
      $PartnerModel->slug = strtolower($request->input('slug'));
      $PartnerModel->status = $request->input('status');
      
      $partnerSaved = $PartnerModel->save();
      $update_partner_code = PartnerModel::find($PartnerModel->id);
      $partnerData = [
        'partner_code' => 'BNC_'.$PartnerModel->id
      ];
      $update_partner_code->update($partnerData);
     
      // Implementing Partner Images Uploading Functionality after Partner is created 

      if($partnerSaved && $request->hasFile('upload_images'))
      {
          $upload_image_directory = "twe-egiftred/partner/".$PartnerModel->id."/images";
          foreach($request->file('upload_images') as $imageFile)
          {
            $partnerUploadImage = $imageFile; 
            $partnerUploadImageStore = Storage::disk('s3')->put($upload_image_directory, $partnerUploadImage,'public');
            if($partnerUploadImageStore)
            {
              $PartnerImageModel = new PartnerImageModel;
              $PartnerImageModel->partner_id = $PartnerModel->id;
              $PartnerImageModel->file_name = $partnerUploadImageStore;
              $PartnerImageModel->save();
            }
          }    
      }
      // End Implementing Partner Images Uploading Functionality after Partner is created 
       
      $request->session()->flash('created', __('partner.added_success'));
      return redirect('admin/manage-partners');
    }

    // this function will delete the partner uploaded images
    public function deletePartnerUploadedImage(Request $request)
    {      
      $request_data = $request->all();
      $partnerImageId = $request_data['partner_image_id'];
      $partnerImage = PartnerImageModel::find($partnerImageId);
      if($partnerImage)
      {
        $result = Storage::disk('s3')->delete($partnerImage->file_name);
        
        $destroy = $partnerImage->delete();
      }
      if(($result && $destroy) )
      {
        return true;
      }
    }
}
