<?php

namespace App\Http\Controllers\Admin;
use Illuminate\Routing\Controller as BaseController;

use Illuminate\Http\Request;
use App\Models\CmsPages as CmsPageModel;
use App\Http\Requests\Admin\CmsPageRequest;
use App\Http\Requests\Admin\HomePageRequest;
use Illuminate\Support\Facades\Storage;
class CmsPages extends BaseController
{
    // this handles the view of terms and condition page
    public function termsAndConditions()
    {
        $cmsdata = CmsPageModel::where('page_type',CmsPageModel::TERMSANDCONDITION)->first();
        
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['name' =>  __('admin.terms_and_conditions')]
          ];
        
        return view('/admin/pages/cmspages/terms-conditions', [
                    'breadcrumbs' => $breadcrumbs,
                    'active_menu' =>6,
                    'cmsdata' => $cmsdata
            ]);
    }

    // this handles the view of privacy policy page
    public function privacyPolicy()
    {
        $cmsdata = CmsPageModel::where('page_type',CmsPageModel::PRIVACYPOLICY)->first();
        
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['name' =>  __('admin.privacy_policy')]
        ];
        
        return view('/admin/pages/cmspages/privacy-policy', [
                    'breadcrumbs' => $breadcrumbs,
                    'active_menu' =>7,
                    'cmsdata' => $cmsdata
            ]);
    }

    // this handles the incoming of update cms page
    public function saveCmsPage(CmsPageRequest $request)
    {
        $request_data = $request->all();
        $page_type = $request_data['page_type'];
        $code = $request_data['code'];
        $meta_keywords = $request_data['meta_keywords'];
        $meta_description = $request_data['meta_description'];
        CmsPageModel::updateOrCreate(
            ['page_type' => $page_type],
            ['code' => $code,'page_type'=>$page_type,'meta_description'=>$meta_description,'meta_keywords'=>$meta_keywords]
        );
        $request->session()->flash('created', __('cmspages.update_success'));
        $redirect_url = 'privacy-policy';
        if($page_type==CmsPageModel::TERMSANDCONDITION)
        {
            $redirect_url = 'terms-and-conditions';
        }
        return redirect('admin/'.$redirect_url);
    }

    // this function will show homepage view 
    public function homePage()
    {
        $desktop = CmsPageModel::where('page_type',CmsPageModel::DESKTOPBANNER)->first();
        $mobile = CmsPageModel::where('page_type',CmsPageModel::MOBILEBANNER)->first();
        
        $breadcrumbs = [
            ['link' => "/admin/dashboard", 'name' =>   __('partner.home') ], ['name' =>  __('admin.privacy_policy')]
        ];
        
        return view('/admin/pages/cmspages/home-page', [
                    'breadcrumbs' => $breadcrumbs,
                    'active_menu' =>8,
                    'desktop' => $desktop,
                    'mobile' => $mobile
            ]);
    }

    // this function handles the incoming of update homepage data
    public function updateHomePage(HomePageRequest $request)
    {
        $homedata = CmsPageModel::where('page_type',CmsPageModel::DESKTOPBANNER)->first();
        

        if ($request->hasFile('home_desktop_image'))
        {
          $imageHome = $request->file('home_desktop_image');
          $homeImagePath = 'twe-egiftred/homepage'; 
          $homeImageName = $request->home_desktop_image->getClientOriginalName();      
          $home_image_extenstion = $imageHome->getClientOriginalExtension();   
          $logoStorePath = Storage::disk('s3')->put($homeImagePath, $imageHome, 'public');
          if($logoStorePath)
            {
                $logo_path = $logoStorePath;
                if(isset($homedata))
                {
                    $oldHomeImage = $homedata->code;  
                     Storage::disk('s3')->delete($oldHomeImage);
                     $home_image_data = [
                        'code' => $logo_path,
                      ];
                     $homedata->update($home_image_data);
                }else
                {
                    $home_image = new CmsPageModel;
                    $home_image->code = $logoStorePath;
                    $home_image->page_type = CmsPageModel::DESKTOPBANNER;
                    $home_image->slug = '';
                    $home_image->save();
                }
          
            }
            
        }

        $homedata = CmsPageModel::where('page_type',CmsPageModel::MOBILEBANNER)->first();
        

        if ($request->hasFile('home_mobile_image'))
        {
           
          $imageHome = $request->file('home_mobile_image');
          $homeImagePath = 'twe-egiftred/homepage'; 
          $homeImageName = $request->home_mobile_image->getClientOriginalName();      
          $home_image_extenstion = $imageHome->getClientOriginalExtension();   
          $logoStorePath = Storage::disk('s3')->put($homeImagePath, $imageHome, 'public');
          if($logoStorePath)
            {
                
                $logo_path = $logoStorePath;
                if(isset($homedata))
                {
                    $oldHomeImage = $homedata->code;  
                     Storage::disk('s3')->delete($oldHomeImage);
                     $home_image_data = [
                        'code' => $logo_path,
                      ];
                     $homedata->update($home_image_data);
                }else
                {
                    $home_image = new CmsPageModel;
                    $home_image->code = $logoStorePath;
                    $home_image->page_type = CmsPageModel::MOBILEBANNER;
                    $home_image->slug = '';
                    $home_image->save();
                }
          
            }
            
        }
        $request->session()->flash('created', __('admin.home_page_success'));
        return redirect('admin/home-page');

    }

}
