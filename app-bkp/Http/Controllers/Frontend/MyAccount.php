<?php
namespace App\Http\Controllers\Frontend;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Http;
use Auth;
use App\Http\Requests\Frontend\AccountDetailsRequest;
use App\Models\User;
use Hash;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Validator;
use App\Models\Transaction;
use App\Models\UserVouchers;
use App\Models\MyBalance;
use DB;
use Newsletter;
use Exception;

use App\Services\EncryptDecrypt;
class MyAccount extends BaseController
{
    //
    // this handles the view screen of my account 
    public function index()
    {
        $user_data = Auth::user();
        return view('/frontend/pages/myaccount', [
            'user' => $user_data
          ]);
    }
    // this handles the statement screen
    public function statements()
    {
        $user_data = Auth::user();
        // gets user total balance
        $my_balance = MyBalance::where('user_id',$user_data->id)->first();
        $my_balance = isset($my_balance->balance)?$my_balance->balance:0;
        // gets all user transactions
        $user_transactions = Transaction::where('user_id',$user_data->id)->get();
        // gets user redeemed vouchers
        $user_vouchers = UserVouchers::where('user_id',$user_data->id)->where('redeemed_amount','!=',NULL)->get();
        $merge_record = array_merge($user_transactions->toArray(),$user_vouchers->toArray());
        // this sorts all transaction time wise
        $sorted_array = $this->sortTransactions($merge_record,$my_balance);
        
        return view('/frontend/pages/statements', [
            'transactions' => $sorted_array,
            'user'=>$user_data,
            'my_balance'=>$my_balance
          ]);
        
      
    }
    // this function sorts the transactions time wise
    public function sortTransactions($merge_record='',$my_balance='')
    {
        $sorted_array = array();
        $cc = new EncryptDecrypt;
       
        foreach($merge_record as $key=> $data) {
            
            $temp_array = array();
            $temp_array['time'] = strtotime($data['created_at']);
            $temp_array['data'] = $data;

           $sorted_array[] = $temp_array;
       }
       $keys = array_column($sorted_array, 'time');

       array_multisort($keys, SORT_ASC, $sorted_array);
       
        $amount_set = 0;
        $new_sort_array = array();
        foreach($sorted_array as $key=> $array_sort)
        {
            $new_data = $array_sort['data'];
            $temp_array = array();
            $temp_array['time'] = strtotime($new_data['created_at']).$key;
            $temp_array['data'] = $new_data;
            $gift_code = '';
            try{
              if(isset($new_data['gift_card_code']))
              {
                $gift_code = $cc->codeDecrypt($new_data['gift_card_code']);
              }
           
            }catch(Exception $e)
            {
                $gift_code = '';
            }
            if($key==0)
            {
                if(isset($new_data['voucher_code']))
                {
                    $temp_array['balance'] = $new_data['redeemed_amount'];
                  
                }else
                {
                    $temp_array['data']['gift_card_code'] =  $gift_code;
                    $temp_array['balance'] = $my_balance - $new_data['gift_value_price'];
                }
                
            }else
            {
                if(isset($new_data['voucher_code']))
                {
                        $temp_array['balance'] = $amount_set + $new_data['redeemed_amount'];
                }else
                {
                    $temp_array['data']['gift_card_code'] =  $gift_code;
                    $temp_array['balance'] = $amount_set - $new_data['gift_value_price'];
                }
            }
                $amount_set = $temp_array['balance'];

            $new_sort_array[] = $temp_array;
        }
        
        $keys = array_column($new_sort_array, 'time');

        array_multisort($keys, SORT_DESC, $new_sort_array);
        return $new_sort_array;
    }

    // this updates the user account details
    public function saveAccountDetails(AccountDetailsRequest $request)
    {
        $userModel = User::find($request->input('user_id'));
        $userModel->first_name = $request->input('first_name')!=''?$request->input('first_name'):'User';
        $userModel->last_name = $request->input('last_name');
        $userModel->email = $request->input('email');
        $userModel->phone = $request->input('phone');
        $userModel->gender = $request->input('gender');
        $userModel->email_news = $request->input('email_news');
       
        if(empty($request->input('email_news')))
        {
            Newsletter::delete($request->input('email'));
        }else if($request->input('email_news')==1 && !Newsletter::isSubscribed($request->input('email')))
        {
          
            $name = $request->input('first_name')!=''?$request->input('first_name'):'User';
             Newsletter::subscribe( $request->input('email'), ['FNAME'=>$name] );
            
        }
        $userModel->save();
        $request->session()->flash('updated', __('myaccount.updated'));
        return redirect('/account-details');
    }

    // this handles the screen of change password screen
    public function changePasswordShow()
    {
        $user_data = Auth::user();
        return view('/frontend/pages/change-password', [
            'user' => $user_data
          ]);
    }

    // this update the user password
    public function changePasswordStore(Request $request)
    {
        
        if($request->isMethod('POST'))
        { 
            $user = Auth::user();
            $rules = array(
                'password' => 'required',
                'newPassword' => 'required|min:8|confirmed|regex:/^(?=.*[a-z])(?=.*[A-Z]).+$/'
            );

            $validator = Validator::make($request->all(), $rules);

            if($validator->fails())
            {
                return Redirect::route('changePasswordFront')->withErrors($validator);
            }
            else
            {
                if (!Hash::check($request->password, $user->password))
                {
                    $errors['password'] = 'Your Current password does not match!';
                    return Redirect::route('changePasswordFront')->withErrors($errors);
                }
                else
                {
                    $user->password = Hash::make($request->newPassword);                    
                    $success = $user->save();
                    $status = 'Password Changed successfully!';
                    $error = 'Password not changed successfully. Please try again later!';

                    // If the password was successfully changed, we will redirect the user back to
                    // the change Password view. If there is an error we can
                    // redirect them back to where they came from with their error message.
                    return $success ? back()->with('status', __($status)) : back()->with('failure', __($error));
                }
            
            }
        }        
    }
}
