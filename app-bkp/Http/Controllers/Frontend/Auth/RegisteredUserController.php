<?php
namespace App\Http\Controllers\Frontend\Auth;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use App\Models\UserVouchers;
use App\Models\VoucherTransactions as VoucherTransactionsModel;
use App\Models\RoleUser;
use Newsletter;
class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('frontend.auth.register');
    }
    /**
     * Handle an incoming registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
      
       
        $request->validate([
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', Rules\Password::defaults()],
            'voucher_code' => ['required', function ($attribute, $value, $fail){
                    $ifRedeemed = UserVouchers::where('voucher_code',$value)->where('redeemed_amount','>',0)->get();
                    $checkVoucher = VoucherTransactionsModel::where('request',$value)->orderBy('id','desc')->get();
                    if(($ifRedeemed->count() > 0) || ($checkVoucher->count()==0) || ($checkVoucher->count() > 0 && $checkVoucher[0]->txn_status=='FAIL'))
                    {
                        $fail(__('message.fail'));
                    }
                    
            }]
        ]);
        
      
    
        $user = User::create([
            'name' => '',
            'email' => $request->email,
            'email_news' => isset($request->email_news)?1:0,
            'password' => Hash::make($request->password),
        ]);
        event(new Registered($user));
        $request_data = $request->all();
        if(isset($request_data['email_news']) && $request_data['email_news']==1 && ! Newsletter::isSubscribed($request->email))
        {
             Newsletter::subscribe($request->email, ['FNAME'=>'User'] );
        }
        $voucher_code = UserVouchers::create([
            'voucher_code' => $request_data['voucher_code'],
            'user_id' => $user->id
        ]);
        $voucher_code = RoleUser::insert([
            'role_id' => 2,
            'user_id' => $user->id
        ]);
        Auth::login($user);
        
        return redirect(RouteServiceProvider::VERIFYEMAIL);
    }
}
