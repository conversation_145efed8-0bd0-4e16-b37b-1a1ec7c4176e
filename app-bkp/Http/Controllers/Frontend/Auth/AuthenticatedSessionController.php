<?php

namespace App\Http\Controllers\Frontend\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Redirect;
use App\Services\Cart as CartService;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('frontend.auth.login');
    }

    /**
     * Handle an incoming authentication request.
     *
     * @param  \App\Http\Requests\Auth\LoginRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LoginRequest $request, CartService $cart)
    {
        
        $request->authenticate();
          $email = $request->get('email'); 
        $user_id = User::where('email',$email)->pluck('id')->first();
      
        $userData = User::find($user_id);
          
        $error = 0;
          if($userData->status==2)
          {
            Auth::logout();
            $request->session()->flash('failToLogin', 'Account is not verified!');    
            $error = 1;    
            
          }else if($userData->status==3)
          {
            Auth::logout();
            $request->session()->flash('failToLogin', 'Account is deactivated by Admin!');        
            $error = 1;    
            
          }else if($userData->roles()->where('title', 'admin')->exists()){
            Auth::logout();
            $request->session()->flash('failToLogin', 'Access Denied!');  
            $error = 1;          
           
        }
        if($error ==1)
        {
          return Redirect::back();
        }

        $request->session()->regenerate();
        $cart->setBasketToLogin();
        return redirect()->intended(RouteServiceProvider::ACCOUNTDETAILS);
    }

   

    /**
     * Destroy an authenticated session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
