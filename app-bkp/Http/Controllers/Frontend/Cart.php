<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Services\Cart as CartService;
use App\Services\Epay as EpayService;
use Redirect;
use App\Models\MyBalance;
use Auth;
use App\Models\Transaction;
use DB;
use App\Models\Cart as CartModel;
use App\Models\GiftValues;

class Cart extends BaseController
{
    // this will add product in cart
    public function addToCart(CartService $cart,Request $request)
    {
        $cart->addToCart($request);    
        $request->session()->flash('cart_added', __('myaccount.cart_updated'));
        return Redirect::back();
    }
    // this will handle the view screen of checkout page
    public function checkoutView(CartService $cart)
    {
        $user_data = Auth::user();
        $basket_data = $cart->getBasket();
        $basket_count = $basket_data->count();
        if($basket_count==0)
        {
            return redirect('/');
        }
        $my_balance = 0;
        if(isset($user_data))
        {
            // this will get balance of user
            $my_balance = MyBalance::where('user_id',$user_data->id)->first();
            $my_balance = isset($my_balance->balance)?$my_balance->balance:0;
        }
        
        return view('/frontend/pages/checkout_view', [
            'basket_data'=>$basket_data,
            'my_balance' => $my_balance
          ]);
    }

    public function sendBasketBalance(CartService $cart)
    {
            $user_data = Auth::user();
            $basket_data = $cart->getBasket();
            $basket_count = $basket_data->count();
            if($basket_count==0)
            {
                return redirect('/');
            }
            $my_balance = 0;
            if(isset($user_data))
            {
                // this will get balance of user
                $my_balance = MyBalance::where('user_id',$user_data->id)->first();
                $my_balance = isset($my_balance->balance)?$my_balance->balance:0;
            }
            return array('my_balance'=>$my_balance,'basket_sum'=>$basket_data);

    }


    // this handles the checkout process of user
    public function checkoutConfirm(Request $request,CartService $cart)
    {
          $user_id = Auth::user()->id;
        $request_data = $request->all();
        $basket_sum = $request_data['basket_sum'];
        $basket_dataTemp = $this->sendBasketBalance($cart);
        if(isset($basket_dataTemp['basket_sum']) && isset($basket_dataTemp['my_balance']))
        {
            $gift_value_basket = 0;
            foreach($basket_dataTemp['basket_sum']->toArray() as $bas_data)
            {
                
                $gift_value_basket += $bas_data['gift_value'];
               
            }   
            
            if($gift_value_basket > $basket_dataTemp['my_balance'])
            {
                echo json_encode(array('status'=>'balance'));
                exit;
            }
        }
        // this gets the basket data
        $basket_data = $cart->getBasket();
        // this verifies the stock of products in cart
        $check_stock = $cart->checkStock($basket_data);
        
        if(!($check_stock))
        {
            echo json_encode(array('status'=>'stock_out'));
            exit;
        }
         
        $last_id = Transaction::orderBy('id','desc')->first();
        $last_id_val = isset($last_id->id)?$last_id->id:0;
        $transaction_id = 'TRN_'.$last_id_val.'_'.time();
       // this will send coupons to user via mail
        $cart->sendGiftCoupons($basket_data,$transaction_id);
       
        MyBalance::where('user_id', $user_id)
                    ->update([
                    'balance'=> DB::raw('balance-'.$basket_sum), 
                    ]);
        CartModel::where('user_id',$user_id)->delete();                    
        echo json_encode(array('status'=>'success','transaction_id'=>$transaction_id));
    }

   
    // this handles the thank you screen
    public function thankYou($transaction_id='')
    {
       $transaction_data = Transaction::where('transaction_id',$transaction_id)->get();
       
        return view('/frontend/pages/thank-you', [
            'transaction_data' => $transaction_data
          ]);
    }
    // this will hadle the remove product from cart functionality
    public function removeCart(CartService $cart,Request $request )
    {
        $request_data = $request->all();
        $cart_id = $request_data['cart_id'];
          $cart->removeCartValue($cart_id);
        echo json_encode(array('status'=>'success'));
    }
    // this will cancle the transactions whis is not saved in our db
    public function autoCancel(EpayService $epay)
    {
            $epay->autoCancelTransaction();
    }
}
