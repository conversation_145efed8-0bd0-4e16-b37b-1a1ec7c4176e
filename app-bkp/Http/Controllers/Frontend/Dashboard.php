<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
class Dashboard extends BaseController
{
    //
    public function index()
    {
     
        $breadcrumbs = [
            ['link' => "/", 'name' => "Home"], ['link' => "javascript:void(0)", 'name' => "Card"], ['name' => "Basic Card"]
          ];
         
          return view('/frontend/pages/card-actions', [
            'breadcrumbs' => $breadcrumbs
          ]);
    }
    // 404 page
    public function notFound(){
      return view('frontend.pages.errors.404');
    }
}
