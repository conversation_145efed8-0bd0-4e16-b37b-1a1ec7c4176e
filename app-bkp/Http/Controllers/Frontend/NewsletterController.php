<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Newsletter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Models\User;

class NewsletterController extends Controller
{
 
    // this handles the save process of user in newsletter 
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'emailSubscribe' => 'required|email',
        ]);
     
        if (!$validator->passes()) {
            return [
                'error'=>'true',
                'msg'=>$validator->errors()->all() 
            ];
        }
        
         
        // check if user is not subscribed
        if ( ! Newsletter::isSubscribed($request->emailSubscribe) ) 
        { 
            $fname = $request->fnameSubscribe;
            (strlen($fname)<1) ? $fname = '' :$fname = $request->fnameSubscribe;
            // this saves the user in newsletter
            if( Newsletter::subscribe($request->emailSubscribe, ['FNAME'=>$fname] )){
                $return =  [
                    'error'=>'false',
                    'msg'=>__('Thanks for subscribing.')
                ];
              
             
            } else {
              
                $return = [
                    'error'=>'true',
                    'msg'=>__('Server error. Please try again later.')  
                ];
               
            }    
        }else
        {
            $return = [
                'error'=>'true',
                'msg'=>__('Sorry! You have already subscribed. ') 
            ];
        }
       
        return $return;
      
            
    }
}
