<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Partners extends Model
{
    use HasFactory;
    protected $fillable = [
        'partner_name',
        'partner_category_id',
        'logo',
        'partner_description',
        'reason_well',
        'terms_and_conditions',
        'redemeption_process',
        'slug',
        'partner_code',
        'status',
        'short_desc',
        'meta_keywords',
        'meta_description'

    ];
    public const ACTIVE = 1;
    public const INACTIVE = 2;
    public const SOFTDELETED = 3;
    public function category()
    {
        return $this->belongsTo(PartnerCategory::class,'partner_category_id');
    }

    public function partnerImages()
    {
        return $this->hasMany(PartnerImage::class,'partner_id');
    }

    public function giftCodes()
    {
        return $this->hasMany(GiftValues::class,'partner_code','partner_code');
    }
}
