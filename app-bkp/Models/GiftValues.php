<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GiftValues extends Model
{
    use HasFactory;
    protected $fillable = [
        'partner_code',
        'currency',
        'gift_value',
        'unique_card_code',
        'unique_card_pin',
        'units',
        'expiry_date',
        "user_send",
        'status',
        'updated_at',
        'last_inactive_date'
    ];
    public const ACTIVE = 1;
    public const USED = 2;
    public const INACTIVE = 3;
    
    public function partner()
    {
        return $this->hasOne(Partners::class,'partner_code','partner_code');
    }
}
