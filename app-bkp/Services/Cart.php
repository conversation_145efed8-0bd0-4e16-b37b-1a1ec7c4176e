<?php

namespace App\Services;

 use Illuminate\Support\Facades\Http;
 
use Auth;
use App\Models\UserVouchers;
use Cookie;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests;
use App\Mail\GiftCouponsNotification;
use Illuminate\Support\Facades\Mail;
use App\Models\Cart as CartModel;
 use App\Models\GiftValues;
 use App\Models\Transaction;
 use App\Services\EncryptDecrypt;
 use App\Models\Partners;
 use URL;
class Cart
{
   // this handles the add production to cart 
    public function addToCart(Request $request)
    {
        $user_data = Auth::user();
        $request_data = $request->all();
       
        if(isset($user_data))
        {
             $user_id = $user_data->id;
             $cookie_id = 0;
        }else
        {
            $user_id = 0;
            $cookie_id = $this->getCookie();  
            
        }
        $gift_value_data = GiftValues::find($request_data['gift_value_id']);
        $gift_value = $gift_value_data->gift_value;
     
        $CartModel= new CartModel;
        $CartModel->user_id = $user_id;
        $CartModel->partner_id = $request_data['partner_id'];
        $CartModel->gift_value_id = $request_data['gift_value_id'];
        $CartModel->gift_value = $gift_value;
         $CartModel->cookie_id = $cookie_id; 
        $CartModel->save();
        return true;
        
      
    }
    // this returns the unique cookie for non loged in user
    public function getCookie(){
    
        $cookie_id = Cookie::forget('usercookieid');
      
        $cookie_has =  Cookie::has('usercookieid');  
        if(!$cookie_has)
        { 
             // set cookie for 2 hours
             $minutes = 120;
             $cookie_id = 'twe_'.time();
             Cookie::queue(Cookie::make('usercookieid', $cookie_id, $minutes));
        } else
        {
            $cookie_id = Cookie::get('usercookieid');
        }
        
        return $cookie_id;
     }

     // this updates the user cart to user account from their cookie
     public function setBasketToLogin()
     {
         $cookie_id = $this->getCookie();  
         $user_data = Auth::user();
         if(isset($user_data))
         {
            $user_id = $user_data->id;
            CartModel::where('cookie_id', '=', $cookie_id)->update(array('cookie_id' => 0,'user_id'=>$user_id));
         }
    }
    // this returns all the products which are in cart
    public function getBasket()
    {
        $cookie_id = $this->getCookie();  
        $user_data = Auth::user();
        
        $basket_data = CartModel::join('partners','partners.id', '=', 'carts.partner_id')->select('carts.*');
        if(isset($user_data))
        {
            $basket_data->where('carts.user_id',$user_data->id);
        }else
        {
            $basket_data->where('carts.cookie_id',$cookie_id);
        }
        $basket_data->where('partners.status',1);
        return $basket_data->get();
    }
    // this removes the product from cart
    public function removeCartValue($cart_id='')
    {
        CartModel::where('id',$cart_id)->delete();
    }

    //this verifies if basket data has valid stock
    public function checkStock($basket_data='')
    {
        $basket_array = $basket_data->toArray();
        $gift_array = array();
        // add total count of available gift products in array
        foreach($basket_array as $basket)
        {
            $temp_array= array();
            $partner_code = 'BNC_'.$basket['partner_id'];
            $gift_count = GiftValues::where('partner_code',$partner_code)->where('status',1)->where('gift_value',$basket['gift_value'])->get(); 
            $count = $gift_count->count();
            $temp_array[$partner_code][$basket['gift_value']] = $count;
            $gift_array[] = $temp_array;
        }
        

        $cart_array = array();
        foreach($basket_array as $basket_val)
        {
            $partner_code = 'BNC_'.$basket_val['partner_id'];
            $cart_array[$partner_code][$basket_val['gift_value']][] = 1;
        }
        
        
        foreach($cart_array as $partner_id=> $cart_data)
        {
            $partner_array = array_column($gift_array,$partner_id);
            foreach($cart_data as $gift_index => $cart_val)
            {
               $partner_count = array_column($partner_array,$gift_index);
               if($partner_count[0] < count($cart_val))
               {
                   // if product available count is less then product in cart count then return false
                    return false;
               }
         
            }
            
            
        }
       

        return true;
        
    }

    public function updateExpiredCodes()
    {
        GiftValues::where('status',1)->where('expiry_date','<',date('Y-m-d 00:00:00'))->where('user_send','=',Null)->update(['status'=>0]);
         
    }
    public function getDuplicateCodes()
    {
        $cc = new EncryptDecrypt;
      
        $giftcodes =  GiftValues::where('status',1)->get();
        
        $codesArray = array();
        $duplicateArray = array();
        foreach($giftcodes->toArray() as $codes)
        {
            
             $gift_card_code_decrypted =  $cc->codeDecrypt($codes['unique_card_code']);
            if(!in_array($gift_card_code_decrypted,$codesArray))
            {
                $codesArray[] = $gift_card_code_decrypted;
            }else
            {
                GiftValues::where('status',1)->where('id',$codes['id'])->update(['status'=>4]);
                $codes['unique_card_code'] = $gift_card_code_decrypted;
                $duplicateArray[] = $codes;
                
            }
        }
        
    }

    // this sends the gift codes to user after checkout
    public function sendGiftCoupons($basket_data='',$transaction_id='')
    {
       
        $gift_mail = array();
        $cc = new EncryptDecrypt;
        foreach($basket_data as $basket)
        {
            
            $partner_code = 'BNC_'.$basket->partner_id;
            $gift_value = $basket->gift_value;
            $gift_data = GiftValues::where('partner_code',$partner_code)->where('status',1)->where('gift_value',$gift_value)->first(); 
            //$gift_data = GiftValues::where([["id",$basket->gift_value_id],["status",1]])->first();
            $gift_card_code = $gift_data->unique_card_code;
            $gift_card_code_decrypted =  $cc->codeDecrypt($gift_card_code);
            $partner_data = Partners::where('id',$basket->partner_id)->first();
            $partner_name = $partner_data->partner_name;
            $terms_conditions = $partner_data->terms_and_conditions;
            $redemeption_process = $partner_data->redemeption_process;
            $partner_slug = URL::to('/partner/'.$partner_data->slug);
            $partner_temp = array();
            $partner_temp['partner_id'] = $basket->partner_id;
            $partner_temp['partner_name'] = $partner_name;
            $partner_temp['redemeption_process'] = $redemeption_process;
            $partner_temp['terms_conditions'] = $terms_conditions;
            $partner_temp['gift_value_price'] = $basket->giftCodesValue->gift_value;
            $partner_temp['gift_card_code'] = $gift_card_code_decrypted;
            $partner_temp['order_id'] = $transaction_id;
            $partner_temp['gift_pin'] = $gift_data->unique_card_pin;
            $partner_temp['partner_url'] =   $partner_slug;
            $gift_mail[$basket->partner_id][] = $partner_temp;

            GiftValues::where('partner_code',$partner_code)->where('status',1)->where('gift_value',$gift_value)->where('unique_card_code',$gift_card_code)->update(['status'=>2,'user_send'=>$basket->user_id]);

            // GiftValues::where([["id",$basket->gift_value_id],["status",1],['unique_card_code',$gift_card_code]])
            //             ->update(['status'=>2,'user_send'=>$basket->user_id]);

            $temp_array = array();
            $temp_array['user_id'] = $basket->user_id;
            $temp_array['partner_id'] = $basket->partner_id;
            $temp_array['gift_value_id'] = $basket->gift_value_id;
            $temp_array['gift_value_price'] = $basket->giftCodesValue->gift_value;
            $temp_array['transaction_id'] = $transaction_id;
            $temp_array['gift_card_code'] = $gift_card_code;
            $temp_array['created_at'] = date('Y-m-d H:i:s');
            $temp_array['updated_at'] = date('Y-m-d H:i:s');
            $bulk_data[] = $temp_array;
        }
        Transaction::insert($bulk_data);

        $user_data = Auth::user();
        $data = array('user_data'=>$user_data,'gift_mail'=>$gift_mail);
        if(config('values.enable_email'))
        {
            //
              Mail::to($user_data->email)->send(new GiftCouponsNotification($data));
        }
    }

    
 
}