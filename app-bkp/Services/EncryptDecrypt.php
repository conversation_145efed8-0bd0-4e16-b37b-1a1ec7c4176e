<?php

namespace App\Services;

 use Illuminate\Support\Facades\Http;
 
 
use App\Http\Requests;
 
 
class EncryptDecrypt
{
     /**
     *  this encrypts the codes 
     *
     * returns encrypt code
     */
     
    public function codeEncrypt($val='')
    {
        $theOtherKey    = "thisisotherkeytoencryptthecodeok"; 
        $newEncrypter   = new \Illuminate\Encryption\Encrypter ($theOtherKey,config('app.cipher'));
        return   $newEncrypter->encrypt($val);
    } 
      /**
     * this decrypts the code from encrypted 
     *
     * returns decrypted code
     */
    // 
    public function codeDecrypt($val='')
    {
        $theOtherKey    = "thisisotherkeytoencryptthecodeok"; 
        $newEncrypter   = new \Illuminate\Encryption\Encrypter ($theOtherKey,config('app.cipher'));
        return $newEncrypter->decrypt($val);
    }
 
}