<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGiftValuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_values', function (Blueprint $table) {
            $table->id();
            $table->string('partner_code',255)->default(null);
            $table->integer('currency')->default(1);
            $table->double('gift_value')->default(null);
            $table->string('unique_card_code')->default(null);
            $table->string('unique_card_pin')->default(null);
            $table->integer('units')->default(null);
            $table->integer('user_send')->default(null);
            $table->integer('status')->default(null);
            $table->dateTime('expiry_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_values');
    }
}
